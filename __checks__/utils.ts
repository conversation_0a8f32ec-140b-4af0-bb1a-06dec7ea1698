import { test } from '@playwright/test'

// Accept .env file environment variables
require('dotenv').config({ override: true })

export enum UserRole {
  Admin = 'ADMIN',
  Member = 'MEMBER',
}

export function getEnvVar(variable: string) {
  const envValue = process.env[variable]

  if (!envValue) {
    throw new Error(`Environment variable ${variable} is not defined`)
  }

  return envValue
}

// Fetches test user's credentials from environment variables
export function getUserCredentials(communityNumber: number, userRole: UserRole, userNumber: number) {
  const userEmail = getEnvVar(`PLAYWRIGHT_COMMUNITY_${communityNumber}_${userRole}_USER_${userNumber}_EMAIL`)
  const userPassword = getEnvVar(`PLAYWRIGHT_COMMUNITY_${communityNumber}_${userRole}_USER_${userNumber}_PASSWORD`)

  return { userEmail, userPassword }
}

// Generates the path to the storageState file where authenticated
// browser state is stored for a particular user
export const getAuthFile = (communityNumber: number, userRole: UserRole, userNumber: number) =>
  `__checks__/playwright/.auth/community_${communityNumber}_${userRole.toLowerCase()}_user_${userNumber}.json`

// Activates a user's storageState file if available
export const activateAuthenticatedUser = (communityNumber: number, userRole: UserRole, userNumber: number) => {
  if (process.env.PLAYWRIGHT_USE_STORAGE_STATE !== 'true') {
    return
  }

  const authFile = getAuthFile(communityNumber, userRole, userNumber)

  test.use({ storageState: authFile })
}

// Generates a community URL based on the community number and path,
// obtaining it's domain, protocol and app domain from environment variables
export function generateCommunityUrl(communityNumber: number, path: string) {
  const communityDomain = getEnvVar(`PLAYWRIGHT_COMMUNITY_${communityNumber}_DOMAIN`)
  const appDomain = getEnvVar('PLAYWRIGHT_APP_DOMAIN')
  const appProtocol = getEnvVar('PLAYWRIGHT_APP_PROTOCOL').toLowerCase()

  return `${appProtocol}://${communityDomain}.${appDomain}${path}`
}

export async function executeUserLogIn(communityNumber: number, page: any, userRole: UserRole, userNumber: number) {
  const { userEmail, userPassword } = getUserCredentials(communityNumber, userRole, userNumber)

  const callbackUrl = generateCommunityUrl(communityNumber, '/community')

  await page.goto(generateCommunityUrl(communityNumber, `/auth/signin?callbackUrl=${encodeURIComponent(callbackUrl)}`))
  await page.getByPlaceholder('Enter your email').fill(userEmail)
  await page.getByPlaceholder('Enter Password').fill(userPassword)
  await page.getByRole('button', { name: 'Login' }).click()

  // Wait for login confirmation
  await page.waitForURL(generateCommunityUrl(communityNumber, '/community'))
}

export async function logUserIn(communityNumber: number, page: any, userRole: UserRole, userNumber: number) {
  // Will only log user in if storage state is not being used
  if (process.env.PLAYWRIGHT_USE_STORAGE_STATE === 'true') {
    return
  }

  await executeUserLogIn(communityNumber, page, userRole, userNumber)
}
