import { test, expect } from '@playwright/test'
import { appDomain, communityDomain, adminUserEmail, adminUserPassword } from '../test-config'

import { formatDatePickerDate } from '../formatting'
import { generateDate } from '../data-generation'
import { UserRole, activateAuthenticatedUser, generateCommunityUrl, logUserIn } from '../utils'

// Test data
const communityNumber = 1
const adminUserNumber = 1
const memberUserNumber = 1
const testId = Date.now()

const publishOptionLabel = 'PublishAll members can view'

const futureEventData = {
  title: `Awesome content release - ${testId}`,
  description: `What you were all expecting is becoming a reality! -${testId}`,
  date: generateDate(1),
  startTime: '10:00 am',
  endTime: '02:00 pm',
  timezone: '(GMT-5:00) Eastern Time',
  location: 'Content Release',
}

const draftEventData = {
  title: `Draft editing process testing event - ${testId}`,
  description: `This event will go through a draft editing process - ${testId}`,
  date: generateDate(1),
  startTime: '10:00 am',
  endTime: '02:00 pm',
  timezone: '(GMT-5:00) Eastern Time',
  location: 'Content Release',
}

const updatedDraftEventData = {
  title: `Draft editing process testing event that has been edited - ${testId}`,
  description: `This event has now been edited as a draft - ${testId}`,
  date: generateDate(2),
  startTime: '10:00 am',
  endTime: '02:00 pm',
  timezone: '(GMT-8:00) Pacific Time',
  location: 'Content Release',
}

test.describe('Events tests: Admin event creation & editing', () => {
  activateAuthenticatedUser(communityNumber, UserRole.Admin, adminUserNumber)

  test('Create and publish "future" event', async ({ page }, testInfo) => {
    testInfo.setTimeout(60_000)

    const { title, description, date, startTime, endTime, timezone, location } = futureEventData

    // Act
    await logUserIn(communityNumber, page, UserRole.Admin, adminUserNumber, testInfo)
    await page.goto(generateCommunityUrl(communityNumber, `/events`))

    await page.getByLabel('create event').click()
    await page.getByPlaceholder('Enter title').fill(title)
    await page.getByPlaceholder('Enter description').fill(description)
    await page.getByPlaceholder('mm/dd/yyyy').click()
    await page.getByPlaceholder('mm/dd/yyyy').press('Home')
    await page.getByPlaceholder('mm/dd/yyyy').fill(formatDatePickerDate(date))
    const startTimeInput = page.getByTestId('start-time').locator('input')
    await startTimeInput.press('Home')
    await startTimeInput.fill(startTime)
    const endTimeInput = page.getByTestId('end-time').locator('input')
    await endTimeInput.press('Home')
    await endTimeInput.fill(endTime)
    await page.getByRole('button', { name: '​', exact: true }).click()
    await page.getByRole('option', { name: timezone }).click()
    await page.getByPlaceholder('Select Location').click()
    await page.getByRole('option', { name: location }).click()
    await page.getByLabel(publishOptionLabel).check()
    await page.getByTestId('event-submit-button').click()

    // Assert
    await expect(page.getByRole('heading', { name: title })).toBeVisible()

    await page.getByTestId('event-card-clickable').filter({ hasText: title }).click()
    await expect(page.getByTestId('event-title')).toHaveText(title)
    await expect(page.getByTestId('event-description')).toHaveText(description)
    // TODO: verify date in a way that doesn't break when daytime saving changes
    // and also validates the date
    // await expect(page.getByTestId("event-date-time")).toContainText(
    //   "7:00 AM To 11:00 AM PDT",
    // );
    await page.getByRole('button', { name: 'Back to Events' }).click()
  })

  test('Create "draft" event', async ({ page }, testInfo) => {
    testInfo.setTimeout(120_000)

    const { title, description, date, startTime, endTime, timezone, location } = draftEventData

    // Act
    await logUserIn(communityNumber, page, UserRole.Admin, adminUserNumber, testInfo)
    await page.goto(generateCommunityUrl(communityNumber, '/events'))

    await page.getByLabel('create event').click()
    await page.getByRole('textbox', { name: 'title' }).fill(title)
    await page.getByPlaceholder('Enter description').fill(description)
    await page.getByPlaceholder('mm/dd/yyyy').click()
    await page.getByPlaceholder('mm/dd/yyyy').press('Home')
    await page.getByPlaceholder('mm/dd/yyyy').fill(formatDatePickerDate(date))
    const startTimeInput = page.getByTestId('start-time').locator('input')
    await startTimeInput.press('Home')
    await startTimeInput.fill(startTime)
    const endTimeInput = page.getByTestId('end-time').locator('input')
    await endTimeInput.press('Home')
    await endTimeInput.fill(endTime)
    await page.getByRole('button', { name: '​', exact: true }).click()
    await page.getByRole('option', { name: timezone }).click()
    await page.getByPlaceholder('Select Location').click()
    await page.getByRole('option', { name: location }).click()

    await page.getByTestId('event-submit-button').click()
    await page.getByTestId('draft-events').click()

    // Assert: event was created as draft
    await page.getByTestId('event-card-clickable').filter({ hasText: title }).click()
    await expect(page.getByTestId('event-title')).toHaveText(title)
    await expect(page.getByTestId('event-description')).toHaveText(description)
    // TODO: verify date in a way that doesn't break when daytime saving changes
    // and also validates the date
    // await expect(page.getByTestId("event-date-time")).toContainText(
    //   "7:00 AM To 11:00 AM PDT",
    // );
    await page.getByRole('button', { name: 'Back to Events' }).click()
    await page.getByTestId('draft-events').click()
  })

  test('Edit "draft" event', async ({ page }, testInfo) => {
    testInfo.setTimeout(120_000)

    const { title, description, date, timezone, location } = updatedDraftEventData

    // Act: edit event draft
    await logUserIn(communityNumber, page, UserRole.Admin, adminUserNumber, testInfo)
    await page.goto(generateCommunityUrl(communityNumber, `/events`))

    await page.getByTestId('draft-events').click()

    await page.getByTestId('event-card').filter({ hasText: draftEventData.title }).hover()
    const moreButton = page
      .getByTestId('event-card')
      .filter({ hasText: draftEventData.title })
      .getByTestId('event-more-button')
    await moreButton.click()
    await page.getByTestId('edit-event-menu-item').click()

    await page.getByRole('textbox', { name: 'title' }).fill(title)
    await page.getByPlaceholder('Enter description').fill(description)
    await page.getByPlaceholder('mm/dd/yyyy').click()
    await page.getByPlaceholder('mm/dd/yyyy').press('Home')
    await page.getByPlaceholder('mm/dd/yyyy').fill(formatDatePickerDate(date))
    await page.getByRole('button', { name: '(GMT-5:00) Eastern Time' }).click()
    await page.getByRole('option', { name: timezone }).click()
    await page.getByPlaceholder('Select Location').click()
    await page.getByRole('option', { name: location }).click()

    await page.getByTestId('event-submit-button').click()

    // Assert: verify edited event details
    await page.getByTestId('draft-events').click()
    await page.getByTestId('event-card-clickable').filter({ hasText: title }).click()
    await expect(page.getByTestId('event-title')).toHaveText(title)
    await expect(page.getByTestId('event-description')).toHaveText(description)
    // TODO: verify date in a way that doesn't break when daytime saving changes
    // and also validates the date
    // await expect(page.getByTestId("event-date-time")).toContainText(
    //   "10:00 AM To 2:00 PM PDT",
    // );
    await page.getByRole('button', { name: 'Back to Events' }).click()
  })
})

test.describe('Events tests: Member', () => {
  activateAuthenticatedUser(communityNumber, UserRole.Member, memberUserNumber)

  test('Verify "future" event as member', async ({ page }, testInfo) => {
    testInfo.setTimeout(60_000)

    const { title, description } = futureEventData

    // Act
    await logUserIn(communityNumber, page, UserRole.Member, memberUserNumber, testInfo)
    await page.goto(generateCommunityUrl(communityNumber, `/events`))

    // Assert
    // Verify that user is a member
    await expect(page.getByTestId('draft-events')).toHaveCount(0)

    await expect(page.getByRole('heading', { name: title })).toBeVisible()

    await page.getByTestId('event-card-clickable').filter({ hasText: title }).click()
    await expect(page.getByTestId('event-title')).toHaveText(title)
    await expect(page.getByTestId('event-description')).toHaveText(description)
    // TODO: verify date in a way that doesn't break when daytime saving changes
    // and also validates the date
    // await expect(page.getByTestId("event-date-time")).toContainText(
    //   "7:00 AM To 11:00 AM PDT",
    // );
  })
})

test.describe('Events tests: Admin event deletion', () => {
  activateAuthenticatedUser(communityNumber, UserRole.Admin, adminUserNumber)

  test('Delete "future" event', async ({ page }, testInfo) => {
    const { title } = futureEventData

    await logUserIn(communityNumber, page, UserRole.Admin, adminUserNumber, testInfo)
    await page.goto(generateCommunityUrl(communityNumber, `/events`))

    // Act: delete event
    await page.getByTestId('event-card').filter({ hasText: title }).hover()
    const moreButton = page.getByTestId('event-card').filter({ hasText: title }).getByTestId('event-more-button')
    await moreButton.click()
    await page.getByTestId('delete-event-menu-item').click()
    await page.getByTestId('warning-proceed-button').click()

    // Assert event no longer available
    await expect(page.getByRole('heading', { name: title })).toHaveCount(0)
  })
})
