{"name": "memberup-mono", "version": "0.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"ensure-babel-config": "[ -f apps/memberup/mod.js ] && mv -n apps/memberup/mod.js apps/memberup/.babelrc.js || true", "clean": "turbo run clean && rm -rf node_modules && rm -rf packages/shared/node_modules && rm -rf packages/database/node_modules && rm -rf apps/memberup-signup/node_modules && rm -rf apps/memberup/node_modules", "rebootstrap": "rm -rf node_modules && rm -rf packages/shared/node_modules && rm -rf packages/database/node_modules && rm -rf apps/memberup-signup/node_modules && rm -rf apps/memberup/node_modules && yarn install", "build-instrumented": "yarn run ensure-babel-config && turbo run build", "build": "turbo run build", "dev": "turbo run dev --no-cache --parallel --continue", "lint": "turbo run ts-lint --no-cache", "lint:fix": "turbo run lint:fix", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "start": "yarn turbo run dev", "start:memberup": "turbo run dev --scope='memberup'", "build:memberup": "turbo run build --scope='memberup' --no-cache", "ts-lint": "turbo run lint", "ts-lint:memberup": "turbo run ts-lint --scope='memberup' --no-cache", "ts-lint:memberup-signup": "turbo run ts-lint --scope='memberup-signup' --no-cache", "run:build:memberup": "turbo run start --scope='memberup'", "start:memberup-signup": "turbo run dev --scope='memberup-signup'", "build:memberup-signup": "turbo run build --scope='memberup-signup' --no-cache", "run:build:memberup-signup": "turbo run start --scope 'memberup-signup'", "prepare": "husky install", "commit": "git-cz", "generate": "turbo run db:generate", "stream-chat-seed": "cli-confirm \"Are you sure you want to run seed? This will clear out the database.\" && turbo run stream-chat-seed --scope='memberup'", "test-jest:ci": "turbo run test-jest:ci --no-cache --parallel --continue", "test-e2e": "npx checkly test", "deploy-e2e": "npx checkly deploy --force", "storybook": "turbo run storybook --scope='memberup'"}, "devDependencies": {"@commitlint/cli": "^17.4.0", "@commitlint/config-conventional": "^17.4.0", "@commitlint/cz-commitlint": "^17.4.0", "checkly": "latest", "@playwright/test": "^1.41.2", "cli-confirm": "^1.0.1", "commitizen": "^4.2.6", "husky": "^8.0.3", "inquirer": "8", "prettier": "latest", "ts-node": "latest", "turbo": "^1.10.16", "typescript": "latest"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "engines": {"node": ">=18.0.0", "yarn": ">=1.22.0", "npm": "please-use-yarn"}, "packageManager": "yarn@1.22.18"}