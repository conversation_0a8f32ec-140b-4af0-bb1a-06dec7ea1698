// TODO: Fix knockObject(s) in this file
import { zonedTimeToUtc } from 'date-fns-tz'

import { formatInTimeZone } from '@memberup/shared/src/libs/date-utils'
import { knockTriggerWorkflow } from '@memberup/shared/src/libs/knock'
import { findMembership } from '@memberup/shared/src/libs/prisma/membership'
import { KNOCK_WORKFLOW_ENUM } from '@memberup/shared/src/types/enum'

const DEFAULT_DOMAIN = process.env.NEXT_PUBLIC_DEFAULT_DOMAIN

export const knockContent = async ({
  id,
  title,
  membership_id,
  user_id,
}: {
  id: string
  title: string
  membership_id: string
  user_id: string
}) => {
  try {
    const membership: any = await findMembership({
      where: { id: membership_id },
    })

    await knockObject({
      workflow_key: KNOCK_WORKFLOW_ENUM.content,
      membership_id,
      user_id,
      collection: membership.slug,
      object_id: 'new-content',
      data: {
        id,
        community_name: membership.name,
        content_title: title,
        content_url: `https://${membership.slug}.${DEFAULT_DOMAIN}/library?id=${id}`,
      },
    })
  } catch (err) {}
}

export const knockEvent = async ({
  id,
  location,
  start_time,
  title,
  time_zone,
  membership_id,
  user_id,
  cancelationKey,
  updated,
}: {
  id: string
  location: string
  start_time: number
  title: string
  time_zone: string
  membership_id: string
  user_id: string
  cancelationKey: string
  updated: boolean
}) => {
  try {
    const startDateTime = start_time * 1000
    const membership: any = await findMembership({
      where: { id: membership_id },
    })

    const startDateTimeTimeZone = zonedTimeToUtc(new Date(startDateTime), time_zone).getTime()
    const currentTimeTimeZone = zonedTimeToUtc(new Date(), time_zone).getTime()
    const delayInSeconds = Math.max(50, Math.floor((startDateTimeTimeZone - currentTimeTimeZone) / 1000 - 24 * 60 * 60))

    const futureEventDate = formatInTimeZone(startDateTime, time_zone, 'MM/dd/yyyy')
    const futureEventTime = formatInTimeZone(startDateTime, time_zone, 'h:mm a zzz')
    await knockObject({
      workflow_key: KNOCK_WORKFLOW_ENUM.event,
      membership_id,
      user_id,
      collection: membership.slug,
      object_id: 'new-event',
      data: {
        id,
        community_name: membership.name,
        event_location: location,
        event_start_date: futureEventDate,
        event_start_time: futureEventTime,
        event_title: updated ? `Event has been updated: ${title}` : `Event starting soon: ${title} 🗓️`,
        community_slug: `https://${membership.host}`,
        event_time_day_reminder: {
          unit: 'seconds',
          value: delayInSeconds,
        },
        event_url: `https://${membership.slug}.${DEFAULT_DOMAIN}/events/${id}`,
        event_updated: updated,
      },
      cancellationKey: `cancel_${cancelationKey}`,
    })
  } catch (err) {}
}

// export const knockFeed = async ({
//   workflow_key,
//   membership_id,
//   user_id,
//   id,
//   permalink,
// }: {
//   workflow_key: KNOCK_WORKFLOW_ENUM
//   membership_id: string
//   user_id: string
//   id: string
//   permalink: string
// }) => {
//   try {
//     let members
//     if (workflow_key === KNOCK_WORKFLOW_ENUM.new_home_content) {
//       const members = await findUsers({
//         select: {
//           id: true,
//         },
//         where: {
//           membership_id,
//           id: { not: user_id },
//         },
//       })
//       if (!members?.docs?.length) {
//         return
//       }
//     }

//     const membership: any = await findMembership({
//       where: { id: membership_id },
//     })
//     const communityUrl = `https://${membership.slug}.${DEFAULT_DOMAIN}/home`

//     if (workflow_key === KNOCK_WORKFLOW_ENUM.new_home_content) {
//       await knockObject({
//         workflow_key: KNOCK_WORKFLOW_ENUM.new_home_content,
//         membership_id,
//         user_id,
//         collection: membership.slug,
//         object_id: 'new-home-content',
//         data: {
//           community_name: membership.name,
//           community_url: communityUrl,
//           id,
//           url: permalink,
//         },
//       })
//     } else {
//       // TODO: This seems obsolete!
//       await knockTriggerWorkflow(
//         membership_id,
//         workflow_key,
//         user_id,
//         members.docs.map((m) => m.id),
//         {
//           community_name: membership.name,
//           community_url: communityUrl,
//           id,
//         }
//       )
//     }
//   } catch (err) {}
// }

export const knockSpark = async ({
  id,
  question,
  membership_id,
  user_id,
}: {
  id: string
  question: string
  membership_id: string
  user_id: string
}) => {
  try {
    const membership: any = await findMembership({
      where: { id: membership_id },
    })

    await knockObject({
      workflow_key: KNOCK_WORKFLOW_ENUM.spark,
      membership_id,
      user_id,
      collection: membership.slug,
      object_id: 'new-spark',
      data: {
        id,
        community_name: membership.name,
        spark_question: question,
        home_url: `https://${membership.slug}.${DEFAULT_DOMAIN}/home`,
      },
    })
  } catch (err) {}
}

export const knockObject = async ({
  workflow_key,
  membership_id,
  user_id,
  collection,
  object_id,
  data,
  cancellationKey,
}: {
  workflow_key: KNOCK_WORKFLOW_ENUM
  membership_id: string
  user_id: string
  collection: string
  object_id: string
  data: any
  cancellationKey?: string
}) => {
  try {
    await knockTriggerWorkflow(
      membership_id,
      workflow_key,
      user_id,
      [{ id: object_id, collection }],
      data,
      cancellationKey,
    )
  } catch (err) {}
}
