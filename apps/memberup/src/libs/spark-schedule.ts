import axios from 'axios'

const MERGENT_API = 'https://api.mergent.co/v2'
const authorization = `Bearer ${process.env.MERGENT_API_KEY}`
const SPARK_CRON_SECRET_KEY = process.env.SPARK_CRON_SECRET_KEY
const DEFAULT_DOMAIN = process.env.NEXT_PUBLIC_DEFAULT_DOMAIN
const SPARK_WEB_HOOK_API = `https://spark_webhook.${
  DEFAULT_DOMAIN === 'localhost:3000' ? 'memberup-dev.com' : DEFAULT_DOMAIN
}/api/webhooks/spark-cron`

export async function sparkCreateSchedule(
  body: string,
  cron: string,
  dtstart: string, // "2019-08-24T14:15:22Z"
  paused?: boolean,
) {
  try {
    const result = await axios.post(
      `${MERGENT_API}/schedules`,
      {
        cron,
        rrule: null,
        dtstart,
        paused: paused || false,
        queue: 'memberup_spark',
        description: null,
        request: {
          url: SPARK_WEB_HOOK_API,
          headers: {
            'Content-Type': 'application/json',
            'X-Api-Key': SPARK_CRON_SECRET_KEY,
          },
          body,
        },
      },
      {
        headers: {
          Authorization: authorization,
        },
      },
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function sparkGetSchedule(id: string) {
  try {
    const result = await axios.get(`${MERGENT_API}/schedules/${id}`, {
      headers: {
        Authorization: authorization,
      },
    })
    return result
  } catch (err: any) {
    throw err
  }
}

export async function sparkUpdateSchedule(
  id: string,
  cron: string,
  dtstart: string, // "2019-08-24T14:15:22Z"
  paused: boolean,
  queue: string,
  description: string,
) {
  try {
    const result = await axios.patch(
      `${MERGENT_API}/schedules/${id}`,
      {
        cron,
        dtstart,
        paused,
        queue,
        description,
      },
      {
        headers: {
          Authorization: authorization,
        },
      },
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function sparkDeleteSchedule(id: string) {
  try {
    const result = await axios.delete(`${MERGENT_API}/schedules/${id}`, {
      headers: {
        Authorization: authorization,
      },
    })
    return result
  } catch (err: any) {
    throw err
  }
}

export async function sparkDeleteTask(id: string) {
  try {
    const result = await axios.delete(`${MERGENT_API}/tasks/${id}`, {
      headers: {
        Authorization: authorization,
      },
    })
    return result
  } catch (err: any) {
    throw err
  }
}
