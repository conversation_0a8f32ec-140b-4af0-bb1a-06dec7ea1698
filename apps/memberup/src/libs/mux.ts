import https from 'https'
import Mux from '@mux/mux-node'

const MUX_ACCESS_TOKEN_ID = process.env.NEXT_PUBLIC_MUX_ACCESS_TOKEN_ID
const MUX_SECRET_KEY = process.env.NEXT_PUBLIC_MUX_SECRET_KEY
const { Video } = new Mux(MUX_ACCESS_TOKEN_ID, MUX_SECRET_KEY)

export const createMuxAsset = async (url: string) => {
  try {
    const result = await Video.Assets.create({
      input: url, // 'https://muxed.s3.amazonaws.com/leds.mp4'
    })
    return result
  } catch (err: any) {
    throw err
  }
}

export const getMuxTranscript = (mediaFileData) => {
  const playbackId = mediaFileData.playback_ids[0].id
  const trackId = mediaFileData.tracks?.find((track) => track.type === 'text').id
  if (!trackId) return Promise.resolve('')

  return new Promise((resolve, reject) => {
    const transcriptUrl = `https://stream.mux.com/${playbackId}/text/${trackId}.txt`
    console.error('transcriptUrl', transcriptUrl)

    https.get(transcriptUrl, (res) => {
      let data = ''

      res.on('data', (chunk) => {
        data += chunk
      })

      res.on('end', () => {
        resolve(data)
      })

      res.on('error', (err) => {
        reject(err)
      })
    })
  })
}

export const deleteMuxAsset = async (assetId: string) => {
  try {
    const result = await Video.Assets.del(assetId)
    return result
  } catch (err: any) {
    throw err
  }
}

export const updateMuxAssetMasterAccess = async (assetId: string, masterAccess: 'temporary' | 'none') => {
  try {
    const result = await Video.Assets.updateMasterAccess(assetId, { master_access: masterAccess })
    return result
  } catch (err: any) {
    throw err
  }
}

export const getMuxAsset = async (id: string) => {
  try {
    const result = await Video.Assets.get(id)
    return result
  } catch (err: any) {
    throw err
  }
}

export const getMuxAssets = async ({
  limit,
  page,
  live_stream_id,
  upload_id,
}: {
  limit?: number
  page?: number
  live_stream_id?: string
  upload_id?: string
}) => {
  try {
    const result = await Video.Assets.list({
      limit,
      page,
      live_stream_id,
      upload_id,
    })
    return result
  } catch (err: any) {
    throw err
  }
}

export const getMuxUploadUrl = async (
  host: string,
  passthrough?: string,
  createWithTranscriptions?: boolean,
  generateRenditions?: boolean,
) => {
  try {
    const newAssetsSettings: Partial<any> = {
      playback_policy: 'public',
      input: createWithTranscriptions
        ? [
            {
              generated_subtitles: [
                {
                  language_code: 'en',
                  name: 'English CC',
                },
              ],
            },
          ]
        : undefined,
    }

    if (generateRenditions) {
      newAssetsSettings.mp4_support = 'standard'
    }

    if (passthrough) {
      newAssetsSettings.passthrough = passthrough
    }

    const result = await Video.Uploads.create({
      cors_origin: host,
      new_asset_settings: newAssetsSettings,
    })
    console.log('result', result)
    return result
  } catch (err: any) {
    throw err
  }
}

export const getMuxUpload = async (id: string) => {
  try {
    const result = await Video.Uploads.get(id)
    return result
  } catch (err: any) {
    throw err
  }
}
