import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import React, { useMemo } from 'react'

const useStyles = makeStyles((theme) => ({
  root: {
    position: 'relative',
    backgroundColor: theme.palette.text.disabled,
    borderRadius: 6,
    height: 12,
    width: '100%',
  },
  slider: {
    borderRadius: 6,
    height: '100%',
  },
}))

const PrettoSlider: React.FC<{ total: number; value: number }> = ({ total, value }) => {
  const classes = useStyles()
  const percent = useMemo(() => Math.floor((value / (total || 1)) * 100), [total, value])

  return (
    <div className={classes.root}>
      <div className={clsx(classes.slider, 'background-gradient01')} style={{ width: `${percent}%` }}></div>
    </div>
  )
}

export default PrettoSlider
