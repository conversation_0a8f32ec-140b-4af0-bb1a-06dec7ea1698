import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { useInstantSearch } from 'react-instantsearch'

import { useStore } from '@/hooks/useStore'
import SearchEmptyState from '@/memberup/components/algolia/search-empty-state'
import MemberListItem from '@/memberup/components/member/member-list-item'
import { openDialog, setSearchStatus } from '@/memberup/store/features/uiSlice'
import { useAppDispatch } from '@/memberup/store/hooks'
import { ALGOLIA_INDEX_ENUM } from '@/src/libs/algolia'

interface MemberHit {
  objectID: string
  membership_id: string
  first_name: string
  last_name: string
  name: string
  email: string
  image: string
  image_crop_area?: any
  bio?: string
  role: string
  status: string
  username: string
}

interface MemberHitsProps {
  searchResults: {
    hits: MemberHit[]
  }
  displayEmptyResult: boolean
  onClickSearchResultHandler: () => void
}

const MemberHits = ({ searchResults, displayEmptyResult, onClickSearchResultHandler }: MemberHitsProps) => {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const membership = useStore((state) => state.community?.membership)

  const handleExplore = () => {
    dispatch(openDialog({ dialog: 'Search', open: false, props: {} }))
    router.replace(`/${membership.slug}/members`)
  }

  useEffect(() => {
    if (searchResults?.hits?.length) {
      dispatch(setSearchStatus({ searchIndex: ALGOLIA_INDEX_ENUM.MEMBER, hasResults: true }))
    } else {
      dispatch(setSearchStatus({ searchIndex: ALGOLIA_INDEX_ENUM.MEMBER, hasResults: false }))
    }
  }, [searchResults])

  return (
    <>
      {searchResults?.hits?.length ? (
        <div className="flex flex-col space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-black text-sm font-semibold">Members</h2>
            <button
              onClick={handleExplore}
              className="bg-transparent text-xs font-medium text-black-300 hover:text-gray-700"
            >
              View all &nbsp;&gt;
            </button>
          </div>
          <div>
            <ul className="space-y-2">
              {searchResults.hits.map((item) => (
                <li key={item.objectID}>
                  <Link href={`/@${item.username}`} className="block py-2" onClick={onClickSearchResultHandler}>
                    <MemberListItem
                      image={item.image}
                      name={item.name}
                      cropArea={item.image_crop_area}
                      imageSize={72}
                      r1={0.14}
                      r2={0.5}
                      primary={
                        <div className="flex items-center space-x-2">
                          <div>
                            <p className="text-black font-bold">{item.name}</p>
                          </div>
                        </div>
                      }
                      secondary={item.bio ? <p className="text-sm text-gray-600">{item.bio}</p> : null}
                      handleClick={() => {}}
                    />
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      ) : null}
      {!searchResults?.hits?.length && displayEmptyResult && (
        <div className="mt-5">
          <SearchEmptyState />
        </div>
      )}
    </>
  )
}

interface CustomSearchBoxProps {
  displayEmptyResult: boolean
  onClickSearchResultHandler: () => void
}

function CustomSearchBox(props: CustomSearchBoxProps) {
  const { results } = useInstantSearch({
    catchError: true,
  })

  return (
    <MemberHits
      displayEmptyResult={props.displayEmptyResult}
      searchResults={results}
      onClickSearchResultHandler={props.onClickSearchResultHandler}
    />
  )
}

export default CustomSearchBox
