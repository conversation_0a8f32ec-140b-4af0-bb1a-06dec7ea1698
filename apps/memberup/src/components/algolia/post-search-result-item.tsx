import _startCase from 'lodash/startCase'
import Image from 'next/image'
import Link from 'next/link'

import { getDateTimeFromNow } from '@memberup/shared/src/libs/date-utils'
import { useStore } from '@/hooks/useStore'

const PostSearchResultItem = ({ item, onClickSearchResultHandler }) => {
  const title = item.title || ''
  const content = item.text || ''
  const author = item.author_full_name || ''
  const createdAt = item.createdAt
  const feedID = item.objectID
  const permalink = item.permalink || feedID
  const spaceName = item.channel
  const postPicURL = item.post_pic_url
  const formattedContent = content.substring(0, 200) + (content.length > 200 ? '...' : '')
  const membership = useStore((state) => state.community.membership)

  return (
    <Link href={`${membership.slug}/post/${permalink}`} onClick={onClickSearchResultHandler}>
      <div className="flex cursor-pointer flex-col rounded shadow-sm transition-all duration-300 hover:shadow-md">
        <div className="flex justify-between">
          <div className="mr-5 flex flex-col gap-1">
            <h6 className="text-black text-sm font-semibold">{_startCase(title)}</h6>
            <div className="break-words">
              <p className="break-all text-xs text-[#2d2d2d]">{formattedContent}</p>
            </div>
          </div>
          <div>
            {postPicURL ? (
              postPicURL?.includes('giphy.com') ? (
                <div className="bg-black flex h-[45px] w-[45px] items-center justify-center overflow-hidden rounded-[10px]">
                  <iframe
                    src={postPicURL}
                    className="pointer-events-none origin-center scale-[1.4] rounded-[10px]"
                    width="45"
                    height="45"
                    style={{ border: 0 }}
                  ></iframe>
                </div>
              ) : (
                <Image className="rounded-[10px]" src={postPicURL} alt="Post Image" width={45} height={45} />
              )
            ) : null}
          </div>
        </div>
        <p className="mb-1.5 mt-2 text-xs text-[#2F2F2F]">
          <span className="font-medium">{author}</span> posted in <span className="font-medium">{spaceName}</span>{' '}
          {getDateTimeFromNow(createdAt)}
        </p>
      </div>
    </Link>
  )
}

export default PostSearchResultItem
