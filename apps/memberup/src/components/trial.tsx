import InfoIcon from '@mui/icons-material/Info'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { useRouter } from 'next/router'

import useTrial from '@/memberup/components/hooks/use-trial'

const AppTrial = () => {
  const router = useRouter()
  const { isTrial, trialDays } = useTrial()

  if (!isTrial || trialDays <= 0) return null

  return (
    <div className="flex-item" style={{ padding: 12, textAlign: 'center' }}>
      <Grid container spacing={1} alignItems="center">
        <Grid item xs></Grid>
        <Grid item>
          <InfoIcon color="primary" />
        </Grid>
        <Grid item>Your trial period will expire in {trialDays} days.</Grid>
        <Grid item xs></Grid>
      </Grid>
    </div>
  )
}

AppTrial.displayName = 'AppTrial'

export default AppTrial
