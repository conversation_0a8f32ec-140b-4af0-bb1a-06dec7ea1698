import { createSvgIcon } from '@mui/material'

const FacebookIcon = createSvgIcon(
  // @TODO: add back SVG tag when M<PERSON> is updated to latest version
  // <svg
  //    width="24"
  //    height="24"
  //    viewBox="0 0 24 24"
  //    xmlns="http://www.w3.org/2000/svg"
  // >
  <path
    d="M 13.342482,13.627223 V 22 H 9.4963269 V 13.627223 H 6.3010587 v -3.39498 H 9.4963269 V 8.9970438 C 9.4963269,4.4112447 11.412006,2 15.465261,2 c 1.242605,0 1.55325,0.1997045 2.23368,0.3624256 v 3.3579931 c -0.761785,-0.1331389 -0.97629,-0.2071009 -1.767706,-0.2071009 -0.939352,0 -1.4423,0.2662655 -1.900881,0.791416 -0.458582,0.5251506 -0.687872,1.434908 -0.687872,2.7366895 v 1.1982247 h 4.356459 l -1.168594,3.394967 h -3.187865 z"
    fill="currentColor"
    style={{ strokeWidth: 1.22799 }}
  />,
  //</svg>,
  'Facebook',
)

export default FacebookIcon
