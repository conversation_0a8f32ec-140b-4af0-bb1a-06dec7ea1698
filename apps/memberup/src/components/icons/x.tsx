import { createSvgIcon } from '@mui/material'

const XIcon = createSvgIcon(
  // @TODO: add back SVG tag when M<PERSON> is updated to latest version
  // <svg
  //    xmlns="http://www.w3.org/2000/svg"
  //    viewBox="0 0 24 24"
  // >
  <g transform="matrix(0.03333333,0,0,0.03333333,2.005501,2.000001)">
    <path
      fill="currentColor"
      d="M 0,0 H 178.66 C 230.33,73.55 281.92,147.17 333.55,220.74 398.3,147.2 462.91,73.53 527.67,0 H 580.6 C 506.06,84.72 431.65,169.54 357.16,254.3 437.98,369.54 518.82,484.77 599.67,600 H 421.01 C 366.51,522.49 312.21,444.84 257.69,367.35 189.59,444.91 121.48,522.46 53.36,600 H 0.44 C 78.39,511.3 156.32,422.59 234.25,333.87 156.2,222.57 78.12,111.29 0,0.04 V 0 m 72.02,39.07 c 124.72,174.75 249.5,349.46 374.2,524.23 27.11,0.07 54.23,0.03 81.35,0.02 C 402.75,388.57 278.11,213.68 153.17,39.01 c -27.04,0.13 -54.1,0.02 -81.15,0.06 z"
    />
  </g>,
  //</svg>,
  'X',
)

export default XIcon
