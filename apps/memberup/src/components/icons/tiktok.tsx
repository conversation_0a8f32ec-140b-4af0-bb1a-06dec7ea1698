import { createSvgIcon } from '@mui/material'

const Tik<PERSON>okIcon = createSvgIcon(
  // @TODO: add back SVG tag when M<PERSON> is updated to latest version
  // <svg
  //    fill="currentColor"
  //    width="24"
  //    height="24"
  //    viewBox="0 0 24 24"
  //    xmlns="http://www.w3.org/2000/svg"
  // >
  <path
    fill="currentColor"
    d="m 12.43787,2.0166665 c 1.091653,-0.016666 2.174973,-0.00833 3.258293,-0.016666 0.06667,1.274984 0.524993,2.5749677 1.458315,3.4749565 0.933322,0.9249884 2.249972,1.349983 3.533289,1.4916479 V 10.324896 C 19.487782,10.28323 18.279464,10.033233 17.187811,9.5165726 16.712817,9.2999086 16.271156,9.0249121 15.837828,8.7415823 c -0.0083,2.4333027 0.0083,4.8666057 -0.01667,7.2915747 -0.06667,1.166653 -0.449994,2.324971 -1.124986,3.283293 -1.091653,1.59998 -2.983296,2.641633 -4.9249381,2.674966 C 8.5795852,22.058082 7.3879334,21.733086 6.3712795,21.133094 4.6879673,20.141439 3.5046488,18.324795 3.329651,16.37482 3.312985,15.958158 3.304651,15.541497 3.321321,15.133169 3.4713192,13.549855 4.2546427,12.033208 5.4712941,10.999887 6.8546101,9.7999024 8.7879192,9.2249096 10.59623,9.5665719 10.6129,10.79989 10.5629,12.033208 10.5629,13.266525 9.7379103,12.999862 8.7712557,13.074861 8.0462648,13.574855 7.5212714,13.916517 7.1212764,14.441511 6.9129457,15.03317 c -0.1749978,0.424995 -0.1249984,0.891656 -0.1166652,1.34165 0.1999975,1.366649 1.5166477,2.516635 2.9166301,2.391637 0.9333214,-0.0083 1.8249764,-0.549994 2.3083044,-1.34165 0.158331,-0.274997 0.333329,-0.558327 0.341662,-0.883323 0.08333,-1.491648 0.05,-2.974962 0.05833,-4.46661 0.0083,-3.3582914 -0.0083,-6.7082494 0.01667,-10.0582075 z"
    style={{ strokeWidth: 0.833323 }}
  />,
  // </svg>,
  'TikTok',
)

export default TikTokIcon
