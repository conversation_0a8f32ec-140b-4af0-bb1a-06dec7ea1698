import { createSvgIcon } from '@mui/material'

const YouTubeIcon = createSvgIcon(
  // @TODO: add back SVG tag when M<PERSON> is updated to latest version
  // <svg
  //    viewBox="0 0 24 24"
  //    xmlns="http://www.w3.org/2000/svg"
  // >
  <path
    fillRule="evenodd"
    clipRule="evenodd"
    d="M 5.2578593,18.741612 H 18.742214 c 1.804223,0 3.265174,-1.587651 3.257758,-3.55832 V 8.8166719 c 0,-1.962637 -1.453535,-3.5582839 -3.257758,-3.5582839 H 5.2578593 c -1.7969334,0 -3.2578594,1.5876654 -3.2578594,3.5582839 v 6.3666201 c 0,1.962624 1.4536231,3.55832 3.2578594,3.55832 z M 9.8653545,8.6291729 15.537874,11.900243 9.8653545,15.171313 Z"
    fill="currentColor"
    style={{ strokeWidth: 1.25695 }}
  />,
  //</svg>
  'YouTube',
)

export default YouTubeIcon
