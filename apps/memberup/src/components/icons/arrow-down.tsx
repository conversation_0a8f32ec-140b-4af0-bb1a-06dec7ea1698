import { createSvgIcon } from '@mui/material'

const ArrowDownIcon = createSvgIcon(
  // @TODO: add back SVG tag when MUI is updated to latest version
  // <svg
  //    width="24"
  //    height="24"
  //    viewBox="0 0 24 24"
  //    xmlns="http://www.w3.org/2000/svg"
  // >
  <path
    d="m 19.154873,6.6548426 c 0.650833,-0.6508671 1.706167,-0.6508671 2.357001,0 0.650834,0.6508837 0.650834,1.7061511 0,2.3570181 l -8.333355,8.3333213 c -0.650884,0.650834 -1.706151,0.650834 -2.357018,0 L 2.4881628,9.0118607 c -0.6508837,-0.650867 -0.6508837,-1.7061344 0,-2.3570181 0.6508671,-0.6508671 1.7061344,-0.6508671 2.3570181,0 L 12.000002,13.80968 Z"
    fill="currentColor"
    style={{ strokeWidth: 1.66667 }}
  />,
  //</svg>,
  'ArrowDown',
)

export default ArrowDownIcon
