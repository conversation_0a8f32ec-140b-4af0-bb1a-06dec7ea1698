import { createSvgIcon } from '@mui/material'

const LinkedInIcon = createSvgIcon(
  // @TODO: add back SVG tag when MUI is updated to latest version
  // <svg
  //    width="24"
  //    height="24"
  //    viewBox="0 0 24 24"
  //    xmlns="http://www.w3.org/2000/svg"
  // >
  <g transform="matrix(1.2569515,0,0,1.2569515,1.2124105,1.2785864)">
    <path d="M 4.21946,5.80626 H 0.894653 V 16.4089 H 4.21946 Z" fill="currentColor" />
    <path
      d="M 13.2133,5.57643 C 13.0907,5.56111 12.9604,5.55345 12.8302,5.54579 10.9686,5.46918 9.91909,6.57234 9.55137,7.04731 9.45178,7.17755 9.40581,7.25416 9.40581,7.25416 V 5.8369 H 6.22656 v 10.6026 h 3.17925 0.14556 c 0,-1.0802 0,-2.1527 0,-3.2329 0,-0.5822 0,-1.1644 0,-1.7466 0,-0.7202 -0.05362,-1.48624 0.30644,-2.14508 0.30639,-0.55158 0.85799,-0.82737 1.47849,-0.82737 1.8386,0 1.877,1.66245 1.877,1.81565 0,0.0076 0,0.0153 0,0.0153 v 6.167 h 3.3248 V 9.56773 c 0,-2.3672 -1.2028,-3.76147 -3.3248,-3.9913 z"
      fill="currentColor"
    />
    <path
      d="m 2.55712,4.43496 c 1.0662,0 1.93054,-0.86433 1.93054,-1.93054 0,-1.0662 -0.86434,-1.930529 -1.93054,-1.930529 -1.06621,0 -1.930533,0.864329 -1.930533,1.930529 0,1.06621 0.864323,1.93054 1.930533,1.93054 z"
      fill="currentColor"
    />
  </g>,
  //</svg>,
  'Facebook',
)

export default LinkedInIcon
