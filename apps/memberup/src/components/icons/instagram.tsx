import { createSvgIcon } from '@mui/material'

const InstagramIcon = createSvgIcon(
  // @TODO: add back SVG tag when M<PERSON> is updated to latest version
  // <svg
  //    viewBox="0 0 24 24"
  //    xmlns="http://www.w3.org/2000/svg"
  // >
  <path
    fillRule="evenodd"
    clipRule="evenodd"
    d="M 18.657035,21.999973 H 5.3429081 C 3.5030834,21.999973 2,20.496921 2,18.657134 V 5.3429439 C 2,3.5031067 3.5030834,2.000027 5.3429081,2.000027 H 18.657035 C 20.496948,2.000027 22,3.5031067 22,5.3429439 V 18.657134 c 0,1.847957 -1.494882,3.342839 -3.342965,3.342839 z M 12.004164,17.13749 c -1.371664,0 -2.6611868,-0.533823 -3.6303775,-1.503052 -0.9692034,-0.969228 -1.5030772,-2.2587 -1.5030772,-3.630352 0,-1.371664 0.5338738,-2.6611861 1.5030772,-3.6303894 C 9.3429772,7.4045058 10.6325,6.8706194 12.004164,6.8706194 c 1.371663,0 2.661199,0.5338864 3.630427,1.5030772 0.969103,0.9692033 1.503052,2.2587264 1.503052,3.6303894 0,1.371652 -0.533949,2.661124 -1.503052,3.630352 -0.977399,0.969229 -2.258764,1.503052 -3.630427,1.503052 z m 0,-9.1744617 c -2.2258698,0 -4.0410584,1.8069681 -4.0410584,4.0410577 0,2.225845 1.8069807,4.040995 4.0410584,4.040995 2.225919,0 4.04107,-1.806855 4.04107,-4.040995 -0.0082,-2.2258692 -1.815151,-4.0410577 -4.04107,-4.0410577 z m 7.120203,-2.2466467 c 0,0.5443567 -0.441313,0.9856441 -0.985695,0.9856441 -0.544256,0 -0.985569,-0.4412874 -0.985569,-0.9856441 0,-0.5443568 0.441313,-0.9856442 0.985569,-0.9856442 0.544382,0 0.985695,0.4412874 0.985695,0.9856442 z"
    fill="currentColor"
    style={{ strokeWidth: 1.25694 }}
  />,
  //</svg>
  'Instagram',
)

export default InstagramIcon
