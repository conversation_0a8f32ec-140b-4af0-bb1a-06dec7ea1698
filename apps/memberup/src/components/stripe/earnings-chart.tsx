import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import { addDays, subDays } from 'date-fns'
import _cloneDeep from 'lodash/cloneDeep'
import _groupBy from 'lodash/groupBy'
import _property from 'lodash/property'
import _sumBy from 'lodash/sumBy'
import React, { useEffect, useLayoutEffect, useMemo, useState } from 'react'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { getStripeBalanceTransactionsApi } from '@memberup/shared/src/services/apis/stripe.api'
import { STRIPE_BALANCE_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { IStripeAccount } from '@memberup/shared/src/types/interfaces'
import { selectMembershipStripeConnectAccountSettings } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

// import {
//   Area,
//   CartesianGrid,
//   ComposedChart,
//   Line,
//   ResponsiveContainer,
//   Tooltip,
//   XAxis,
//   YAxis,
// } from 'recharts'

// const convertDate = (seconds) => formatDate(seconds)
// const convertValue = (value) => `$${value}`

const useStyles = makeStyles((theme) => ({
  root: {
    position: 'relative',
    // minHeight: 690,
    width: '100%',
  },
  content: {
    padding: 32,
    width: '100%',
  },
  itemCard: {
    position: 'relative',
    borderRadius: 12,
    height: 154,
    padding: 24,
  },
  itemCardAction: {
    position: 'absolute',
    bottom: 24,
  },
  noStripeAccount: {
    position: 'absolute',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    left: 0,
    top: 0,
  },
  tooltip: {
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#fff',
    color: '#000',
  },
}))

const CustomTooltip = (props: { active?: boolean; payload?: any; label?: string }) => {
  const classes = useStyles()
  if (props?.active) {
    return (
      <div className={classes.tooltip}>
        <div>{props.label}</div>
        <br />
        <div>${props.payload[0].value}</div>
      </div>
    )
  }

  return null
}

const EarningsChart: React.FC<{ connectedStripeAccount: IStripeAccount }> = ({ connectedStripeAccount }) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const membershipStripeConnectAccount = useAppSelector((state) => selectMembershipStripeConnectAccountSettings(state))
  const [requestPayouts, setRequestPayouts] = useState(false)
  const [dateRange, setDateRange] = useState({
    startDate: 0,
    endDate: 0,
  })
  const [totalAmounts, setTotalAmounts] = useState({
    current: 0,
    prev: 0,
  })
  const [chartData, setChartData] = useState<{ date: string; value: number }[]>([])
  const [payouts, setPayouts] = useState({
    data: [],
    hasMore: false,
    startingAfter: null,
  })

  const fetchMoreEarning = async (startingAfter?: string) => {
    try {
      if (requestPayouts) return
      setRequestPayouts(true)
      getStripeBalanceTransactionsApi(true, {
        accountId: connectedStripeAccount?.id,
        type: STRIPE_BALANCE_TYPE_ENUM.charge,
        limit: 6,
        starting_after: startingAfter,
      }) //   dateRange.startDate, dateRange.endDate,
        .then((res) => {
          if (res.data.success) {
            const temp = (res.data.data.data || []).map((d) => {
              d.created = formatDate({ date: d.created * 1000, format: 'yyyy/MM/dd' })
              d.available_on = formatDate({ date: d.available_on * 1000, format: 'yyyy/MM/dd' })
              return d
            })
            const length = temp.length

            if (length) {
              const tempGroup = _groupBy(temp, _property('created'))
              const clonedChartData = _cloneDeep(chartData)
              let tempAmount = totalAmounts.current

              Object.keys(tempGroup).forEach((k) => {
                const tempIndex = clonedChartData.findIndex((cd) => cd.date === k)

                if (tempIndex >= 0) {
                  const net = _sumBy(tempGroup[k], 'net')
                  tempAmount += net
                  clonedChartData[tempIndex].value += net
                }
              })
              setTotalAmounts({
                ...totalAmounts,
                current: tempAmount,
              })
              setChartData(clonedChartData)
            }

            setPayouts((prevValue) => ({
              data: startingAfter ? temp : prevValue.data.concat(temp),
              hasMore: res.data.data.has_more,
              startingAfter: length ? temp[length - 1].id : null,
            }))
          }
        })
        .finally(() => {
          if (!mountedRef.current) return
          setRequestPayouts(false)
        })
    } catch (err: any) {
      if (!mountedRef.current) return
      setRequestPayouts(false)
    }
  }

  useLayoutEffect(() => {
    const endDate = new Date() //'2021-11-11'
    const startDate = subDays(endDate, 30)
    setDateRange({
      startDate: startDate.getTime() / 1000,
      endDate: endDate.getTime() / 1000,
    })

    const temp: { date: string; value: number }[] = []
    for (let i = 0; i < 30; i++) {
      temp.push({
        date: formatDate({ date: addDays(startDate, i), format: 'YYYY/MM/dd' }),
        value: 0,
      })
    }
    setChartData(temp)
  }, [])

  useEffect(() => {
    if (!connectedStripeAccount || dateRange.startDate === 0) return
    fetchMoreEarning()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [connectedStripeAccount, dateRange])

  const renderDateRange = useMemo(() => {
    if (chartData.length > 2) {
      return (
        <Typography color="text.disabled" variant="body2">
          <span>{formatDate({ date: new Date(chartData[0].date), format: 'LLLL dd, yyyy' })}</span>
          &nbsp;-&nbsp;
          <span>
            {formatDate({
              date: new Date(chartData[chartData.length - 1].date),
              format: 'LLLL dd, yyyy',
            })}
          </span>
        </Typography>
      )
    }
    return null
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chartData.length])

  return <></>
}

export default EarningsChart
