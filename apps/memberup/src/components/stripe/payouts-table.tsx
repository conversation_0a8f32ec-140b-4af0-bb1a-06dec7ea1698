import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { numberToCurrency } from '@memberup/shared/src/libs/numeric-utils'
import { getStripePayoutsApi } from '@memberup/shared/src/services/apis/stripe.api'
import { STRIPE_BALANCE_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { IStripeAccount } from '@memberup/shared/src/types/interfaces'
import Button from '@mui/material/Button'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import { makeStyles } from '@mui/styles'
import React, { useEffect, useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'

const useStyles = makeStyles((theme) => ({
  root: {
    overflow: 'auto',
    maxHeight: 360,
    '& .MuiTableCell-root': {
      color: theme.palette.text.disabled,
    },
  },
}))

const PayoutsTable: React.FC<{ connectedStripeAccount: IStripeAccount }> = ({
  connectedStripeAccount,
}) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const [requestPayouts, setRequestPayouts] = useState(false)
  const [allView, setAllView] = useState(false)
  const [dateRange, setDateRange] = useState({
    startDate: 0,
    endDate: 0,
  })
  const [payouts, setPayouts] = useState({
    data: [],
    hasMore: false,
    startingAfter: null,
  })

  const fetchMorePayouts = async (startingAfter?: string) => {
    try {
      if (requestPayouts) return
      setRequestPayouts(true)
      getStripePayoutsApi(true, {
        accountId: connectedStripeAccount?.id,
        type: STRIPE_BALANCE_TYPE_ENUM.charge,
        limit: 6,
        created_gte: dateRange.startDate,
        created_lt: dateRange.endDate,
        starting_after: startingAfter,
      })
        .then((res) => {
          if (res.data.success) {
            setPayouts((prevValue) => ({
              data: startingAfter ? res.data.data.data : prevValue.data.concat(res.data.data.data),
              hasMore: res.data.data.has_more,
              startingAfter: res.data.data.data.length
                ? res.data.data.data[res.data.data.data.length - 1].id
                : null,
            }))
          }
        })
        .finally(() => {
          if (!mountedRef.current) return
          setRequestPayouts(false)
        })
    } catch (err: any) {
      if (!mountedRef.current) return
      setRequestPayouts(false)
    }
  }

  useEffect(() => {
    const now = new Date()
    const month = now.getMonth()
    const year = now.getFullYear()
    setDateRange({
      startDate: new Date(year, month, 1).getTime() / 1000,
      endDate:
        new Date(month === 11 ? year + 1 : year, month === 11 ? 0 : month + 1, 1).getTime() / 1000,
    })
  }, [])

  useEffect(() => {
    if (!connectedStripeAccount || !dateRange.startDate) return
    fetchMorePayouts()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [connectedStripeAccount, dateRange])

  return (
    <div className={classes.root} id="payouts">
      <InfiniteScroll
        dataLength={payouts.data.length}
        next={() => fetchMorePayouts(payouts.startingAfter)}
        hasMore={payouts.hasMore && allView}
        loader={<div className="text-center">Loading...</div>}
        scrollableTarget="profile-dialog-content"
      >
        <TableContainer>
          <Table stickyHeader aria-label="sticky table">
            <TableHead>
              <TableRow>
                <TableCell>Amount</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Bank Account</TableCell>
                <TableCell>ID</TableCell>
                <TableCell>Date Initiated</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {payouts.data.map((row, index) => (
                <TableRow key={`row-${index}`} hover role="checkbox" tabIndex={-1}>
                  <TableCell>
                    <b className="color01">{numberToCurrency(row.amount)}</b>
                  </TableCell>
                  <TableCell>
                    <div className="status round-small color01 background-color11">
                      {row.status}
                    </div>
                  </TableCell>
                  <TableCell></TableCell>
                  <TableCell>{row.id}</TableCell>
                  <TableCell>
                    {formatDate({ date: row.created, format: 'MMM dd yyyy, K:mm aa z' })}
                  </TableCell>
                </TableRow>
              ))}
              {!requestPayouts && !payouts.data.length && (
                <TableRow>
                  <TableCell className="text-center" colSpan={5}>
                    No Payouts
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </InfiniteScroll>
      {payouts.hasMore && !allView && (
        <>
          <br />
          <div className="text-center">
            <Button
              className="no-padding"
              variant="text"
              color="primary"
              onClick={() => setAllView(true)}
            >
              View all payouts
            </Button>
          </div>
        </>
      )}
    </div>
  )
}

export default PayoutsTable
