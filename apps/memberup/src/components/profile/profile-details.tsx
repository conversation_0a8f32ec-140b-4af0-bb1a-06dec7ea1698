import { <PERSON>, Button, Divider, Grid, Typography } from '@mui/material'
import { useEffect, useMemo, useState } from 'react'

import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { formatDate, formatLastActiveDate } from '@memberup/shared/src/libs/date-utils'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { IUser, IUserProfile } from '@memberup/shared/src/types/interfaces'
import CircledProfileImage from '@/memberup/components/common/circled-profile-image'
import MBTIBadge from '@/memberup/components/common/mbti-badge'
import SocialLinks from '@/memberup/components/common/social-links'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import SVGBrain from '@/memberup/components/svgs/brain'
import SVGCalendar2 from '@/memberup/components/svgs/calendar-2'
import SVGClock2 from '@/memberup/components/svgs/clock-2'
import SVGLocation from '@/memberup/components/svgs/location'
import { getStreamChatClient } from '@/memberup/libs/getstream'

const Counter = ({ number, label }: { number: number; label: string }) => {
  const { isDarkTheme } = useAppTheme()

  return (
    <Box>
      <Typography
        sx={{
          fontSize: '16px',
          lineHeight: '32px',
          color: isDarkTheme ? 'var(--body-copy)' : 'var(--font-light-ui-gray)',
        }}
        className="font-family-graphik-bold"
      >
        {number}
      </Typography>
      <Typography
        sx={{
          fontSize: '12px',
          lineHeight: '16px',
          color: isDarkTheme ? 'var(--ui-light-800)' : 'var(--font-light-ui-gray)',
        }}
      >
        {label}
      </Typography>
    </Box>
  )
}

export default function ProfileDetails({ profile, user }: { profile: IUserProfile; user: IUser }) {
  const [chatUserInfo, setChatUserInfo] = useState<any>(null)
  const { website, facebook, instagram, youtube, x, tiktok } = profile?.social || {}

  const { isDarkTheme, theme } = useAppTheme()
  const client = getStreamChatClient()

  useEffect(() => {
    if (!user) return
    ;(async () => {
      const response = await client.queryUsers({
        id: user.id,
      })

      setChatUserInfo(response.users[0])
    })()
  }, [user])

  const { chatButtonColor, listItemColor, textColor } = useMemo(
    () => ({
      chatButtonColor: isDarkTheme ? 'var(--black-1100)' : 'var(--button-focus)',
      listItemColor: isDarkTheme ? 'var(--ui-dark-800)' : 'var(--font-light-ui-gray)',
      textColor: isDarkTheme ? 'var(--body-copy)' : 'var(--font-light-ui-black)',
    }),
    [isDarkTheme],
  )

  const lastActive = useMemo(() => {
    const lastActive = chatUserInfo?.last_active

    if (!lastActive) return null

    return formatLastActiveDate(lastActive)
  }, [chatUserInfo])

  const { dateJoined, fullName } = useMemo(
    () => ({
      dateJoined: formatDate({ date: user.createdAt, format: 'MMM yyyy' }),
      fullName: getFullName(user?.first_name, user?.last_name),
    }),
    [user],
  )

  return (
    <Box
      className="rounded-container background-d-dark-background-3-l-pure-white"
      sx={{
        position: 'relative',
        width: { xs: '100%', sm: '328px' },
        mb: {
          xs: '24px',
          sm: '0',
        },
        '& .circled-profile-picture': {
          position: 'absolute',
          left: '50%',
          marginLeft: '-70px',
          marginTop: '-75px',
        },
        '& .section-container': {
          paddingLeft: '33px',
          paddingRight: '33px',
        },
      }}
    >
      <Box
        sx={{
          background: "url('/assets/default/images/auth-signin-background.png') center center no-repeat",
          backgroundSize: 'cover',
          height: '181px',
          width: '100%',
          borderRadius: '10px 10px 0 0',
        }}
      />
      <CircledProfileImage imageUrl={profile.image} ranking={7} progress={55} />
      <Box className="section-container" sx={{ pt: '83px', textAlign: 'center', flexGrow: 0 }}>
        <Typography
          className="demi"
          variant="h2"
          sx={{
            fontSize: 18,
            lineHeight: '24px',
            mb: '12px',
            letterSpacing: 'normal',
            color: textColor,
          }}
        >
          {fullName}
        </Typography>
        {profile.location && (
          <Typography
            className="demi"
            sx={{
              fontSize: '12px',
              lineHeight: isDarkTheme ? '20px' : '22px',
              mb: isDarkTheme ? '10px' : '12px',
              letterSpacing: 'normal',
              color: isDarkTheme ? 'var(--ui-dark-800)' : 'var(--font-light-ui-black)',
              '& > .separator': {
                display: 'inline-block',
                margin: '0 3px',
              },
              '& > .location': {
                display: 'inline-block',
                background: isDarkTheme ? 'transparent' : 'var(--ui-dark-300)',
                borderRadius: '8px',
                padding: isDarkTheme ? '0' : '0 6px',
                color: isDarkTheme ? 'var(--ui-dark-800)' : 'var(--ui-light-1000)',
              },
              '& > .location > svg': {
                position: 'relative',
                top: '-1px',
              },
            }}
          >
            @{fullName.toLowerCase().replace(' ', '')} <span className="separator">•</span>{' '}
            <span className="location">
              <SVGLocation /> {profile.location}
            </span>
          </Typography>
        )}
        {profile.bio && (
          <Typography sx={{ lineHeight: '16px', pb: '16px', color: textColor }}>{profile.bio}</Typography>
        )}
        {(website || facebook || instagram || youtube || x || tiktok) && (
          <Box sx={{ pb: '24px', textAlign: 'center' }}>
            <SocialLinks
              website={website}
              facebook={facebook}
              instagram={instagram}
              youtube={youtube}
              x={x}
              tiktok={tiktok}
            />
          </Box>
        )}
      </Box>
      <Divider orientation="horizontal" sx={{ borderColor: 'var(--divider-border-color)' }} />
      <Box className="section-container"></Box>
      <Box
        className="section-container"
        sx={{
          pt: '20px',
          pb: '20px',
          color: isDarkTheme ? 'var(--ui-dark-800)' : 'var(--font-light-ui-gray)',
          '& svg': {
            mr: '8px',
            color: isDarkTheme ? 'var(--ui-dark-1000)' : 'var(--ui-light-1000)',
          },
          '& span': {
            fontSize: '13px',
          },
          '& > div:not(:last-child)': {
            mb: '13px',
          },
        }}
      >
        <Box>
          <SVGClock2 />
          <Box component="span" color={listItemColor}>
            {lastActive ? `Last Active ${lastActive}` : ''}
          </Box>
        </Box>
        <Box>
          <SVGCalendar2 />
          <Box component="span" color={listItemColor}>
            Joined {dateJoined}
          </Box>
        </Box>
        <Box>
          <SVGBrain />
          <MBTIBadge mbtiType={profile.personality_type} />
        </Box>
      </Box>
      <Divider orientation="horizontal" sx={{ borderColor: 'var(--divider-border-color)' }} />
      <Box className="section-container" sx={{ pb: 4, pt: 4 }}>
        <Grid container justifyContent="space-between">
          <Grid item xs="auto">
            <Counter number={405} label="Contributions" />
          </Grid>
          <Grid item xs="auto">
            <Counter number={2296} label="Followers" />
          </Grid>
          <Grid item xs="auto">
            <Counter number={96} label="Following" />
          </Grid>
        </Grid>
      </Box>
      <Box className="section-container" sx={{ pb: '32px', pt: '8px' }}>
        <Button
          sx={{
            width: '100%',
            backgroundColor: adjustRGBA(theme.palette.primary.main, 0.6),
            color: 'var(--pure-white)',
            mb: '16px',
          }}
          variant="contained"
          className="demi round-small text-medium padding-17 no-shadow"
        >
          Follow
        </Button>
        <Button
          sx={{
            width: '100%',
            ml: 0,
            borderColor: chatButtonColor,
            color: isDarkTheme ? 'var(--ui-dark-100)' : 'var(--font-light-ui-gray)',
            '&:hover': {
              backgroundColor: isDarkTheme ? 'var(--black-1100)' : 'var(--button-focus)',
              borderColor: isDarkTheme ? 'var(--black-1100)' : 'var(--button-focus)',
            },
          }}
          variant="outlined"
          className="demi round-small text-medium padding-17"
        >
          Chat
        </Button>
      </Box>
    </Box>
  )
}
