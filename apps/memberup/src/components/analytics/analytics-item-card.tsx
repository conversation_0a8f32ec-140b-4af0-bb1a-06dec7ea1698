import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import React, { useMemo } from 'react'

import { AppPercent } from '@memberup/shared/src/components/common/percent'
import { numberFormat, numberToCurrency } from '@memberup/shared/src/libs/numeric-utils'

const AnalyticsItemCard: React.FC<{
  title: string
  icon: React.ReactNode
  currentValue: number
  lastValue: number
  isCurrency?: boolean
  visibleLastValue?: boolean
}> = ({ title, icon, currentValue, lastValue, isCurrency, visibleLastValue = true }) => {
  const dispCurrentValue = useMemo(() => {
    if (isCurrency) return numberToCurrency(currentValue, 0, 0)
    return numberFormat(currentValue) || '0'
  }, [currentValue, isCurrency])

  const dispLastValue = useMemo(() => {
    if (!visibleLastValue) return null
    if (isCurrency) return numberToCurrency(lastValue, 0, 0)
    return numberFormat(lastValue) || '0'
  }, [lastValue, isCurrency, visibleLastValue])

  return (
    <Card sx={{ borderRadius: 3, padding: '24px' }}>
      <CardContent sx={{ p: 0, pb: '0!important' }}>
        <Grid container>
          <Grid item xs={12}>
            <div className="d-flex align-center color03">
              {icon}
              &nbsp;&nbsp;
              <Typography variant="h6">{title}</Typography>
            </div>
            <br />
            <br />
          </Grid>
          <Grid item xs={12}>
            <Grid container alignItems="center">
              <Grid item xs>
                <Typography variant="h3">{dispCurrentValue}</Typography>
              </Grid>
              {visibleLastValue && (
                <Grid item>
                  <AppPercent currentValue={currentValue} lastValue={lastValue} />
                </Grid>
              )}
            </Grid>
          </Grid>
          {visibleLastValue && (
            <Grid item xs={12}>
              <Typography color="text.disabled" variant="body2">
                from {dispLastValue} last month
              </Typography>
            </Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  )
}

export default AnalyticsItemCard
