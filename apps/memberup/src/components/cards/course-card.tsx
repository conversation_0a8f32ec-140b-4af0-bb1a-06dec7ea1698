import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { DragIndicator } from '@mui/icons-material'
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord'
import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import { Box, Button, CardActions, Divider, IconButton, Menu, MenuItem } from '@mui/material'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { alpha } from '@mui/system'
import { capitalize } from 'lodash'
import { useRouter } from 'next/navigation'

import useCheckUserRole from '../hooks/use-check-user-role'
import SVGCalendar from '../svgs/calendar'
import SVGPerson from '../svgs/person'
import { useStore } from '@/hooks/useStore'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import SVGPreview from '@/memberup/components/svgs/preview'
import SVGTrash from '@/memberup/components/svgs/trash'
import ProgressBarWithAvatar from '@/memberup/components/ui/progress-bar-with-avatar'
import { roundCourseProgress } from '@/memberup/libs/utils'
import { stripHtml } from '@/shared-libs/string-utils'

interface CourseCardProps {
  course: any
  menuOpen: boolean
  activeCard: string
  handleClick: any
  anchorEl: any
  handleClose: any
  handleDeleteCourse: any
  currentUserProfile: any
  theme: any
  onCardClick: any
  membersCount: number
  id: string
}

const imageContainerStyle = {
  width: '100%',
  position: 'relative',
  paddingTop: '51%', // 100 / 1.8 (aspect ratio) = 55.56%
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  overflow: 'hidden',
}

const CourseCard = ({
  course,
  menuOpen,
  activeCard,
  handleClick,
  anchorEl,
  handleClose,
  handleDeleteCourse,
  currentUserProfile,
  theme,
  onCardClick,
  membersCount,
  id,
}: CourseCardProps) => {
  const { isMobile } = useAppTheme()
  const router = useRouter()
  const { isCurrentUserAdmin } = useCheckUserRole()
  const isDarkTheme = useStore((state) => state.ui.isDarkTheme)
  const membership = useStore((state) => state.community.membership)

  const { attributes, listeners, setNodeRef, transform, isDragging } = useSortable({
    id,
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    /* transition, */
    zIndex: isDragging ? '100' : 'auto',
    opacity: isDragging ? 0.3 : 1,
  }

  return (
    <div ref={setNodeRef} style={style}>
      <Card
        sx={{
          flex: 1,
          flexDirection: 'column',
          position: 'relative',
          overflow: 'hidden',
          borderRadius: '12px',
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
          transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
          cursor: 'pointer',
          transform: 'none',
          '& .card-icons': {
            opacity: 0,
            transition: 'opacity 0.3s ease-in-out',
          },
          '&:hover .card-icons': {
            opacity: 1,
          },
        }}
        onClick={onCardClick}
      >
        {isCurrentUserAdmin && !isMobile && (
          <div
            className="card-icons"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              display: 'flex',
              justifyContent: 'space-between',
              padding: '10px',
              opacity: menuOpen && activeCard === course.id ? 1 : undefined,
              zIndex: 999,
            }}
          >
            <IconButton
              {...attributes}
              {...listeners}
              aria-label="drag"
              style={{
                cursor: 'pointer',
                backgroundColor: isDarkTheme ? '#303033' : '#f3f5f5',
                height: '32px',
                width: '32px',
              }}
              sx={{ '&:hover': { backgroundColor: 'inherit' } }}
              onClick={(e) => {
                e.stopPropagation()
              }}
            >
              <DragIndicator sx={{ color: isDarkTheme ? '#8d94a3' : '#585d66' }} />
            </IconButton>
            <IconButton
              aria-label="more"
              aria-controls="long-menu"
              aria-haspopup="true"
              onClick={(e) => {
                e.stopPropagation()
                handleClick(e, course.id)
              }}
              style={{
                cursor: 'pointer',
                backgroundColor: isDarkTheme ? '#17171a' : '#f4f5f5',
                height: '32px',
                width: '32px',
              }}
              sx={{ '&:hover': { backgroundColor: 'inherit' } }}
            >
              <MoreHorizIcon sx={{ color: isDarkTheme ? '#8d94a3' : '#585d66' }} />
            </IconButton>
          </div>
        )}
        <Menu
          id="long-menu"
          anchorEl={anchorEl}
          keepMounted
          open={menuOpen && activeCard === course.id}
          onClose={(e) => handleClose(e, course.id)}
          PaperProps={{
            style: {
              borderRadius: '16px',
              padding: '5px 5px',
              width: '191px',
              backgroundColor: isDarkTheme ? '#000' : '#ffffff',
              color: isDarkTheme ? '#fff' : '#000',
              border: isDarkTheme ? '1px solid #303033' : '1px solid #f3f5f5',
            },
          }}
          sx={{ '& .MuiList-root': { padding: '2px' } }}
        >
          <MenuItem
            sx={{ padding: '10px', borderRadius: '12px' }}
            onClick={(e) => {
              e.stopPropagation()
              router.push(`/${membership.id}/content/course/${course.id}`)
            }}
          >
            <SVGPreview styles={{ color: '#8D94A3', marginRight: '10px' }} />
            <Typography
              sx={{
                color: isDarkTheme ? '#fff' : '#585D66',
                fontFamily: 'Graphik Semibold',
                fontSize: '14px',
                fontWeight: '500',
              }}
            >
              Preview
            </Typography>
          </MenuItem>
          <Divider />
          <MenuItem
            sx={{
              padding: '10px',
              borderRadius: '12px',
            }}
            onClick={(e) => handleDeleteCourse(e, course.id)}
          >
            <SVGTrash
              styles={{
                color: theme.palette.error.main,
                marginRight: '12px',
              }}
            />
            <Typography
              sx={{
                color: theme.palette.error.main,
                fontFamily: 'Graphik Semibold',
                fontSize: '14px',
                fontWeight: '500',
              }}
            >
              Delete course
            </Typography>
          </MenuItem>
        </Menu>
        {course.thumbnail?.url ? (
          <div
            style={{
              ...imageContainerStyle,
              backgroundImage: `url(${course.thumbnail?.croppedImg?.url})`,
            }}
          />
        ) : (
          <div
            style={{
              ...imageContainerStyle,
              backgroundImage: isDarkTheme
                ? 'url(/assets/default/images/dark-card.png)'
                : 'url(/assets/default/images/light-card.png)',
              backgroundColor: theme.palette.primary.main,
            }}
          />
        )}
        <CardContent
          style={{
            padding: '18px 20px 12px 20px',
          }}
        >
          <Typography
            gutterBottom
            variant="h5"
            component="div"
            sx={{
              fontSize: '14px',
              fontFamily: 'Graphik Semibold',
              fontWeight: 500,
              color: isDarkTheme ? 'rgba(255, 255, 255, 0.87)' : 'rgba(0, 0, 0, 0.87)',
              display: '-webkit-box',
              WebkitLineClamp: 1,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {course?.title}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontSize: '13px',
              fontFamily: 'Graphik Regular',
              lineHeight: '20px',
              fontWeight: 500,
              minHeight: '40px',
              marginBottom: '25px',
              overflow: 'hidden',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              color: isDarkTheme ? 'rgb(141, 148, 163)' : 'rgb(88, 93, 102)',
            }}
          >
            {stripHtml(course.description || '')}
          </Typography>
          <div
            style={{
              width: '100%',
              padding: '0px 0px',
            }}
          >
            {isCurrentUserAdmin ? (
              <Box
                sx={{
                  display: 'flex',
                  width: '100%',
                  marginTop: '12px',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor:
                      course.visibility.toLowerCase() === 'published'
                        ? theme.palette.mode === 'dark'
                          ? alpha('#AEE78B', 0.12)
                          : '#e8f6e1'
                        : alpha(theme.palette.warning.main, 0.12),
                    borderRadius: '12px',
                    padding: '4px 12px 3px 12px',
                  }}
                >
                  <FiberManualRecordIcon
                    sx={{
                      fontSize: '10px',
                      color:
                        course.visibility.toLowerCase() === 'published'
                          ? theme.palette.mode === 'dark'
                            ? '#AEE78B'
                            : '#48b705'
                          : theme.palette.warning.main,
                      marginRight: '4px',
                    }}
                  />
                  <Typography
                    sx={{
                      fontSize: '12px',
                      fontFamily: 'Graphik Semibold',
                      fontWeight: 500,
                      color:
                        course.visibility.toLowerCase() === 'published'
                          ? theme.palette.mode === 'dark'
                            ? '#AEE78B'
                            : '#48b705'
                          : theme.palette.warning.main,
                    }}
                  >
                    {capitalize(course.visibility)}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    marginLeft: '10px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '8px',
                    padding: '4px 8px',
                    color: 'rgba(141, 148, 163, 0.87)',
                  }}
                >
                  <SVGCalendar styles={{ marginRight: '7px' }} />
                  <Typography
                    sx={{
                      fontSize: '12px',
                      fontFamily: 'Graphik Semibold',
                      fontWeight: 500,
                      color: 'rgba(141, 148, 163, 0.87)',
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    {new Date(course.createdAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                    })}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '8px',
                    padding: '4px 8px',
                    color: 'rgba(141, 148, 163, 0.87)',
                  }}
                >
                  <SVGPerson styles={{ marginRight: '7px' }} />
                  <Typography
                    sx={{
                      fontSize: '12px',
                      fontFamily: 'Graphik Semibold',
                      fontWeight: 500,
                      color: 'rgba(141, 148, 163, 0.87)',
                    }}
                  >
                    {membersCount}
                  </Typography>
                </Box>
              </Box>
            ) : (
              <ProgressBarWithAvatar
                image={currentUserProfile.image}
                progress={roundCourseProgress(course.ContentLibraryCourseUserProgress?.[0]?.progress_percentage) || 0}
              />
            )}
          </div>
        </CardContent>
        <CardActions sx={{ m: '-3px 12px' }}>
          <Button
            sx={{
              borderRadius: '10px',
              backgroundColor: alpha(theme.palette.primary.main, 0.5),
              color: '#fff',
              marginBottom: '10px',
            }}
            fullWidth
            variant="contained"
          >
            {isCurrentUserAdmin && !isMobile ? 'Manage' : 'Open'}
          </Button>
        </CardActions>
      </Card>
    </div>
  )
}

export default CourseCard
