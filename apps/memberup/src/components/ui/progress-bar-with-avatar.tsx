import { Avatar, Box, LinearProgress, Typography } from '@mui/material'
import React from 'react'

import useAppTheme from '@/memberup/components/hooks/use-app-theme'

interface ProgressBarWithAvatarProps {
  image: string
  progress: number
}

const ProgressBarWithAvatar: React.FC<ProgressBarWithAvatarProps> = ({ image, progress }) => {
  const { isDarkTheme, newGradient, theme } = useAppTheme()
  return (
    <>
      <LinearProgress
        variant="determinate"
        value={progress}
        sx={{
          mt: '24px',
          mb: '8px',
          height: '8px',
          '& .MuiLinearProgress-bar': {
            background: isDarkTheme ? '#AEE78B' : '#48B705',
          },
        }}
      />{' '}
      <Box display="flex" alignItems="center" mt={1} justifyContent="space-between">
        <Typography
          sx={{
            fontFamily: 'Graphik Semibold',
            fontSize: '12px',
            color: isDarkTheme ? 'rgba(255, 255, 255, 0.87)' : 'rgb(0, 0, 0)',
          }}
        >
          Your Progress
        </Typography>
        <Box display="flex" alignItems="center">
          <Avatar sx={{ height: '20px', width: '20px' }} src={image} alt="User Avatar" />
          <Typography
            sx={{
              ml: 1,
              fontFamily: 'Graphik Semibold',
              fontSize: '12px',
              fontWeight: '500',
              color: isDarkTheme ? 'rgba(255, 255, 255, 0.87)' : 'rgb(0,0,0)',
            }}
          >
            {progress}%
          </Typography>
        </Box>
      </Box>
    </>
  )
}

export default ProgressBarWithAvatar
