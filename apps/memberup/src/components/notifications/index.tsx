// TODO: Looks like this is a placeholder? Do we still need?
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import React, { useState } from 'react'

import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import SVGClose from '@/memberup/components/svgs/close'
import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { selectUser, selectUserFullName, selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    width: '100%',
    maxWidth: '100%',
    marginLeft: 'auto',
    '& .MuiDialog-container': {
      alignItems: 'unset',
    },
    '& .MuiDialog-paperFullWidth': {
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      maxWidth: '100%',
      margin: 0,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
  },
  dialogContent: {
    minHeight: 320,
    lineHeight: 1,
    '& img': {
      borderRadius: 16,
    },
  },
  content: {
    color: theme.palette.text.disabled,
    maxWidth: 747,
    margin: 'auto',
    padding: 16,
  },
  title: {
    marginBottom: 32,
  },
  gridItem: {
    borderBottomColor: theme.palette.action.disabledBackground,
    borderBottomWidth: 1,
    borderBottomStyle: 'solid',
    '&:last-of-type': {
      borderBottom: 'none',
    },
  },
}))

const Notifications: React.FC<{
  open: boolean
  onClose: () => void
}> = ({ open, onClose }) => {
  const classes = useStyles()
  const membership = useAppSelector((state) => selectMembership(state))
  const user = useAppSelector((state) => selectUser(state))
  const userProfile = useAppSelector((state) => selectUserProfile(state))
  const userFullName = useAppSelector((state) => selectUserFullName(state))

  const [requestGetActivities, setRequestGetActivities] = useState(false)
  const [activities, setActivities] = useState({
    activities: [],
    hasMore: true,
    next: null,
  })

  const fetchMoreActivities = async (feed, id_gt?: string) => {
    if (requestGetActivities) return
    setRequestGetActivities(true)
    // getActivitiesApi(feed, {
    //   limit: FEEDS_LIMIT,
    //   id_gt,
    //   enrich: true,
    //   reactions: {
    //     counts: true,
    //     recent: true,
    //   },
    // })
    //   .then((result) => {
    //     if (mountedRef.current) {
    //       setActivities((prevValue) => ({
    //         activities: id_gt ? prevValue.activities.concat(result.results) : result.results,
    //         hasMore: Boolean(result.next),
    //         next: result.next,
    //       }))
    //     }
    //   })
    //   .catch((err) => {
    //     if (!mountedRef.current) return
    //     setActivities((prevValue) => ({
    //       activities: prevValue.activities,
    //       hasMore: false,
    //       next: '',
    //     }))
    //   })
    //   .finally(() => {
    //     setRequestGetActivities(false)
    //   })
  }

  // useEffect(() => {
  //   if (!userFeed) return
  //   fetchMoreActivities(userFeed)
  //   return () => {}
  // }, [userFeed])

  return (
    <Dialog
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      aria-labelledby="notifications-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="notifications-dialog-title">
        <IconButton size="medium" aria-label="close" className="close" onClick={onClose}>
          <SVGClose fontSize={16} />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <div className={classes.content}>
          <Grid container alignItems="center" spacing={3}>
            <Grid item xs>
              <Typography variant="h4">Notifications</Typography>
            </Grid>
            <Grid item>
              <Button variant="text" color="inherit">
                Clear All
              </Button>
            </Grid>
          </Grid>
          <br />
          <br />
          <Grid container spacing={3}>
            <Grid className={classes.gridItem} item xs={12}>
              <Grid container alignItems="center" spacing={2}>
                <Grid item>
                  <AppProfileImage
                    imageUrl={userProfile?.image || user?.image}
                    cropArea={userProfile?.image_crop_area || user?.image_crop_area}
                    name={userFullName}
                    size={48}
                  />
                </Grid>
                <Grid item xs>
                  <Typography variant="body2" component="span">
                    <b>{userFullName}</b>
                  </Typography>
                  &nbsp;
                  <Typography variant="body2" component="span" color="inherit">
                    liked your post
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography variant="body2" component="span" color="inherit">
                    24 minutes ago
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
            <Grid className={classes.gridItem} item xs={12}>
              <Grid container alignItems="center" spacing={2}>
                <Grid item>
                  <AppProfileImage
                    imageUrl={userProfile?.image || user?.image}
                    cropArea={userProfile?.image_crop_area || user?.image_crop_area}
                    name={userFullName}
                    size={48}
                  />
                </Grid>
                <Grid item xs>
                  <Typography variant="body2" component="span">
                    <b>{userFullName}</b>
                  </Typography>
                  &nbsp;
                  <Typography variant="body2" component="span" color="inherit">
                    liked your post
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography variant="body2" component="span" color="inherit">
                    24 minutes ago
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default Notifications
