import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import React, { useEffect, useMemo, useState } from 'react'

import SVGHideIcon from '../svgs/hide-icon'
import SVGSparkLogo from '../svgs/spark-logo'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { SparkStatusIndicator } from '@/components/spark/spark-status-indicator'
import { useStore } from '@/hooks/useStore'
import SparkAnsweredDialog from '@/memberup/components/dialogs/spark/spark-answered'
import SparkResponsesDialog from '@/memberup/components/dialogs/spark/spark-responses'
import SparkShareAnswerDialog from '@/memberup/components/dialogs/spark/spark-share-answer'
import SparkShareSocialDialog from '@/memberup/components/dialogs/spark/spark-share-social'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import { selectMembersMap } from '@/memberup/store/features/memberSlice'
import { setSparkResponses } from '@/memberup/store/features/sparkSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { useAppDispatch } from '@/memberup/store/store'
import { AppProfileImage } from '@/shared-components/common/profile-image'
import { useBrowserLayoutEffect } from '@/shared-components/hooks/use-browser-layout-effect'
import { useMounted } from '@/shared-components/hooks/use-mounted'
import { ISparkQuestion, ISparkResponse } from '@/shared-types/interfaces'

const useStyles = makeStyles((theme) => ({
  normalText: {
    color: theme.palette.mode === 'dark' ? 'rgba(141, 148, 163, 1)' : '#000',
    fontFamily: '"Graphik Medium"',
    fontSize: 12,
    fontWeight: 500,
    fontStyle: 'medium',
  },
  icon: {
    color: 'rgba(141, 148, 163, 1)',
  },
}))
const SparkQuestionCard: React.FC<{ question: ISparkQuestion; sparkStreak: number; onHideClick: any }> = ({
  question,
  sparkStreak,
  onHideClick,
}) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const { isCurrentUserAdmin } = useCheckUserRole()
  const user = useStore((state) => state.auth.user)
  const [openAnswer, setOpenAnswer] = useState(false)
  const [openAnswered, setOpenAnswered] = useState<{
    open: boolean
    response: ISparkResponse
  }>({
    open: false,
    response: null,
  })
  const { theme, isLightTheme, newGradient } = useAppTheme()
  const members = useAppSelector((state) => selectMembersMap(state))
  const [openSocial, setOpenSocial] = useState(false)
  const [openResponses, setOpenResponses] = useState(false)
  const dispatch = useAppDispatch()

  useBrowserLayoutEffect(() => {
    if (mountedRef.current) {
      setOpenAnswered({
        open: false,
        response: user?.id ? (question?.spark_responses || []).find((r) => r.user_id === user.id) : null,
      })
    }
  }, [question, user?.id])

  const answered = useMemo(() => {
    if (!user?.id) return false
    if (isCurrentUserAdmin && question?.answer) return true
    return Boolean(openAnswered.response)
  }, [openAnswered, user, isCurrentUserAdmin])

  const [allResponses, setAllResponses] = useState(question?.spark_responses || [])

  const memoizedRespondedUsers = useMemo(() => {
    const users = allResponses.map((r) => {
      return {
        id: r.user_id,
        image: members?.[r.user_id]?.profile?.image,
        image_crop_area: members?.[r.user_id]?.profile?.image_crop_area,
        name: members?.[r.user_id]?.name || r.name,
      }
    })
    return users.map((r, index) => (
      <AppProfileImage
        key={r.id}
        imageUrl={r.image}
        cropArea={r.image_crop_area}
        name={r.name}
        size={32}
        style={{
          marginLeft: index === 0 ? 0 : -8,
          zIndex: 1,
        }}
      />
    ))
  }, [allResponses, members])

  useEffect(() => {
    dispatch(setSparkResponses(allResponses))
  }, [allResponses])

  return (
    <Box
      className="spark-question-card"
      sx={{
        position: 'relative',
        width: '100%',
        overflow: 'hidden',
        marginBottom: '16px',
        boxShadow: theme.palette.mode === 'light' ? '0px 4px 8px rgba(0, 0, 0, 0.1)' : 'none',
        borderRadius: '16px',
        zIndex: 1,
      }}
    >
      <Box
        sx={{
          '& .MuiButtonBase-root': {
            '&:hover': { backgroundColor: 'transparent !important' },
          },
          color: '#FFFFFF',
          position: 'absolute',
          top: 8,
          right: 8,
          zIndex: 10,
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            zIndex: 1,
            color: theme.palette.mode === 'dark' ? 'rgb(141, 148, 163)' : '#000',
            '& .MuiButtonBase-root': {
              '&:hover': { backgroundColor: 'transparent !important' },
            },
          }}
          aria-label="hide"
          onClick={onHideClick}
        >
          {' '}
          <SVGHideIcon />
          <Typography sx={{ marginLeft: '8px' }} className={classes.normalText}>
            Hide
          </Typography>
        </IconButton>
      </Box>
      <Box
        sx={{
          position: 'absolute',
          left: 1,
          top: 1,
          width: '100%',
          height: '100%',
          borderRadius: '16px',
          background: isLightTheme ? '#FFFFFF' : '#212126',
          opacity: isLightTheme ? 0.5 : 1.0,
          overflow: 'hidden',
          zIndex: 1,
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          display: 'block',
          background: newGradient,
          top: 0,
          left: -50,
          width: '40%',
          height: '70%',
          borderTopRightRadius: '10%',
          borderBottomRightRadius: '50%',
          filter: 'blur(60px) opacity(0.7)',
          webkitFilter: 'blur(60px)',
          zIndex: 1,
        }}
      />
      <Box
        sx={{
          position: 'relative',
          padding: 4,
          zIndex: 1,
        }}
      >
        <Grid
          container
          sx={{
            position: 'relative',
            '& svg': {
              display: 'inline-block',
            },
          }}
        >
          <Grid className="text-center" item xs={12}>
            {isLightTheme ? (
              <SVGSparkLogo width={74} height={24} styles={{ color: '#000' }} />
            ) : (
              <SVGSparkLogo width={74} height={24} styles={{ color: '#fff' }} />
            )}
          </Grid>

          <div className={'mt-4 w-full text-center text-xl font-bold'}>{question?.content || ''}</div>

          {allResponses.length > 0 ? (
            <Grid item xs={12} sx={{ pt: '18px' }}>
              <Box
                sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'center' }}
                data-cy="spark-responded-members"
              >
                {memoizedRespondedUsers}
              </Box>
            </Grid>
          ) : (
            <div className={'mt-4 w-full text-center'}>Be the first to respond.</div>
          )}
          <Grid className="py-8 text-center" item xs={12}>
            <SparkStatusIndicator streak={sparkStreak} />
          </Grid>
          <Grid item xs={12}>
            <Button
              className="round-small"
              variant="contained"
              fullWidth
              sx={{
                fontSize: 14,
                height: 40,
                backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
                color: '#fff',
              }}
              onClick={() => {
                if (answered) {
                  setOpenResponses(true)
                } else {
                  setOpenAnswer(true)
                }
              }}
              data-cy="spark-answer-button"
            >
              {answered
                ? `View ${allResponses.length > 1 ? 'Answers' : 'Answer'} From ${
                    allResponses.length
                  } ${allResponses.length > 1 ? 'Members' : 'Member'}`
                : 'Share Your Response'}
            </Button>
          </Grid>
        </Grid>
      </Box>
      {openAnswer && (
        <SparkShareAnswerDialog
          open={openAnswer}
          question={question}
          onClose={(e) => {
            setOpenAnswer(false)
            if (e) {
              setOpenAnswered({
                open: true,
                response: e.response,
              })
              setAllResponses((responses) => [...responses, e.response])
            }
          }}
        />
      )}

      {openAnswered.open && (
        <SparkAnsweredDialog
          open={openAnswered.open}
          question={question}
          onClose={() =>
            setOpenAnswered({
              ...openAnswered,
              open: false,
            })
          }
          onShareOnSocial={() => {
            setOpenAnswered({
              ...openAnswered,
              open: false,
            })
            setOpenSocial(true)
          }}
          onViewCommunityResponses={() => {
            setOpenAnswered({
              ...openAnswered,
              open: false,
            })
            setOpenResponses(true)
          }}
        />
      )}

      {openResponses && (
        <SparkResponsesDialog
          open={openResponses}
          question={question}
          onClose={() => {
            setOpenResponses(false)
          }}
        />
      )}

      {openSocial && (
        <SparkShareSocialDialog
          open={openSocial}
          question={question}
          onClose={() => {
            setOpenSocial(false)
          }}
        />
      )}
    </Box>
  )
}

export default SparkQuestionCard
