import { Stack, Theme, useMediaQuery } from '@mui/material'
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'

import SVGHidePost from '../svgs/hide-post'
import SVGSparkLogo from '../svgs/spark-logo'
import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { SparkStatusIndicator } from '@/components/spark/spark-status-indicator'
import { Button } from '@/components/ui'
import { useStore } from '@/hooks/useStore'
import SparkEmptyInfoDialog from '@/memberup/components/dialogs/spark/spark-empty-info'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  normalText: {
    color: theme.palette.mode === 'dark' ? 'rgba(141, 148, 163, 1)' : '#000',
    fontFamily: '"Graphik Medium"',
    fontSize: 12,
    fontWeight: 500,
    fontStyle: 'medium',
  },
  icon: {
    color: 'rgba(141, 148, 163, 1)',
  },
}))

const SparkNotConfigured: React.FC<{ onHideClick: any }> = ({ onHideClick }) => {
  const classes = useStyles()
  const { theme, newGradient } = useAppTheme()
  const isSmallScreen = useMediaQuery((theme: Theme) => theme.breakpoints.down('sm'))
  const marginSpace = isSmallScreen ? '-2px' : '4px'
  const router = useRouter()
  const isMdScreen = useMediaQuery((theme: Theme) => theme.breakpoints.down('md'))
  const [openInfo, setOpenInfo] = useState(false)
  const membership = useAppSelector((state) => selectMembership(state))
  const setAdminSettingsOpen = useStore((state) => state.community.setCommunityAdminSettingsOpen)

  const isDarkMode = theme.palette.mode === 'dark'

  const sparkGradientWhite = 'linear-gradient(90deg, #000000 0%, RGBA(0, 0, 0, 0.01) 3%, RGBA(0, 0, 0, 0.2) 100%)'
  const sparkGradientBlack = 'linear-gradient(90deg, #000000 0%, RGBA(0, 0, 0, 0.2) 25%, RGBA(0, 0, 0, 0.2) 100%)'

  return (
    <Box
      className="spark-not-configured"
      sx={{
        p: 3,
        pt: 0,
        pb: 0,
        position: 'relative',
        width: '100%',
        borderRadius: '16px',
        marginBottom: '16px',
        minHeight: { xs: '320px', sm: '295px' },
        overflow: 'hidden',
        background: isDarkMode ? '#212124' : '#FFFFFF',
        zIndex: 1,
        display: 'flex',
        flexDirection: 'column',
        boxShadow: theme.palette.mode === 'light' ? '0px 4px 8px rgba(0, 0, 0, 0.1)' : 'none',
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          display: 'block',
          background: newGradient,
          top: 0,
          left: -50,
          width: '40%',
          height: '70%',
          borderTopRightRadius: '10%',
          borderBottomRightRadius: '50%',
          filter: 'blur(60px) opacity(0.7)',
          webkitFilter: 'blur(60px)',
          zIndex: 1,
        }}
      />
      <Box
        sx={{
          padding: 4,
          paddingBottom: 0,
          position: 'relative',
          zIndex: 2,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            color: '#FFFFFF',
            position: 'absolute',
            top: 8,
            right: 8,
            left: 8,
            zIndex: 10,
          }}
        >
          {/* Info Button */}
          <Box className="text-center">
            <Button onClick={() => setOpenInfo(true)}>What is this?</Button>
          </Box>

          {/* Hide Button */}
          <IconButton
            sx={{
              position: 'absolute',
              top: '19px',
              right: { xs: '-5px', sm: '20px' },
              zIndex: 10,
              '&.MuiButtonBase-root': {
                padding: '0px !important',
                '&:hover': { backgroundColor: 'transparent !important' },
              },
            }}
            aria-label="hide"
            onClick={onHideClick}
          >
            <SVGHidePost styles={{ color: theme.palette.mode === 'dark' ? 'rgba(141, 148, 163, 1)' : '#000' }} />
            <Typography sx={{ marginLeft: '6px' }} className={classes.normalText}>
              Hide
            </Typography>
          </IconButton>
        </Box>
        <Grid container spacing={3}>
          <Grid className="text-center" item xs={12}>
            <SVGSparkLogo
              width={74}
              height={24}
              styles={{
                color: isDarkMode ? '#fff' : '#000',
                marginTop: '-4px',
                marginBottom: '24px',
                display: 'inline-block',
              }}
            />
            <Typography
              variant="body1"
              align="center"
              sx={{
                color: isDarkMode ? '#FFFFFF' : '#000000',
                fontFamily: 'Graphik SemiBold',
                fontWeight: 500,
                fontSize: { xs: '18px', md: '18px', lg: '18px' },
                lineHeight: '24px',
                marginBottom: { xs: '10px', sm: '20px' },
                zIndex: 10,
              }}
            >
              What would you say is your greatest achievement?
            </Typography>
          </Grid>
        </Grid>
      </Box>
      <div className="pb-3 text-center">
        <SparkStatusIndicator streak={124} />
      </div>
      <Box
        sx={{
          order: { xs: 1, sm: 'unset' },
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%',
          zIndex: 10,
        }}
      >
        <Stack direction={'row'}>
          {Array.from({ length: 10 }, (_, i) => i + 1).map((index) => (
            <Box
              key={index}
              style={{
                width: '32px',
                margin: `6px ${marginSpace}`,
                height: '32px',
                position: 'relative',
              }}
            >
              <AppProfileImage
                imageUrl={`/assets/default/images/face${index}.jpeg`}
                size={32}
                style={{
                  position: 'absolute',
                  overflow: 'hidden',
                }}
              />
            </Box>
          ))}
          <Box
            style={{
              width: '32px',
              margin: `6px ${marginSpace}`,
              height: '32px',
              position: 'relative',
              backgroundColor: 'rgba(88, 93, 102, 0.6)',
              WebkitBackdropFilter: 'blur(4px)',
              borderRadius: '50%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Typography
              sx={{
                fontSize: '10px',
                color: '#fff',
              }}
            >
              +37
            </Typography>
          </Box>
        </Stack>
      </Box>
      <Button
        className={'mt-4 w-full'}
        type="button"
        variant="default"
        onClick={() => setAdminSettingsOpen(true)}
        data-cy="setup-spark-button"
      >
        Setup Spark
      </Button>

      <SparkEmptyInfoDialog open={openInfo} onClose={() => setOpenInfo(false)} />
    </Box>
  )
}

export default SparkNotConfigured
