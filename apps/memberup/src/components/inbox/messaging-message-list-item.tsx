import CircleIcon from '@mui/icons-material/Circle'
import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import SentimentSatisfiedOutlinedIcon from '@mui/icons-material/SentimentSatisfiedOutlined'
import Box from '@mui/material/Box'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import ListItemText from '@mui/material/ListItemText'
import Menu from '@mui/material/Menu'
import MenuItem from '@mui/material/MenuItem'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import { useEffect, useMemo, useRef, useState } from 'react'
import { messageHasReactions, ReactionSelector, useMessageContext } from 'stream-chat-react'

import AppMessagingAttachmentsView from './messaging-attachments'
import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import { SimpleReactionsList } from '@/components/chat/simple-reactions-list'
import { UserDetailsHoverCard } from '@/components/community/user-details-hover-card'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import AppLinkify from '@/memberup/components/common/app-linkify'
import useIsInViewPort from '@/memberup/components/hooks/use-is-in-viewport'
import { refineStreamMessageText } from '@/memberup/libs/getstream'
import { setMarkReadNotification } from '@/memberup/store/features/uiSlice'
import { selectUser } from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

const AppMessagingMessageListItem = () => {
  const mountedRef = useMounted(true)
  const rootRef = useRef(null)
  const theme = useTheme()
  const dispatch = useAppDispatch()
  const user = useAppSelector((state) => selectUser(state))
  const inViewport = useIsInViewPort(rootRef)
  const { groupStyles, isMyMessage, message, handleDelete, handleReaction } = useMessageContext()
  const streamUser = message.user
  const isMine = isMyMessage()
  const [moreAnchorEl, setMoreAnchorEl] = useState(null)
  const [messageText, setMessageText] = useState('')
  const isReactionEnabled = true
  const [reactionSelectorOpen, setReactionSelectorOpen] = useState(false)

  useEffect(() => {
    if (!mountedRef.current) return
    setMessageText(message.text || '')
  }, [message])

  useEffect(() => {
    if (inViewport && message.id && !isMine) {
      dispatch(setMarkReadNotification(message.id))
    }
  }, [inViewport, message?.id])

  const handleClickMore = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation()
    e.preventDefault()
    setMoreAnchorEl(e.currentTarget)
  }

  const onDeleteMessage = (e: React.MouseEvent<HTMLElement>) => {
    handleDelete(e)
    setMoreAnchorEl(null)
  }

  const hasReactions = messageHasReactions(message)
  const hasAttachments = message.attachments && message.attachments.length > 0
  const showReactionsList = hasReactions
  const isBottom = groupStyles.includes('bottom')
  const isMiddle = groupStyles.includes('middle')
  const isSingle = groupStyles.includes('single')
  const isLightTheme = theme.palette.mode === THEME_MODE_ENUM.light

  /**
   * Render date
   * @param dateStr
   * @param visibleUserName
   * @param visibleToday
   * @param onlyDay
   * @returns
   * @description
   * 1. If visibleUserName is true, render user name  + date
   * 2. If visibleToday is true, render today at time
   * 3. If onlyDay is true, render only day, without time, if not,
   *    renders time prefix + 'K:mm aa' format
   */
  const renderDate = (
    dateStr: string | Date,
    visibleUserName: boolean = false,
    visibleToday: boolean = false,
    onlyDay: boolean = false,
  ) => {
    let result = ''
    const prefix = (visibleUserName && !isMine && message.user?.name) || ''
    const tempDate = new Date(dateStr)
    const date = tempDate.getDate()
    const month = tempDate.getMonth()
    const year = tempDate.getFullYear()
    const now = new Date()
    const timeFormat = 'h:mm aa'
    if (date === now.getDate() && month === now.getMonth() && year === now.getFullYear())
      return onlyDay
        ? 'Today'
        : `${visibleToday ? 'Today at ' : ''}${formatDate({
            date: tempDate,
            format: timeFormat,
          })}`
    const yesterday = new Date(now.setDate(now.getDate() - 1))
    if (
      tempDate.getDate() === yesterday.getDate() &&
      tempDate.getMonth() === yesterday.getMonth() &&
      tempDate.getFullYear() === yesterday.getFullYear()
    ) {
      result = onlyDay ? 'Yesterday' : `Yesterday at ${formatDate({ date: tempDate, format: timeFormat })}`
    } else {
      result = formatDate({ date: tempDate, format: onlyDay ? 'PPP' : timeFormat })
    }
    return prefix ? (
      <>
        {prefix}&nbsp;
        <CircleIcon sx={{ fontSize: 4, height: 8 }} />
        &nbsp;{result}
      </>
    ) : (
      result
    )
  }

  const renderAttachments = useMemo(() => {
    if (!message.attachments.length) return null
    const hasMessageText = Boolean(messageText)
    return (
      <Box
        sx={{
          width: 'auto',
          position: 'relative',
          mt: hasMessageText ? '4px' : 0,
          '& .MuiCardHeader-root': {
            p: '6px!important',
          },
          '& .media-files': {
            mt: '0px!important',
            mb: '0px!important',
          },
        }}
      >
        <AppMessagingAttachmentsView feed={message} isMine={isMine} handleRemovePreview={() => {}} />
      </Box>
    )
  }, [messageText, message.attachments, message['date_line'], isMine, isMiddle, isBottom])

  return (
    <>
      {message['date_line'] && (
        <Box padding={2} marginBottom="24px" marginTop={!message['is_first'] && '22px'}>
          <Grid container alignItems="center" spacing={2}>
            <Grid item xs>
              <Divider sx={{ borderColor: 'rgba(141, 148, 163, 0.16)' }} />
            </Grid>
            <Grid item>
              <Typography
                className="font-family-graphik-semibold uppercase"
                color="text.disabled"
                variant="body2"
                sx={{ fontSize: 10 }}
              >
                {renderDate(message.updated_at || message.created_at, false, true, true)}
              </Typography>
            </Grid>
            <Grid item xs>
              <Divider sx={{ borderColor: 'rgba(141, 148, 163, 0.16)' }} />
            </Grid>
          </Grid>
        </Box>
      )}
      <Box
        sx={{
          pb: '2px',
          position: 'relative',
          maxWidth: {
            xs: '100%',
            sm: '80%',
            md: '70%',
          },
          marginLeft: isMine ? 'auto' : 0,
          '& .app-messaging-icon-button': {
            visibility: 'hidden',
          },
          '&:hover': {
            '& .app-messaging-icon-button': {
              visibility: 'visible',
            },
          },
          '& .str-chat__reaction-selector': {
            background: isLightTheme ? theme.palette.background.paper : '#17171a',
            backgroundImage: 'none',
            boxShadow: isLightTheme ? `0 3px 1px rgba(0, 0, 0, 0.01), 0 5px 8px rgba(0, 0, 0, 0.1)` : 'none',
            outline: isLightTheme ? '0.5px solid rgba(224, 224, 224, 0.03)' : 'none',
            height: {
              xs: 40,
              sm: 52,
            },
            top: {
              xs: -40,
              sm: -52,
            },
            zIndex: 1,
          },
          '& .str-chat__reaction-selector::before': {
            content: "''",
            display: 'none',
          },
          '& .str-chat__message-reactions-list': {
            marginLeft: {
              xs: '6px',
              sm: '12px',
            },
            marginRight: {
              xs: '6px',
              sm: '12px',
            },
          },
          '& .str-chat__simple-reactions-list': {
            borderColor: isLightTheme ? 'rgba(0, 0, 0, 0.12)' : undefined,
            borderRadius: '16px',
            borderWidth: '2px',
            padding: '4px 8px 2px',
          },
          '& .str-chat__simple-reactions-list-item--last-number, & .str-chat__message-reactions-list-item__count': {
            color: theme.palette.text.primary,
          },
          '& .str-chat__simple-reactions-list-tooltip': {
            backgroundColor: theme.palette.background.paper,
            backgroundImage: 'none',
            border: `1px solid ${isLightTheme ? 'rgba(0, 0, 0, 0.08)' : 'rgba(46, 47, 52, 0.9)'}`,
            color: theme.palette.text.primary,
            padding: '12px',
            maxWidth: 140,
            width: 'max-content',
            zIndex: 1,
          },
          '& .str-chat__simple-reactions-list-tooltip>.arrow': {
            borderTopColor: theme.palette.background.paper,
          },
          '& .str-chat__message-reactions-list-item': {
            marginLeft: {
              xs: '5px',
              sm: '8px',
            },
            marginRight: {
              xs: '5px',
              sm: '8px',
            },
          },
          '& .str-chat__simple-reactions-list, & .str-chat__message-reactions': {
            borderRadius: '11px !important',
          },
          '& .emoji-mart-emoji span': {
            width: '18px',
            height: '18px',
          },
        }}
        ref={rootRef}
      >
        {/*<MessageOptions />*/}
        <Grid
          container
          alignItems="flex-end"
          flexDirection={streamUser?.id === user.id ? 'row' : 'row-reverse'}
          flexWrap="nowrap"
          spacing={2}
          data-cy="app-messaging-list-item"
          data-message={message.id}
          data-message-channel={message.cid}
        >
          <Grid item xs></Grid>
          <Grid item sx={{ textAlign: streamUser?.id === user.id ? 'right' : 'left' }}>
            <Box
              sx={{
                display: 'inline-flex',
                justifyContent: streamUser?.id === user.id ? 'flex-end' : 'flex-start',
                flexDirection: streamUser?.id === user.id ? 'row' : 'row-reverse',
              }}
            >
              {isMine && (
                <IconButton
                  className="app-messaging-icon-button"
                  size="small"
                  aria-label="more"
                  onClick={(e) => handleClickMore(e)}
                >
                  <MoreHorizIcon fontSize="small" />
                </IconButton>
              )}
              {isReactionEnabled && (
                <Popover open={reactionSelectorOpen} onOpenChange={(value) => setReactionSelectorOpen(value)}>
                  <PopoverTrigger asChild>
                    <IconButton className="app-messaging-icon-button" size="small" aria-label="emoji">
                      <SentimentSatisfiedOutlinedIcon fontSize="small" />
                    </IconButton>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-2 [&_ul]:space-x-2">
                    <ReactionSelector
                      detailedView={false}
                      handleReaction={(reactionType, event) => {
                        setReactionSelectorOpen(false)
                        return handleReaction(reactionType, event)
                      }}
                    />
                  </PopoverContent>
                </Popover>
              )}
              <Box
                sx={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: streamUser?.id === user.id ? 'flex-end' : 'flex-start',
                  position: 'relative',
                }}
              >
                {(Boolean(messageText) || Boolean(hasAttachments)) && (
                  <Box
                    sx={{
                      height: '100%',
                      backgroundColor: isMine
                        ? isLightTheme
                          ? '#ECEBEB'
                          : 'rgba(0, 0, 0, 0.24)'
                        : isLightTheme
                          ? '#FCFCFC'
                          : 'rgba(255, 255, 255, 0.08)',
                      border: isLightTheme ? '1px solid rgba(0, 0, 0, 0.08)' : undefined,
                      borderRadius: '16px',
                      borderBottomLeftRadius: !isMine && '2px',
                      borderBottomRightRadius: isMine && '2px',
                      p: Boolean(hasAttachments) ? '0px' : '12px 12px 6px 12px',
                      pt: Boolean(hasAttachments) ? '6px' : '6px',
                      mb: Boolean(hasAttachments) ? '4px' : '0px',
                      '& .MuiTypography-root': {
                        wordBreak: 'break-word',
                        paddingLeft: Boolean(hasAttachments) ? '12px !important' : 0,
                        paddingRight: Boolean(hasAttachments) ? '12px !important' : 0,
                      },
                    }}
                  >
                    <AppLinkify
                      content={refineStreamMessageText(messageText)}
                      isMessage={true}
                      skeletonDimensions={{ height: 440 }}
                    />
                    {renderAttachments}
                  </Box>
                )}
              </Box>
            </Box>
            {showReactionsList && <SimpleReactionsList />}
            {(isSingle || isBottom) && (
              <Box sx={{ mt: '2px' }}>
                <Typography variant="body2" component="span" color={isLightTheme ? 'text.disabled' : 'action.disabled'}>
                  {!isMine && message.user.name ? (
                    <UserDetailsHoverCard username={message.user.username}>
                      <div className="inline cursor-pointer">
                        {message.user.name}&nbsp;
                        <CircleIcon sx={{ fontSize: 4, height: 8 }} />
                        &nbsp;
                      </div>
                    </UserDetailsHoverCard>
                  ) : (
                    ''
                  )}
                  {renderDate(message.updated_at || message.created_at, false, false)}
                </Typography>
              </Box>
            )}
          </Grid>
          <Grid item>
            <Box
              sx={{
                pb: showReactionsList ? '44px' : '16px',
                width: 32,
                cursor: !isMine ? 'pointer' : undefined,
              }}
            >
              {(isSingle || isBottom) && (
                <UserDetailsHoverCard username={streamUser.username}>
                  <div>
                    <AppProfileImage
                      imageUrl={streamUser?.image}
                      cropArea={streamUser?.image_crop_area as any}
                      name={streamUser?.name || streamUser?.username}
                      size={32}
                      style={{ borderRadius: 16 }}
                    />
                  </div>
                </UserDetailsHoverCard>
              )}
            </Box>
          </Grid>
        </Grid>
        {isMine && (
          <Menu
            anchorEl={moreAnchorEl}
            open={Boolean(moreAnchorEl)}
            onClose={() => setMoreAnchorEl(null)}
            PaperProps={{
              sx: {
                ...theme.components.MuiCssBaseline.styleOverrides['body']['& .border-color10'],
                boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
                borderWidth: 1,
                borderStyle: 'solid',
                borderRadius: '12px',
                minWidth: 160,
                '& .MuiMenu-list': {
                  backgroundColor: !isLightTheme && 'rgb(23, 23, 26)',
                  p: '2px!important',
                },
                '& .MuiMenuItem-root': {
                  borderRadius: '12px',
                  height: 40,
                  p: '12px',
                  '&:hover': {
                    backgroundColor: !isLightTheme && 'rgba(141, 148, 163, 0.04)',
                  },
                },
              },
            }}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
          >
            <MenuItem onClick={onDeleteMessage} sx={{ '&:hover': { borderRadius: '10px' } }}>
              <ListItemText
                primary={
                  <Typography sx={{ pt: '2px', fontSize: 13 }} className="font-family-graphik-medium">
                    Unsend
                  </Typography>
                }
              />
            </MenuItem>
          </Menu>
        )}
      </Box>
    </>
  )
}

export default AppMessagingMessageListItem
