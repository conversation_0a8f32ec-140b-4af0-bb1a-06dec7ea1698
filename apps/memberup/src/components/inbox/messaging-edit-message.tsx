import { refineStreamMessageInputText } from '@/memberup/libs/getstream'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { insertStr } from '@memberup/shared/src/libs/string-utils'
import SentimentVerySatisfiedIcon from '@mui/icons-material/SentimentVerySatisfied'
import Box from '@mui/material/Box'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import TextField from '@mui/material/TextField'
import useTheme from '@mui/material/styles/useTheme'
import AttachFileIcon from '@mui/icons-material/AttachFile'
import ArrowCircleRightIcon from '@mui/icons-material/ArrowCircleRight'
import dynamic from 'next/dynamic'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { useMessageInputContext, useChatContext, LoadingIndicatorIcon } from 'stream-chat-react'
import { useDropzone } from 'react-dropzone'
import { ACCEPT_ALL } from '@memberup/shared/src/types/consts'
import { getFileType, getThumbnailForVideo } from '@memberup/shared/src/libs/file'
import { toast } from 'react-toastify'
import { IGif } from '@giphy/js-types'
import { AppImg } from '@memberup/shared/src/components/common/media/image'
import Typography from '@mui/material/Typography'
import Image from 'next/image'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import { TAttachment } from '@memberup/shared/src/types/types'
import SVGCloseSmall from '@/memberup/components/svgs/close-small'
import { getFileTypeIcon } from '@/memberup/libs/utils'
import { shortenFileName } from '@/shared-libs/string-utils'
import { uploadFileToCloudinaryApi } from '@memberup/shared/src/services/apis/cloudinary.api'
import { base64toImgFile } from '@/memberup/libs/attachments'
import useUploadFiles from '@/memberup/components/hooks/use-upload-files'
import { useAppMessagingContext } from '@/memberup/components/inbox/messaging-context'
import SVGPlus from '../svgs/plus-icon'
import { showToast } from '@memberup/shared/src/libs/toast'

const IMG_FILE_MAX_SIZE = parseInt(process.env.NEXT_PUBLIC_IMG_FILE_MAX_SIZE)
const VIDEO_FILE_MAX_SIZE = parseInt(process.env.NEXT_PUBLIC_VIDEO_FILE_MAX_SIZE)
const EmojiPicker = dynamic(() => import('@/memberup/components/common/pickers/emoji-picker'), {
  ssr: false,
})
const PDFtoImage = dynamic(() => import('@/memberup/components/common/pdf-to-image'), {
  ssr: false,
})

const VideoThumbnail = ({ file }) => {
  const mountedRef = useMounted(true)
  const [src, setSrc] = useState('')

  useEffect(() => {
    const getThumbnail = async () => {
      const temp = await getThumbnailForVideo(file)
      if (mountedRef.current) {
        setSrc(temp)
        const videoThumbnailBlob = base64toImgFile(temp, file.name)
        const uploadedThumbnail = await uploadFileToCloudinaryApi(videoThumbnailBlob, 'image')
        file.thumbnail = uploadedThumbnail.data.secure_url
        file.size_in_bytes = file.size
      }
    }
    if (file instanceof File) {
      getThumbnail()
    }
  }, [file])

  if (!src) return null
  return <AppImg src={src} height={100} width={100} alt="No Attachment Image" />
}

type FormDataType = {
  text: string
  attachments: TAttachment[]
  attachment_files: (File & Partial<IGif> & { is_gif?: boolean })[]
}
const FormValue: FormDataType = {
  text: '',
  attachments: [],
  attachment_files: [],
}

const AppMessagingEditMessage = () => {
  const mountedRef = useMounted(true)
  const theme = useTheme()
  const { client: streamChatClient, channel: streamChatChannel } = useChatContext()
  const { disabled, focus } = useMessageInputContext()
  const { selectedMessage, setSelectedMessage } = useAppMessagingContext()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const selectionRef = useRef({
    start: 1,
    end: -1,
  })
  const textInputRef = useRef(null)
  const isDarkTheme = theme.palette.mode === THEME_MODE_ENUM.dark

  const onDrop = useCallback((acceptedFiles) => {
    const acceptedFile = acceptedFiles?.[0]
    if (!acceptedFile) return
    const fileType = getFileType(acceptedFile)
    if (fileType === 'video') {
      if (acceptedFile.size > VIDEO_FILE_MAX_SIZE) {
        showToast(
          `Video file size needs to be ${VIDEO_FILE_MAX_SIZE / 1024 / 1024 / 1024} GB or less.`,
          'error'
        )
        return
      }
    } else if (acceptedFile.size > IMG_FILE_MAX_SIZE) {
      showToast(
        `The ${fileType === 'image' ? 'image file' : 'file'} size needs to be ${
          IMG_FILE_MAX_SIZE / 1024 / 1024
        } MB or less.`,
        'error'
      )
      return
    }

    const temp = getValues('attachment_files')
    setValue('attachment_files', [...temp, acceptedFile], {
      shouldValidate: true,
      shouldDirty: true,
    })
    // Do something with the files
  }, [])

  const {
    getRootProps,
    getInputProps,
    isDragActive,
    open: openFileSelect,
  } = useDropzone({
    onDrop,
    maxFiles: 1,
    accept: ACCEPT_ALL,
    noClick: true,
  })
  const { control, getValues, setValue, register, watch } = useForm<FormDataType>({
    mode: 'onBlur',
    reValidateMode: 'onChange',
    defaultValues: FormValue,
  })
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [requestUpsertMessage, setRequestUpsertMessage] = useState(false)
  const { uploadProgress, initUploadFiles, handleUploadFiles } = useUploadFiles('inbox')

  const attachments = watch('attachments')
  const attachmentFiles = watch('attachment_files')
  const text = watch('text')

  useEffect(() => {
    if (!mountedRef.current) return
    setValue('text', selectedMessage?.text || '')
    setValue('attachments', (selectedMessage?.attachments as TAttachment[]) || [])
    setValue('attachment_files', [])
  }, [selectedMessage])

  const handleFormSubmit = async () => {
    try {
      if (requestUpsertMessage) return
      const formData = getValues()
      if (
        (formData?.attachments.length || 0) === (selectedMessage?.attachments?.length || 0) &&
        !formData?.text &&
        !formData.attachment_files?.length
      )
        return
      setRequestUpsertMessage(true)

      const text = refineStreamMessageInputText(formData.text)
      let uploadedAttachments = []
      setIsSubmitting(true)
      if (formData.attachment_files.length > 0) {
        uploadedAttachments = await handleUploadFiles(formData.attachment_files, 'Cloudinary')
      }

      const messageData = {
        text,
        attachments: [],
      }
      messageData.attachments = formData.attachments
      uploadedAttachments.forEach((item) => {
        messageData.attachments.push({
          id: item.id,
          filename: item.filename,
          is_gif: item.is_gif,
          mimetype: item.mimetype,
          url: item.url,
          size_in_bytes: item.size_in_bytes ?? null,
          thumbnail: item.thumbnail ?? null,
          show_preview: item.show_preview,
        })
      })

      if (selectedMessage?.id) {
        messageData['id'] = selectedMessage.id
        streamChatClient
          .updateMessage(messageData)
          .then((res) => {
            if (!mountedRef.current) return
            setRequestUpsertMessage(false)
            if (!res?.message?.id) return
            setValue('text', '')
            setValue('attachments', [])
            setValue('attachment_files', [])
            setSelectedMessage(null)
            setIsSubmitting(false)
          })
          .catch((err) => {
            if (!mountedRef.current) return
          })
      } else {
        streamChatChannel
          .sendMessage(messageData)
          .then((res) => {
            if (!mountedRef.current) return
            setRequestUpsertMessage(false)
            if (!res?.message?.id) return
            setValue('text', '')
            setValue('attachments', [])
            setValue('attachment_files', [])
            setSelectedMessage(null)
            setIsSubmitting(false)
          })
          .catch((err) => {
            if (!mountedRef.current) return
          })
      }
    } catch (err: any) {
      console.log('err =====', err)
      setIsSubmitting(false)
      if (!mountedRef.current) return
      setRequestUpsertMessage(false)
    }
  }

  const handleClickEmoji = useCallback(
    (emoji) => {
      setShowEmojiPicker(false)
      const temp = insertStr(
        getValues('text') || '',
        emoji,
        selectionRef.current.start,
        selectionRef.current.end
      )
      setValue('text', temp)
    },
    [getValues, setValue]
  )

  const renderEmojiPicker = useMemo(() => {
    if (typeof window === 'undefined') return null
    return (
      <Box
        sx={{
          position: 'absolute',
          right: 8,
          top: -390,
          zIndex: 1,
        }}
      >
        <EmojiPicker onClickEmoji={handleClickEmoji} />
      </Box>
    )
  }, [handleClickEmoji])

  const renderAttachments = useMemo(() => {
    const temp = []
    const sx = {
      position: 'relative',
      borderRadius: '12px',
      border: '1px solid',
      borderColor: theme.palette.divider,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: 42,
      lineHeight: 0,
      overflow: 'hidden',
      marginRight: '8px',
      marginBottom: '8px',
      width: 112,
      height: 112,
      '& img': {
        height: '100%',
        width: '100%',
        objectFit: 'cover',
      },
      '& .MuiIconButton-root': {
        backgroundColor: '#17171a',
        color: '#8d94a3',
        position: 'absolute',
        right: '3px',
        top: '3px',
        padding: '6px',
        width: '22px',
        height: '22px',
        '&:hover': {
          display: 'inline-flex',
          backgroundColor: 'rgba(23, 23, 26, 0.75) !important',
        },
      },
    }
    const sxOther = {
      position: 'relative',
      borderRadius: '12px',
      border: '1px solid',
      borderColor: theme.palette.divider,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: 42,
      lineHeight: 0,
      overflow: 'hidden',
      marginRight: '8px',
      marginBottom: '8px',
      width: 112,
      height: 112,
      '& .MuiIconButton-root': {
        backgroundColor: '#17171a',
        color: '#8d94a3',
        position: 'absolute',
        right: '3px',
        top: '3px',
        padding: '6px',
        width: '22px',
        height: '22px',
        '&:hover': {
          display: 'inline-flex',
          backgroundColor: 'rgba(23, 23, 26, 0.75) !important',
        },
      },
    }
    const addAttachmentToRender = (
      key: string,
      attachment?: TAttachment,
      file?: File,
      handleDelete?: () => void
    ) => {
      const mimetype = attachment?.mimetype || file?.type
      const name = attachment?.filename || file?.name
      const DeleteButton = isSubmitting
        ? () => null
        : () => (
            <IconButton
              size="small"
              sx={{
                backgroundColor: isDarkTheme ? '#17171a !important' : '#f3f5f5 !important',
              }}
              aria-label="delete attachment"
              onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
                handleDelete()
              }}
            >
              <SVGCloseSmall />
            </IconButton>
          )
      if (mimetype?.includes('image')) {
        temp.push(
          <Box key={key} sx={sx}>
            <AppImg
              src={attachment?.url || URL.createObjectURL(file)}
              height={100}
              width={100}
              alt="No Attachment Image"
            />
            <DeleteButton />
          </Box>
        )
      } else if (mimetype?.includes('pdf')) {
        temp.push(
          <Box key={key} sx={{ ...sxOther }}>
            <Image
              style={{ marginTop: '15px' }}
              width="18"
              height="24"
              alt="icon"
              src={`/assets/default/images/icons/pdf.png`}
            />
            {Boolean(file) && (
              <PDFtoImage
                pdfData={file} // Use the provided PDF file data
                onThumbnailGenerated={async (base64data) => {
                  const pdfScreenshotBlob = base64toImgFile(base64data, file.name)

                  // Update your attachments array with the file object
                  file['size_in_bytes'] = file.size

                  const uploadedThumbnail = await uploadFileToCloudinaryApi(
                    pdfScreenshotBlob,
                    'image'
                  )
                  file['thumbnail'] = uploadedThumbnail.data.secure_url
                }}
              />
            )}
            {Boolean(name) && (
              <Typography
                sx={{
                  mt: '4px',
                  color: '#8D94A3',
                  width: '100%',
                  fontSize: '12px',
                  p: '12px',
                }}
              >
                {shortenFileName(name, 25)}
              </Typography>
            )}
            <DeleteButton />
          </Box>
        )
      } else if (mimetype?.includes('video') && file) {
        temp.push(
          <Box key={key} sx={sx}>
            <VideoThumbnail file={file} />
            <DeleteButton />
          </Box>
        )
      } else {
        const iconSrc = getFileTypeIcon(mimetype, name)
        temp.push(
          <Box key={key} sx={sxOther}>
            <Image
              width="18"
              height="24"
              alt="icon"
              src={`/assets/default/images/icons/${iconSrc}`}
            />
            {Boolean(name) && (
              <Typography
                sx={{
                  mt: '12px',
                  textAlign: 'center',
                  color: isDarkTheme ? '#8d94a3' : '#000000',
                  fontWeight: 400,
                  lineHeight: '16px',
                }}
              >
                {shortenFileName(name, 25)}
              </Typography>
            )}
            <DeleteButton />
          </Box>
        )
      }
    }
    for (let i = 0; i < attachments.length; i++) {
      if (attachments[i]?.mimetype && attachments[i]?.url) {
        addAttachmentToRender(`attachments-${i}`, attachments[i], undefined, () => {
          setValue(
            'attachments',
            attachments.filter((a, j) => j !== i),
            { shouldValidate: true, shouldDirty: true }
          )
        })
      }
    }

    for (let i = 0; i < attachmentFiles.length; i++) {
      addAttachmentToRender(`attachment_files-${i}`, undefined, attachmentFiles[i], () => {
        setValue(
          'attachment_files',
          attachmentFiles.filter((a, j) => j !== i),
          { shouldValidate: true, shouldDirty: true }
        )
      })
    }

    return (
      <Box
        sx={{
          display: 'flex',
          flexWrap: 'wrap',
        }}
      >
        {temp}
      </Box>
    )
  }, [attachments, attachmentFiles, isSubmitting])

  return (
    <Box
      sx={{
        position: 'relative',
        p: '8px 16px',
        boxSizing: 'border-box',
      }}
      {...getRootProps()}
    >
      <form autoComplete="off">
        <Grid container spacing={2}>
          <Grid item>
            <IconButton
              onClick={() => openFileSelect()}
              color="inherit"
              size="small"
              sx={{
                backgroundColor: theme.palette.action.disabledBackground,
                width: 40,
                height: 40,
                mt: '4px',
              }}
            >
              <SVGPlus
                styles={{
                  color: isDarkTheme ? theme.palette.action.disabled : theme.palette.text.disabled,
                }}
              />
            </IconButton>
          </Grid>
          <Grid item xs>
            <Box
              sx={{
                backgroundColor: theme.palette.action.disabledBackground,
                borderRadius: 3,
                minHeight: 48,
                p: 2,
                // pt: '5px',
                '& .MuiOutlinedInput-root': {
                  backgroundColor: 'transparent',
                  minHeight: 32,
                  my: 'auto',
                  p: 1,
                  '& .MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                },
              }}
            >
              <Grid container spacing={1}>
                <Grid item>
                  <IconButton
                    onClick={() => setShowEmojiPicker((prevValue) => !prevValue)}
                    color="inherit"
                    size="small"
                    aria-label="show emoji picker"
                    sx={{ mt: '2px' }}
                  >
                    <SentimentVerySatisfiedIcon
                      sx={{
                        color: isDarkTheme
                          ? theme.palette.action.disabled
                          : theme.palette.text.disabled,
                      }}
                    />
                  </IconButton>
                </Grid>
                <Grid item xs>
                  <input {...getInputProps()} />
                  <input type="hidden" {...register('attachments')} />
                  <input type="hidden" {...register('attachment_files')} />
                  <Controller
                    render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                      <FormControl error={Boolean(error)} className="form-control" fullWidth>
                        <TextField
                          placeholder="Write message..."
                          variant="outlined"
                          size="small"
                          disabled={disabled || isSubmitting}
                          inputRef={textInputRef}
                          value={value}
                          onChange={onChange}
                          onBlur={(e) => {
                            selectionRef.current = {
                              start: e.target.selectionStart,
                              end: e.target.selectionEnd,
                            }
                            onBlur()
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault()
                              e.stopPropagation()
                              handleFormSubmit()
                            }
                          }}
                          InputProps={{
                            maxRows: 6,
                            multiline: true,
                          }}
                          data-cy="app-messaging-new-message-text-field"
                        />
                      </FormControl>
                    )}
                    control={control}
                    name="text"
                  />
                  {showEmojiPicker && (
                    <ClickAwayListener onClickAway={(e) => setShowEmojiPicker(false)}>
                      {renderEmojiPicker}
                    </ClickAwayListener>
                  )}
                  <Box>{renderAttachments}</Box>
                </Grid>
                <Grid item>
                  <IconButton
                    color="primary"
                    size="small"
                    disabled={
                      requestUpsertMessage ||
                      (attachments.length === (selectedMessage?.attachments?.length || 0) &&
                        !attachmentFiles.length &&
                        !text)
                    }
                    onClick={(e) => handleFormSubmit()}
                    data-cy="app-messaging-new-message-submit-button"
                    sx={{ mt: '2px' }}
                  >
                    <ArrowCircleRightIcon />
                  </IconButton>
                  {isSubmitting && <LoadingIndicatorIcon />}
                </Grid>
              </Grid>
            </Box>
          </Grid>
        </Grid>
      </form>
    </Box>
  )
}

export default AppMessagingEditMessage
