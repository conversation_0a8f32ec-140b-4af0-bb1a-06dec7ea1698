import { createContext, useContext, useState } from 'react'
import { Channel } from 'stream-chat'
import { StreamMessage } from 'stream-chat-react'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'

export type TAppMessagingContext = {
  openCreateMessagingChannel: boolean
  selectedMessage: StreamMessage
  setOpenCreateMessagingChannel: (v: boolean) => void
  setSelectedMessage: (v: StreamMessage) => void
}
export const AppMessagingContext = createContext<TAppMessagingContext>({
  openCreateMessagingChannel: false,
  selectedMessage: null,
  setOpenCreateMessagingChannel: (v) => {},
  setSelectedMessage: (v) => {},
})

export function AppMessagingContextProvider({ children }) {
  const mountedRef = useMounted(true)
  const [openCreateMessagingChannel, setOpenCreateMessagingChannel] = useState(false)
  const [selectedMessage, setSelectedMessage] = useState(null)

  return (
    <AppMessagingContext.Provider
      value={{
        openCreateMessagingChannel,
        selectedMessage,
        setOpenCreateMessagingChannel: (v: boolean) => {
          if (mountedRef.current) {
            setOpenCreateMessagingChannel(v)
          }
        },
        setSelectedMessage: (v: StreamMessage) => {
          if (mountedRef.current) {
            setSelectedMessage(v)
          }
        },
      }}
    >
      {children}
    </AppMessagingContext.Provider>
  )
}

export const useAppMessagingContext = () => useContext(AppMessagingContext)
