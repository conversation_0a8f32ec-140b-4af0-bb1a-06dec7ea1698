import Box from '@mui/material/Box'
import useTheme from '@mui/material/styles/useTheme'
import React, { useEffect, useRef } from 'react'
import { MessageList, useChannelActionContext, useChannelStateContext } from 'stream-chat-react'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import AppMessagingEmptyPlaceholder from '@/memberup/components/inbox/messaging-empty-placeholder'
import AppMessageListItem from '@/memberup/components/inbox/messaging-message-list-item'
import { selectUser } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const AppMessagingMessageList: React.FC = () => {
  const mountedRef = useMounted(true)
  const { channel, loading, loadingMore } = useChannelStateContext()
  const { jumpToLatestMessage } = useChannelActionContext()
  const isEmpty = !channel?.state?.messages?.find((item) => !item.deleted_at)
  const isScrollingUpRef = useRef(false)
  const scrollPosition = useRef(0)
  const theme = useTheme()
  const user = useAppSelector((state) => selectUser(state))

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollPosition = document.getElementsByClassName('str-chat__list')?.[0]?.scrollTop || 0
      isScrollingUpRef.current = currentScrollPosition < scrollPosition.current
      scrollPosition.current = currentScrollPosition
    }

    const streamChatListEle = document.getElementsByClassName('str-chat__list')?.[0]
    if (streamChatListEle) {
      streamChatListEle.addEventListener('scroll', handleScroll)
    }

    return () => {
      if (streamChatListEle) {
        streamChatListEle.removeEventListener('scroll', handleScroll)
      }
    }
  }, [])

  useEffect(() => {
    if (!mountedRef.current || loading || loadingMore || isScrollingUpRef.current) return
    jumpToLatestMessage()
      .then((value) => {
        setTimeout(() => {
          setTimeout(() => {
            const streamChatListEle = document.getElementsByClassName('str-chat__list')?.[0]
            if (streamChatListEle && !isScrollingUpRef.current) {
              streamChatListEle.scrollTo(0, streamChatListEle.scrollHeight + 1000) // Add 100 pixels
            }
          }, 100) // Add delay
        }, 800)
      })
      .catch((err) => {})
  }, [channel, loading, loadingMore])

  useEffect(() => {
    setTimeout(() => {
      const streamChatListEle = document.getElementsByClassName('str-chat__list')?.[0]
      if (!streamChatListEle) return
      const lastMessage = channel.state?.messages?.[channel.state?.messages?.length - 1]
      if (!lastMessage) return
      const isOwnMessage = lastMessage?.user?.id === user?.id
      const isAtBottom = streamChatListEle.scrollHeight - streamChatListEle.scrollTop === streamChatListEle.clientHeight

      if (!isAtBottom && isOwnMessage) {
        streamChatListEle.scrollTo(0, streamChatListEle.scrollHeight)
      }
    }, 100)
  }, [channel?.state?.messages])

  return (
    <Box
      sx={{
        position: 'relative',
        padding: '8px 16px',
        height: '100%',
        '& .str-chat__list': {
          backgroundColor: 'transparent!important',
          pt: '0!important',
          pl: '8px!important',
          pr: '8px!important',
          height: isEmpty ? 0 : '100%',
          '& .str-chat__reverse-infinite-scroll': {
            pt: '0!important',
            pl: '0!important',
            pr: '0!important',
          },
        },
        '& .str-chat__li--single, & .str-chat__li--top, & .str-chat__li--single': {
          py: '0!important',
        },
        '& .str-chat__message--system__text p': {
          maxWidth: '50%',
          fontSize: '10px',
          fontFamily: 'Graphik Semibold',
          fontWeight: 500,
          color: theme.palette.mode == 'dark' ? 'rgba(255,255,255,0.4)' : 'rgba(0,0,0,0.4)',
        },
        '& .str-chat__message--system__date': {
          display: 'none',
        },
        '& .str-chat__message--system__line': {
          backgroundColor: 'rgba(141, 148, 163, 0.16)',
        },
      }}
    >
      {isEmpty && <AppMessagingEmptyPlaceholder isMessageEmpty={true} />}
      <MessageList
        disableDateSeparator={true}
        hideDeletedMessages={true}
        groupStyles={(message, prevMessage, nextMessage, noGroupByUser) => {
          const messageCreatedAt = new Date(message.created_at)
          const prevMessageCreatedAt = prevMessage && new Date(prevMessage.created_at)
          const nextMessageCreatedAt = nextMessage && new Date(nextMessage.created_at)
          if (
            !prevMessageCreatedAt ||
            messageCreatedAt.getDate() !== prevMessageCreatedAt.getDate() ||
            messageCreatedAt.getMonth() !== prevMessageCreatedAt.getMonth() ||
            messageCreatedAt.getFullYear() !== prevMessageCreatedAt.getFullYear()
          ) {
            message['date_line'] = true
            message['is_first'] = true
          }

          const isSameDateAsNextMessage =
            message.user?.id === nextMessage?.user?.id &&
            messageCreatedAt.getDate() === nextMessageCreatedAt.getDate() &&
            messageCreatedAt.getMonth() === nextMessageCreatedAt.getMonth() &&
            messageCreatedAt.getFullYear() === nextMessageCreatedAt.getFullYear()
          return message.user?.id === prevMessage?.user?.id
            ? isSameDateAsNextMessage
              ? 'middle'
              : 'bottom'
            : isSameDateAsNextMessage
              ? 'top'
              : 'single'
        }}
        Message={AppMessageListItem}
      />
    </Box>
  )
}

export default AppMessagingMessageList
