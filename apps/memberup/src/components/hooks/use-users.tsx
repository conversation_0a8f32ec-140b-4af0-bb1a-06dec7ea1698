import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { deleteUserApi, getUsersApi } from '@memberup/shared/src/services/apis/user.api'
import { IUser } from '@memberup/shared/src/types/interfaces'
import { useEffect, useState } from 'react'
import { useChatContext } from 'stream-chat-react'
import { useAppSelector } from '@/memberup/store/hooks'
import { selectMembership } from '@/memberup/store/features/membershipSlice'

const useUsers = (props: {
  filter: any
  limit: number
  orderBy?: any
  isPagination?: boolean
  getProgress?: boolean
}) => {
  const membership = useAppSelector((state) => selectMembership(state))
  const { filter: initialFilter, limit, orderBy, isPagination, getProgress = false } = props
  const mountedRef = useMounted(true)
  const { client } = useChatContext()
  const [pagination, setPagination] = useState({
    page: 0,
    limit,
  })
  const [filter, setFilter] = useState<any>(initialFilter)
  const [requestGetUsers, setRequestGetUsers] = useState(false)
  const [requestDeleteUser, setRequestDeleteUser] = useState(false)
  const [users, setUsers] = useState<{ total: number; docs: IUser[] }>({
    total: 1,
    docs: [],
  })

  const MAX_IDS_ALLOWED = 100

  useEffect(() => {
    if (!mountedRef.current || requestGetUsers) return
    setRequestGetUsers(true)
    getUsersApi(
      {
        where: filter ? JSON.stringify(filter) : undefined,
        take: pagination.limit,
        skip: pagination.limit * pagination.page,
        orderBy: orderBy ? JSON.stringify(orderBy) : undefined,
      },
      getProgress
    )
      .then(async (result) => {
        if (!mountedRef.current) return
        const docs = result.data?.data?.docs || []
        if (docs.length && client) {
          const sortedById = [...docs]
          sortedById.sort((a, b) => a.id.localeCompare(b.id))
          let referenceId = sortedById[0]?.id

          let getStreamUsers = []

          const userIds = docs.map((u) => u.id)
          const shouldRequestAll = userIds.length > MAX_IDS_ALLOWED

          let filterConditions: any = { id: { $in: userIds }, status: 'active' }
          if (shouldRequestAll) {
            let chunkOffset = 0
            let chunkSize = 100
            let hasMore = true
            filterConditions = {
              id: { $gte: referenceId },
              teams: { $in: [membership.id] },
              status: 'active',
            }
            while (hasMore) {
              const usersResponse = await client.queryUsers(
                filterConditions,
                { id: 1 }, // sort
                { limit: 100 }
              )
              getStreamUsers = getStreamUsers.concat(usersResponse.users)
              if (!shouldRequestAll) {
                break
              }
              if (usersResponse.users.length < 100) {
                hasMore = false
              }
              chunkOffset += chunkSize
              referenceId = getStreamUsers[getStreamUsers.length - 1]?.id
            }
          } else {
            const usersResponse = await client.queryUsers(
              filterConditions,
              { id: 1 }, // sort
              { limit: 100 }
            )
            getStreamUsers = getStreamUsers.concat(usersResponse.users)
          }

          for (const cu of getStreamUsers) {
            const u = docs.find((d) => d.id === cu.id)
            if (u) {
              u.online_status = cu.online
              u.last_active = cu.last_active
            }
          }
        }

        if (isPagination || !pagination.page) {
          setUsers({
            total: result.data?.data?.total || 0,
            docs: docs,
          })
        } else {
          setUsers((prevValue) => ({
            total: result.data?.data?.total || 0,
            docs: [...prevValue.docs, ...docs],
          }))
        }
      })
      .catch((err) => {})
      .finally(() => {
        if (!mountedRef.current) return
        setRequestGetUsers(false)
      })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination, filter])

  const handleDeleteUser = (id: string, ban?: boolean) => {
    setRequestDeleteUser(true)
    deleteUserApi(id, ban)
      .then(async (result) => {
        if (!mountedRef.current) return
        setUsers({
          total: users.total - 1,
          docs: users.docs.filter((item) => item.id !== id),
        })
      })
      .catch((err) => {})
      .finally(() => {
        if (!mountedRef.current) return
        setRequestDeleteUser(false)
      })
  }

  return {
    users,
    pagination,
    setPagination,
    handleDeleteUser,
    requestGetUsers,
    requestDeleteUser,
    setUsers,
    setFilter,
  }
}

export default useUsers
