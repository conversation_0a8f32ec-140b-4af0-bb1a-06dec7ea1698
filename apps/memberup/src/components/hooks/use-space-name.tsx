import { useMemo } from 'react'

import { selectChannels } from '@/memberup/store/features/spaceSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const useSpaceName = (slug: string) => {
  const spaces = useAppSelector((state) => selectChannels(state))

  return useMemo(() => {
    return (spaces?.docs || []).find((s) => s.slug === slug)?.name || ''
  }, [spaces, slug])
}

export default useSpaceName
