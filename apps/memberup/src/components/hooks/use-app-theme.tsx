import useTheme from '@mui/material/styles/useTheme'
import useMediaQuery from '@mui/material/useMediaQuery'

import { hexToRGBA } from '@memberup/shared/src/libs/color'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'

const useAppTheme = () => {
  const theme = useTheme()
  const isDarkTheme = theme.palette.mode === THEME_MODE_ENUM.dark
  const isXsMobile = useMediaQuery(theme.breakpoints.down('xsMobile'))
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  const isLgDown = useMediaQuery(theme.breakpoints.down('lg'))
  const isContainerWidthDown = useMediaQuery(theme.breakpoints.down('containerWidth'))

  let secondaryBackgroundColor =
    theme.components.MuiCssBaseline.styleOverrides['body']['& .background-gradient01']['background']
  const regex =
    /#(?:[a-fA-F0-9]{6}|[a-fA-F0-9]{3})|rgba?\(\s*\d{1,3}\s*,\s*\d{1,3}\s*,\s*\d{1,3}(?:\s*,\s*[0-9.]+)?\s*\)/g
  const colors = secondaryBackgroundColor.match(regex)
  let newGradient = secondaryBackgroundColor
  if (colors) {
    const newAlpha = 0.38
    const rgbaColors = colors.map((color) => {
      const replaceAlphaRGBA = (rgbaString, newAlpha) => {
        // This regex matches rgba strings and captures each number
        const regex = /rgba\((\d{1,3}),(\d{1,3}),(\d{1,3}),(\d*\.?\d+)\)/i
        return rgbaString.replace(regex, `rgba($1,$2,$3,${newAlpha})`)
      }
      if (color.includes('#')) {
        return hexToRGBA(color, newAlpha)
      }
      return replaceAlphaRGBA(color, newAlpha)
    })
    if (rgbaColors.length === 2) {
      newGradient = `linear-gradient(90deg, ${rgbaColors[0]} 0%, ${rgbaColors[1]} 100%)`
    } else if (rgbaColors.length === 3) {
      newGradient = `linear-gradient(90deg, ${rgbaColors[0]} 0%, ${rgbaColors[1]} 50%, ${rgbaColors[2]} 100%)`
    }
  }

  const isSmDown = useMediaQuery(theme.breakpoints.down('sm'))
  const isLightTheme = !isDarkTheme

  return {
    theme,
    newGradient,
    secondaryBackgroundColor,
    isSmDown,
    isDarkTheme,
    isLightTheme,
    isMobile,
    isContainerWidthDown,
    isLgDown,
    isXsMobile,
  }
}
export default useAppTheme
