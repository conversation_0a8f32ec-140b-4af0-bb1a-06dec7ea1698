import { CSSObject } from '@mui/material/styles'
import { makeStyles } from '@mui/styles'

const useStyles = makeStyles((theme) => ({
  form: {
    '& .MuiOutlinedInput-root': {
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: 'transparent',
        borderRadius: 12,
        backgroundColor: (theme.components.MuiCssBaseline.styleOverrides as CSSObject).body['& .background-color18'][
          'backgroundColor'
        ],
      },
    },
  },
  textEditorWrapper: {
    borderColor: 'transparent',
    borderRadius: 12,
    backgroundColor: (theme.components.MuiCssBaseline.styleOverrides as CSSObject).body['& .background-color18'][
      'backgroundColor'
    ],
    height: 200,
  },
}))

const useDialogFormClasses = () => {
  return useStyles()
}

export default useDialogFormClasses
