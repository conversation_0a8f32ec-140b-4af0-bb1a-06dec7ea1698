import { signOut, useSession } from 'next-auth/react'
import { useRouter } from 'next/router'
import { useEffect, useMemo, useRef, useState } from 'react'

import { getActiveUserApi } from '@memberup/shared/src/services/apis/user.api'
import { USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import { checkIsPaidMembership } from '@/memberup/libs/utils'
import { AUTH_ROUTES, GLOBAL_ROUTES, NO_REDIRECT_ROUTES, ROUTES } from '@/memberup/settings/router'
import {
  selectMembership,
  selectMembershipError,
  selectMembershipSetting,
} from '@/memberup/store/features/membershipSlice'
import { selectRequestGetActiveUser, selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const CHECK_USER_INTERVAL_MS = 15000

const useCheckRoute = () => {
  const router = useRouter()
  const membership = useAppSelector((state) => selectMembership(state))
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const membershipError = useAppSelector((state) => selectMembershipError(state))
  const requestGetActiveUser = useAppSelector((state) => selectRequestGetActiveUser(state))
  const userProfile = useAppSelector((state) => selectUserProfile(state))
  const [mounted, setMounted] = useState(false)
  const { status: sessionStatus } = useSession()
  const { isCurrentUserAdmin, user } = useCheckUserRole()
  const intervalRef = useRef(null)

  const isCreatorRoute =
    ROUTES[router.pathname]?.isCreatorRoute ||
    (router.pathname.startsWith('/settings') && !router.pathname.includes('/settings/account'))
  const isChatRoute = ROUTES[router.pathname]?.chatRequired || false

  useEffect(() => {
    setMounted(true)
    return () => {
      setMounted(false)
    }
  }, [])

  const shouldRedirectToSignupPayment = useMemo(() => {
    if (user?.status === USER_STATUS_ENUM.active || isCurrentUserAdmin !== false) return false
    const isPaidMembership = checkIsPaidMembership(membershipSetting)
    const isAuthSetupRoute = router.pathname === '/auth/signup/payment'
    return isPaidMembership && !isAuthSetupRoute
  }, [
    membershipSetting?.stripe_connect_account,
    membershipSetting?.stripe_prices,
    user?.status,
    isCurrentUserAdmin,
    router.pathname,
  ])

  const isEnabledRoute = useMemo(() => {
    const pathname = router.pathname || ''
    if (GLOBAL_ROUTES.includes(pathname)) return true
    if (!mounted || !membership?.id || requestGetActiveUser) return false
    if (AUTH_ROUTES.includes(pathname)) return !user?.id
    if (!ROUTES[pathname]?.authRequired) return true
    if (shouldRedirectToSignupPayment) return false
    if (!userProfile?.id) return false
    return isCreatorRoute ? isCurrentUserAdmin : true
  }, [
    mounted,
    requestGetActiveUser,
    router.pathname,
    isCurrentUserAdmin,
    isCreatorRoute,
    shouldRedirectToSignupPayment,
    membership?.id,
    user?.id,
    userProfile?.id,
  ])

  useEffect(() => {
    if (intervalRef.current) {
      return
    }
    const pathname = router.pathname
    intervalRef.current = setInterval(() => {
      if (!ROUTES[pathname]?.authRequired) {
        return
      }
      getActiveUserApi({})
        .then((res: any) => {
          if (res.data.success) {
            const data = res.data.data
            if (data.user.status === USER_STATUS_ENUM.deleted || data.user.status === USER_STATUS_ENUM.banned) {
              signOut({ redirect: false }).then(() => {
                clearInterval(intervalRef.current)
                intervalRef.current = null
              })
            }
          }
        })
        .catch((e) => {
          // signOut({ redirect: false }).then(() => {
          //   clearInterval(intervalRef.current)
          //   intervalRef.current = null
          // })
        })
    }, CHECK_USER_INTERVAL_MS)
    return () => {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }, [sessionStatus, router.pathname])

  useEffect(() => {
    const pathname = router.pathname || ''
    if (!mounted || requestGetActiveUser) return
    if (membershipError) {
      router.push(`/404`)
    } else if (membership?.id && !isEnabledRoute) {
      if (shouldRedirectToSignupPayment) {
        router.replace({ pathname: '/auth/signup/payment' }, undefined, { shallow: false })
      } else if (!NO_REDIRECT_ROUTES.includes(pathname) && pathname !== '/community' && !pathname) {
        router.replace({ pathname: '/community' }, undefined, { shallow: false })
      }
    }
  }, [
    mounted,
    membershipError,
    isEnabledRoute,
    membership?.id,
    requestGetActiveUser,
    shouldRedirectToSignupPayment,
    router.pathname,
  ])

  return {
    isChatRoute,
    isCreatorRoute,
    isEnabledRoute,
  }
}

export default useCheckRoute
