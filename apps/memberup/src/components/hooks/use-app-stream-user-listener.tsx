import { updateMember } from '@/memberup/store/features/memberSlice'
import { selectUser } from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { useEffect } from 'react'
import { Channel } from 'stream-chat'
import { useChatContext } from 'stream-chat-react'

const useAppStreamUserListener = (streamChatChannel: Channel) => {
  const mountedRef = useMounted(true)
  const dispatch = useAppDispatch()
  const user = useAppSelector((state) => selectUser(state))
  const { client: streamChatClient } = useChatContext()

  useEffect(() => {
    let streamChatClientEventListner
    let streamChatChannelEventListener

    if (mountedRef.current) {
      streamChatClientEventListner = streamChatClient?.on('user.updated', (e) => {
        if ((e.user.teams || []).includes(user.membership_id)) {
          streamChatChannel?.sendEvent({
            type: 'app_member_updated' as any,
            updated_member: e.user,
          })
        }
      })
      streamChatChannelEventListener = streamChatChannel?.on((e) => {
        switch (e.type) {
          case 'app_member_updated' as any:
            if (e.updated_member) {
              const updatedMember = e.updated_member
              dispatch(
                updateMember({
                  id: updatedMember['id'],
                  online: updatedMember['online'],
                  profile: {
                    image: updatedMember['image'],
                    image_crop_area: updatedMember['image_crop_area'],
                  },
                })
              )
            }
            return
        }
      })
    }

    return () => {
      streamChatClientEventListner?.unsubscribe?.()
      streamChatChannelEventListener?.unsubscribe?.()
    }
  }, [streamChatChannel])

  return {}
}

export default useAppStreamUserListener
