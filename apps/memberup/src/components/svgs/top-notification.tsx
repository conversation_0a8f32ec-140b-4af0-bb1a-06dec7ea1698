import React from 'react'

const SVGTopNotification: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 16} height={height || 17} viewBox="0 0 16 17" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-4.000000, -4.000000)" fill="#8D94A3" fillRule="nonzero">
          <path
            d="M15,18 C15,19.6568542 13.6568542,21 12,21 C10.3431458,21 9,19.6568542 9,18 L5,18 C4.44771525,18 4,17.5522847 4,17 C4,16.4477153 4.44771525,16 5,16 L6,16 L6,10 C6,6.6862915 8.6862915,4 12,4 C15.3137085,4 18,6.6862915 18,10 L18,16 L19,16 C19.5522847,16 20,16.4477153 20,17 C20,17.5522847 19.5522847,18 19,18 L15,18 Z M13,18 L11,18 C11,18.5522847 11.4477153,19 12,19 L12.1166211,18.9932723 C12.6139598,18.9355072 13,18.5128358 13,18 Z M12,6 C9.85780461,6 8.10892112,7.68396847 8.00489531,9.80035966 L8,10 L8,16 L16,16 L16,10 C16,7.85780461 14.3160315,6.10892112 12.1996403,6.00489531 L12,6 Z"
            id="Combined-Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGTopNotification
