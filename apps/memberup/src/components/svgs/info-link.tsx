import React from 'react'

const SVGInfoLink: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={'14px' || width} height={'14px' || height} viewBox="0 0 14 14" style={{ ...styles }}>
      <g id="🟣-Home-Feed---About-Section---Spark-Feed" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g
          id="🌑-Home---Public---Creators-View---Empty-State---Collapsed-Spark-Copy"
          transform="translate(-1102, -374)"
          fill="currentColor"
          fillRule="nonzero"
        >
          <g id="Group-2-Copy" transform="translate(1089, 98)">
            <g id="Group-3" transform="translate(13, 273)">
              <g id="9104220_link_permalink_chain_hyperlink_connect_icon" transform="translate(0, 3)">
                <path
                  d="M9.90178571,0 C8.77678571,0 7.8125,0.401785714 7.00892857,1.20535714 L5.24107143,2.97321429 C4.91964286,3.29464286 4.91964286,3.77678571 5.24107143,4.09821429 C5.5625,4.41964286 6.04464286,4.41964286 6.36607143,4.09821429 L8.13392857,2.33035714 C9.09821429,1.36607143 10.7053571,1.36607143 11.6696429,2.33035714 C12.1517857,2.8125 12.3928571,3.45535714 12.3928571,4.09821429 C12.3928571,4.74107143 12.1517857,5.38392857 11.6696429,5.86607143 L9.90178571,7.63392857 C9.58035714,7.95535714 9.58035714,8.4375 9.90178571,8.75892857 C10.0625,8.91964286 10.3035714,9 10.4642857,9 C10.625,9 10.8660714,8.91964286 11.0267857,8.75892857 L12.7946429,6.99107143 C13.5982143,6.1875 14,5.22321429 14,4.09821429 C14,2.97321429 13.5982143,2.00892857 12.7946429,1.20535714 C11.9910714,0.401785714 11.0267857,0 9.90178571,0 Z"
                  id="Path"
                ></path>
                <path
                  d="M4.09821429,6.37837838 C4.41964286,6.05405405 4.41964286,5.56756757 4.09821429,5.24324324 C3.77678571,4.91891892 3.29464286,4.91891892 2.97321429,5.24324324 L1.20535714,6.94594595 C0.401785714,7.75675676 0,8.72972973 0,9.86486486 C0,11 0.401785714,11.972973 1.20535714,12.7837838 C2.00892857,13.5945946 2.97321429,14 4.09821429,14 C5.22321429,14 6.1875,13.5945946 6.99107143,12.7837838 L8.75892857,11 C9.08035714,10.6756757 9.08035714,10.1891892 8.75892857,9.86486486 C8.4375,9.54054054 7.95535714,9.54054054 7.63392857,9.86486486 L5.86607143,11.6486486 C4.90178571,12.6216216 3.29464286,12.6216216 2.33035714,11.6486486 C1.84821429,11.1621622 1.60714286,10.5135135 1.60714286,9.86486486 C1.60714286,9.21621622 1.84821429,8.56756757 2.33035714,8.08108108 L4.09821429,6.37837838 Z"
                  id="Path"
                ></path>
                <path
                  d="M4.23809524,9.76190476 C4.3968254,9.92063492 4.63492063,10 4.79365079,10 C4.95238095,10 5.19047619,9.92063492 5.34920635,9.76190476 L8.76190476,6.34920635 C9.07936508,6.03174603 9.07936508,5.55555556 8.76190476,5.23809524 C8.44444444,4.92063492 7.96825397,4.92063492 7.65079365,5.23809524 L4.23809524,8.65079365 C3.92063492,8.88888889 3.92063492,9.44444444 4.23809524,9.76190476 Z"
                  id="Path"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGInfoLink
