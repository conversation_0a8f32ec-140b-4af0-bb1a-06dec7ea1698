import React from 'react'

const SVGUnsplash: React.FC<{
  width?: number
  height?: number
  styles?: any
}> = ({ width, height, styles }) => {
  return (
    <svg width={width || '12px'} height={height || '12px'} viewBox="0 0 12 12" style={{ ...styles }}>
      <g id="🎬-Content---Index---Customize" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g
          id="Content---Index---Customize---About---Page-Header---Text---Empty"
          transform="translate(-893, -206)"
          fill="currentColor"
        >
          <g id="item-/-toggle" transform="translate(855, 184)">
            <g id="Group-11" transform="translate(32, 16)">
              <g id="Group-12" transform="translate(4, 4)">
                <g id="Logo_of_Unsplash" transform="translate(2, 2)">
                  <path
                    d="M3.75,3.375 L3.75,0 L8.25,0 L8.25,3.375 L3.75,3.375 Z M8.25,5.25 L12,5.25 L12,12 L0,12 L0,5.25 L3.75,5.25 L3.75,8.625 L8.25,8.625 L8.25,5.25 Z"
                    id="Shape"
                  ></path>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGUnsplash
