import React from 'react'

const SVGStayhome: React.FC<{
  width?: number
  height?: number
  strokeWidth?: number
}> = ({ width, height, strokeWidth }) => {
  return (
    <svg width={width || 20} height={width || 20} viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-2.000000, -2.000000)">
          <g transform="translate(2.000000, 2.000000)">
            <path
              d="M15.3125,20 L4.6875,20 C2.1028125,20 0,17.8968825 0,15.3118201 L0,8.73778082 C0,7.28479667 0.689648438,5.89127427 1.84476562,5.0100918 L7.15726562,0.957590041 C8.83105469,-0.31919668 11.1689453,-0.31919668 12.8427344,0.957590041 L18.1552344,5.0100918 C19.3103516,5.8912352 20,7.2847576 20,8.73778082 L20,15.3118201 C20,17.8968825 17.8971875,20 15.3125,20 Z M10,1.56174015 C9.33128906,1.56174015 8.66285156,1.77450538 8.10484375,2.20011398 L2.79234375,6.25261574 C2.02226563,6.84008374 1.5625,7.76908565 1.5625,8.73778082 L1.5625,15.3118201 C1.5625,17.035195 2.964375,18.4372734 4.6875,18.4372734 L15.3125,18.4372734 C17.035625,18.4372734 18.4375,17.035195 18.4375,15.3118201 L18.4375,8.73778082 C18.4375,7.76912472 17.9777344,6.84008374 17.2076562,6.2526548 L11.8951562,2.20015305 C11.3373047,1.77462259 10.6685547,1.56174015 10,1.56174015 Z M10,15.4262508 C9.47589844,15.4262508 8.95199219,15.2625552 8.51464844,14.9350467 C6.71,13.5836007 5.73785156,11.9649676 5.70328125,10.2541335 C5.703125,10.2488593 5.703125,10.2435851 5.703125,10.23835 C5.703125,8.83513869 6.84214844,7.69356689 8.2421875,7.69356689 C8.92375,7.69356689 9.54347656,7.96352791 10,8.40218528 C10.4565625,7.96352791 11.07625,7.69356689 11.7578125,7.69356689 C13.1578516,7.69356689 14.296875,8.83513869 14.296875,10.23835 C14.296875,10.2436242 14.296875,10.2488984 14.2967188,10.2541335 C14.2621484,11.9649676 13.29,13.5836007 11.4853125,14.9350467 C11.048125,15.262477 10.5239844,15.4262508 10,15.4262508 L10,15.4262508 Z M7.265625,10.2310833 C7.30082031,11.7582579 8.47296875,12.951556 9.45117188,13.6841231 C9.77433594,13.9261504 10.2257422,13.9261504 10.5487891,13.6841231 C11.5269922,12.951595 12.6991406,11.758297 12.7343359,10.2310833 C12.7304297,9.69288027 12.2938672,9.25621538 11.7577734,9.25621538 C11.2192969,9.25621538 10.7812109,9.69436486 10.7812109,10.2329195 C10.7812109,10.6644665 10.4314453,11.0142828 9.99996094,11.0142828 C9.56847656,11.0142828 9.21871094,10.6644665 9.21871094,10.2329195 C9.21871094,9.69436486 8.780625,9.25621538 8.24214844,9.25621538 C7.70609375,9.25621538 7.26953125,9.69288027 7.265625,10.2310833 Z"
              fill="currentColor"
              fillRule="nonzero"
            ></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGStayhome
