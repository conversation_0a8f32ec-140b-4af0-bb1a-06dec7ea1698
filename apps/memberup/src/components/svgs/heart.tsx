import React from 'react'

const SVGHeart: React.FC<{ width?: number; height?: number; padding?: number }> = ({ width, height, padding = 0 }) => {
  return (
    <svg
      width={width || 16}
      height={height || 14}
      viewBox={`${padding} ${padding} ${16 - padding * 2} ${14 - padding * 2}`}
      version="1.1"
    >
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/20px/heart" transform="translate(-2, -3)" fill="currentColor">
          <path
            d="M16.5224,4.09757148 C17.4752,4.99751944 18,6.19506924 18,7.47039232 C18,8.7457154 17.4752,9.9432652 16.5224,10.8424196 L10,17 L3.4776,10.8424196 C2.5248,9.9432652 2,8.7457154 2,7.47039232 C2,6.19506924 2.5248,4.99751944 3.4776,4.09757148 C5.2536,2.42068431 8.42809207,2.6266013 10,5.09901164 C11.594914,2.59725811 14.7464,2.42068431 16.5224,4.09757148 Z M15.149346,5.55178014 C14.2259589,4.67992428 12.6650036,4.83063386 11.7918998,6.02005911 L11.6864403,6.17414842 L10.4198821,8.16084898 C10.2714367,8.3936978 9.96233662,8.46212028 9.7294878,8.31367487 C9.6678374,8.2743716 9.61555662,8.22202609 9.57632966,8.1603271 L8.31223066,6.17206086 C7.47977367,4.86271239 5.80173086,4.65377978 4.85091263,5.55153585 C4.2949139,6.07669321 4,6.74981837 4,7.47039232 C4,8.12538112 4.24365168,8.7409509 4.70548626,9.24148536 L4.85055666,9.38811903 L9.999,14.249 L15.1497278,9.38785053 C15.6546857,8.91132338 15.9442426,8.31197739 15.9927161,7.66563057 L16,7.47039232 C16,6.81532509 15.7562695,6.19947174 15.2942248,5.69835369 L15.149346,5.55178014 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGHeart
