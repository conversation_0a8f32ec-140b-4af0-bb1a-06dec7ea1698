import React from 'react'

const SVGDownload: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({ width, height }) => {
  return (
    <svg width={width || '16px'} height={height || '16px'} viewBox="0 0 16 16" version="1.1">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/20px/download" transform="translate(-2, -2)" fill="currentColor">
          <path
            d="M17,12 C17.5128358,12 17.9355072,12.3860402 17.9932723,12.8833789 L18,13 L18,15 C18,16.5976809 16.75108,17.9036609 15.1762728,17.9949073 L15,18 L5,18 C3.40231912,18 2.09633912,16.75108 2.00509269,15.1762728 L2,15 L2,13 C2,12.4477153 2.44771525,12 3,12 C3.51283584,12 3.93550716,12.3860402 3.99327227,12.8833789 L4,13 L4,15 C4,15.5128358 4.38604019,15.9355072 4.88337887,15.9932723 L5,16 L15,16 C15.5128358,16 15.9355072,15.6139598 15.9932723,15.1166211 L16,15 L16,13 C16,12.4477153 16.4477153,12 17,12 Z M10,2 C10.5522847,2 11,2.44771525 11,3 L11,10.585 L12.2928932,9.29289322 C12.6533772,8.93240926 13.2206082,8.90467972 13.6128994,9.20970461 L13.7071068,9.29289322 C14.0675907,9.65337718 14.0953203,10.2206082 13.7902954,10.6128994 L13.7071068,10.7071068 L10.7071068,13.7071068 C10.3466228,14.0675907 9.77939176,14.0953203 9.38710056,13.7902954 L9.29289322,13.7071068 L6.29289322,10.7071068 C5.90236893,10.3165825 5.90236893,9.68341751 6.29289322,9.29289322 C6.65337718,8.93240926 7.22060824,8.90467972 7.61289944,9.20970461 L7.70710678,9.29289322 L9,10.585 L9,3 C9,2.44771525 9.44771525,2 10,2 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGDownload
