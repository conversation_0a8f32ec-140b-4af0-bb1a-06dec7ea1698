import React from 'react'

const SVGEditNew: React.FC<{ width?: number; height?: number }> = ({ width, height }) => {
  return (
    <svg width={width || '16px'} height={height || '16px'} viewBox="0 0 16 16" version="1.1">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/20px/edit" transform="translate(-2, -2)" fill="currentColor">
          <path
            d="M2,14 L2,17 C2,17.5522847 2.44771525,18 3,18 L6,18 C6.26521649,18 6.5195704,17.8946432 6.70710678,17.7071068 L17.7071068,6.70710678 C18.0976311,6.31658249 18.0976311,5.68341751 17.7071068,5.29289322 L14.7071068,2.29289322 C14.3165825,1.90236893 13.6834175,1.90236893 13.2928932,2.29289322 L2.29289322,13.2928932 C2.10535684,13.4804296 2,13.7347835 2,14 Z M4,14.4142136 L14,4.41421356 L15.5857864,6 L5.58578644,16 L4,16 L4,14.4142136 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGEditNew
