import React from 'react'

const SVGMessage: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg
      width={width || 16}
      height={width || 16}
      viewBox="0 0 16.0009712 16.0011668"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="🔶-Symbols" stroke="none" strokeWidth={strokeWidth || 1} fill="currentColor" fillRule="evenodd">
        <g id="Icons/feed/24px/comment-outline-alt" transform="translate(-1.999, -2)" fill="currentColor">
          <path
            d="M9.99902875,2 C14.4171926,2 17.9990288,5.58163076 17.9990288,9.99959852 L17.9917166,10.3429334 C17.9479209,11.3695994 17.7080893,12.3646756 17.2910254,13.2864807 L17.1830288,13.512 L17.947403,16.250408 L17.9753619,16.3692919 C18.1634952,17.3559157 17.243956,18.2268865 16.2488272,17.9486374 L13.5110288,17.184 L13.2836802,17.2924325 C12.260281,17.755095 11.1464823,17.999197 9.99902875,17.999197 C5.58086495,17.999197 1.99902875,14.4175663 1.99902875,9.99959852 C1.99902875,5.58163076 5.58086495,2 9.99902875,2 Z M9.99902875,4 C6.68540877,4 3.99902875,6.68622594 3.99902875,9.99959852 C3.99902875,13.3129711 6.68540877,15.999197 9.99902875,15.999197 C11.0320314,15.999197 12.0257824,15.7362574 12.9126126,15.2418996 L13.2719885,15.0415678 L15.7250288,15.726 L15.0397115,13.2749784 L15.2403143,12.9153719 C15.7355235,12.0276454 15.9990288,11.0329163 15.9990288,9.99959852 C15.9990288,6.68622594 13.3126487,4 9.99902875,4 Z M9.99902875,11 C10.5513135,11 10.9990288,11.4477153 10.9990288,12 C10.9990288,12.5522847 10.5513135,13 9.99902875,13 L7.99902875,13 C7.446744,13 6.99902875,12.5522847 6.99902875,12 C6.99902875,11.4477153 7.446744,11 7.99902875,11 Z M11.9990288,7 C12.5513135,7 12.9990288,7.44771525 12.9990288,8 C12.9990288,8.55228475 12.5513135,9 11.9990288,9 L7.99902875,9 C7.446744,9 6.99902875,8.55228475 6.99902875,8 C6.99902875,7.44771525 7.446744,7 7.99902875,7 Z"
            id="Combined-Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGMessage
