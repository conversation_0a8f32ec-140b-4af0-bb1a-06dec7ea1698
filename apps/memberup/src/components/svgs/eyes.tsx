import React from 'react'

const <PERSON><PERSON>ye: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={width || '16px'} height={height || '12px'} style={{ ...styles }} viewBox="0 0 16 12">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/20px/visibility-on" transform="translate(-2, -4)" fill="currentColor">
          <path
            d="M10,4 C13.1992868,4 15.7550825,5.7838584 17.596248,9.21473976 L17.7667614,9.54120598 L18,10 L17.7667614,10.458794 C15.9141913,14.1029084 13.2992646,16 10,16 C6.80071316,16 4.24491753,14.2161416 2.40375204,10.7852602 L2.23323859,10.458794 L2,10 L2.23323859,9.54120598 C4.08580872,5.8970916 6.70073544,4 10,4 Z M10,6 C7.73671394,6 5.89056825,7.18770355 4.41039841,9.7231775 L4.25320687,10 L4.41039841,10.2768225 C5.89056825,12.8122964 7.73671394,14 10,14 C12.3441177,14 14.2407672,12.7259456 15.7467931,10 C14.2407672,7.27405445 12.3441177,6 10,6 Z M10,8 C11.1045695,8 12,8.8954305 12,10 C12,11.1045695 11.1045695,12 10,12 C8.8954305,12 8,11.1045695 8,10 C8,8.8954305 8.8954305,8 10,8 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGEye
