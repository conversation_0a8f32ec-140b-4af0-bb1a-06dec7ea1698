import React from 'react'

const SVGAddMessage: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 16} height={height || 16} viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1144.000000, -200.000000)">
          <g transform="translate(1130.000000, 64.000000)">
            <g transform="translate(4.000000, 124.000000)">
              <g transform="translate(8.000000, 10.000000)">
                <rect fillOpacity="0" fill="#FFFFFF" x="0" y="0" width="20" height="20"></rect>
                <g transform="translate(3.000000, 3.000000)" stroke="#FFFFFF" strokeLinejoin="round" strokeWidth="2">
                  <path d="M7,0 C3.13410811,0 0,3.13392835 0,6.99959852 C0,8.23531143 0.323135135,9.39459629 0.885405405,10.4025385 L0.0147567568,13.5194408 C-0.0647027027,13.8032083 0.196756757,14.0650311 0.480918919,13.9855762 L3.59951351,13.1153558 C4.60675676,13.6768372 5.76497297,13.999197 7,13.999197 C10.8658919,13.999197 14,10.8652687 14,6.99959852 C14,3.13392835 10.8658919,0 7,0 Z"></path>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGAddMessage
