import React from 'react'

const SVGExplore: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 16} height={height || 16} viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-26.000000, -103.000000)" fill="currentColor">
          <g transform="translate(12.000000, 92.000000)">
            <path
              d="M22,11 C26.418278,11 30,14.581722 30,19 C30,23.418278 26.418278,27 22,27 C17.581722,27 14,23.418278 14,19 C14,14.581722 17.581722,11 22,11 Z M26.4427413,15.7044567 C26.6793702,14.9450899 25.954405,14.2201247 25.1950382,14.4567536 L25.0913621,14.4950685 L20.1416146,16.6163889 L20.0277836,16.6740258 C19.8818181,16.760051 19.760051,16.8818181 19.6740258,17.0277836 L19.6163889,17.1416146 L17.4950685,22.0913621 L17.4567536,22.1950382 C17.2201247,22.954405 17.9450899,23.6793702 18.7044567,23.4427413 L18.8081329,23.4044264 L23.7578803,21.2831061 L23.8717113,21.2254691 C24.0176768,21.1394439 24.1394439,21.0176768 24.2254691,20.8717113 L24.2831061,20.7578803 L26.4044264,15.8081329 L26.4427413,15.7044567 Z M22,18 C22.5522847,18 23,18.4477153 23,19 C23,19.5522847 22.5522847,20 22,20 C21.4477153,20 21,19.5522847 21,19 C21,18.4477153 21.4477153,18 22,18 Z"
              id="Icon"
            ></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGExplore
