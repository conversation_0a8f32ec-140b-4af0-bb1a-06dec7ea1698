import React from 'react'

const SVGMailInbox: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 50} height={height || 40} viewBox="0 0 50 40" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1010.000000, -388.000000)" fill="currentColor" fillRule="nonzero">
          <g stroke="currentColor" strokeWidth={strokeWidth || 1} transform="translate(1010.000000, 388.000000)">
            <path d="M38.28125,40 L11.71875,40 C5.25703125,40 0,34.670099 0,28.1188119 L0,11.8811881 C0,5.32990099 5.25703125,0 11.71875,0 L38.28125,0 C44.7429688,0 50,5.32990099 50,11.8811881 L50,28.1188119 C50,34.670099 44.7429688,40 38.28125,40 Z M11.71875,3.96039604 C7.4109375,3.96039604 3.90625,7.51366337 3.90625,11.8811881 L3.90625,28.1188119 C3.90625,32.4863366 7.4109375,36.039604 11.71875,36.039604 L38.28125,36.039604 C42.5890625,36.039604 46.09375,32.4863366 46.09375,28.1188119 L46.09375,11.8811881 C46.09375,7.51366337 42.5890625,3.96039604 38.28125,3.96039604 L11.71875,3.96039604 Z M32.1568359,23.5055446 L41.8106445,16.0291089 C42.6678711,15.3652475 42.8319336,14.1225743 42.1771484,13.2534653 C41.5222656,12.3843564 40.2966797,12.2180198 39.4394531,12.8818812 L29.7868164,20.3574257 C26.9996094,22.5127723 23.1071289,22.5143564 20.3180664,20.3628713 L10.9644531,12.9910891 C10.112793,12.319802 8.88564453,12.4757426 8.22373047,13.3392079 C7.56171875,14.2026733 7.71542969,15.4467327 8.56708984,16.1179208 L17.9279297,23.4953465 C17.9326172,23.4991089 17.9374023,23.5027723 17.9421875,23.5064356 C20.034375,25.1242574 22.5412109,25.9330693 25.0486328,25.9330693 C27.5560547,25.9330693 30.0639648,25.1237624 32.1568359,23.5055446 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGMailInbox
