import React from 'react'

const SVGGift: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={'20px' || width} height={'20px' || height} style={{ ...styles }} viewBox="0 0 20 20">
      <g id="🟣-Home-Feed---About-Section---Spark-Feed" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="🌑-Home---Public---Members-View" transform="translate(-30, -800)" fill="currentColor">
          <g id="Group-4" transform="translate(14, 786)">
            <g id="Icons/system/20px/gift" transform="translate(17, 16)">
              <path
                d="M12.6,0 C14.4242294,0 15.8987264,1.47260838 15.9949993,3.30879787 L16,3.5 L16,3.8571375 C16,4.26001645 15.9260077,4.64597739 15.7909884,5.00098537 L15.8571429,5 C16.962867,5 17.9138653,5.7900688 17.9944706,6.85336066 L18,7 L18,8 C18,8.72789384 17.5951651,9.34381864 17.0003534,9.69260662 L17,12.7142857 C17,14.4709019 15.6045443,15.8984519 13.8553828,15.9948149 L13.6666667,16 L4.33333333,16 C2.56026026,16 1.10364062,14.6330141 1.00529238,12.9011888 L1,12.7142857 L0.999847541,9.69272009 C0.445309707,9.36762478 0.0558452526,8.810361 0.00552949674,8.14663901 L0,8 L0,7 C0,5.91294 0.902935337,5.07560537 1.9931044,5.0048486 L2.14285714,5 L2.20901218,5.0009828 C2.09394879,4.69844588 2.02320564,4.37342994 2.00480732,4.0346214 L2,3.8571375 L2,3.5 C2,1.64006503 3.41371368,0.10544878 5.21257878,0.00520762417 L5.4,0 C6.8393985,0 8.149809,1.06783941 9.00051715,2.50838267 C9.81365808,1.12996711 11.0481305,0.0931819835 12.4137364,0.00595224335 L12.6,0 Z M8,11 L3,11 L3,12.7142857 C3,13.3756295 3.52064321,13.926976 4.19625341,13.9933109 L4.33333333,14 L8,14 L8,11 Z M15,11 L10,11 L10,14 L13.6666667,14 C14.3613333,14 14.925375,13.4899999 14.9931667,12.8449791 L15,12.7142857 L15,11 Z M8,7 L3,7 L3,9 L8,9 L8,7 Z M15,7 L10,7 L10,9 L15,9 L15,7 Z M5.4,2 C5.17533525,2 4.96171896,2.05732229 4.77208672,2.15955243 L4.66128894,2.22618014 L4.55686824,2.30298985 L4.50573726,2.34636424 C4.24105499,2.58152607 4.05724156,2.91980635 4.01125565,3.30820013 L4.00650004,3.35414449 L4,3.5 L4,3.8571375 C4,3.94248641 4.00858019,4.02539317 4.02481433,4.1049917 C4.05875394,4.27149467 4.12573653,4.4226539 4.21741878,4.55161421 L4.27560934,4.62641903 C4.32432419,4.68406347 4.37837737,4.7362345 4.43684265,4.78206605 L4.50011678,4.8280264 C4.55188299,4.86279282 4.60668167,4.89288846 4.66381092,4.91772945 C4.75224922,4.95620178 4.84613557,4.98203325 4.94358637,4.99340498 L5.057148,5 L7.878,5 L7.83647856,4.83154439 C7.53899084,3.74110684 6.87706278,2.76291973 6.19842431,2.29358012 C6.01395428,2.16459628 5.82640877,2.07337997 5.6423102,2.02955066 C5.59273201,2.01854858 5.55242755,2.01141989 5.51250749,2.00672303 L5.4,2 Z M12.6,2 L12.4879752,2.00648931 L12.362861,2.02890943 C12.2821779,2.04646511 12.1917992,2.07790863 12.1012763,2.11957452 L11.9360222,2.20675418 L11.7667062,2.31817831 L11.6239692,2.42827022 C11.202258,2.77626008 10.7990577,3.3103879 10.4999042,3.93309711 L10.5,4 L10.4684362,3.99975767 C10.3457146,4.26437241 10.2419522,4.54405724 10.1635214,4.83154439 L10.121,5 L12.94284,5 C13.0810276,5 13.2138987,4.97091589 13.3360165,4.91783144 L13.3745967,4.90006966 C13.4179634,4.87902942 13.4598577,4.85491251 13.5000169,4.82795604 L13.5631574,4.78206785 C13.6216231,4.73623657 13.6756766,4.68406578 13.7243915,4.62642152 L13.7818316,4.55243016 C13.828048,4.48754365 13.8679017,4.41731055 13.9004323,4.34258028 L13.9452103,4.22180247 L13.9751859,4.10499358 L13.9936807,3.98322208 L14,3.8571375 L14,3.5 C14,3.11075201 13.8629805,2.76025043 13.6401924,2.49612538 L13.5405628,2.38938032 L13.4431408,2.30298531 C13.2074166,2.11234912 12.9145394,2 12.6,2 Z"
                id="Combined-Shape"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGGift
