import React from 'react'

const SVGClose: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 24} height={height || 24} viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1092.000000, -604.000000)">
          <g transform="translate(8.000000, 584.000000)">
            <g transform="translate(1076.000000, 12.000000)">
              <g transform="translate(8.000000, 8.000000)">
                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                <rect
                  stroke="currentColor"
                  strokeWidth={strokeWidth || 2}
                  x="2"
                  y="5"
                  width="14"
                  height="14"
                  rx="1"
                ></rect>
                <rect
                  stroke="currentColor"
                  strokeWidth={strokeWidth || 2}
                  x="16"
                  y="5"
                  width="6"
                  height="14"
                  rx="1"
                ></rect>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGClose
