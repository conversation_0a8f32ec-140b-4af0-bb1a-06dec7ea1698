import React from 'react'

const SVGCamcorder: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 22} height={height || 16} viewBox="0 0 22 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1.000000, -4.000000)">
          <g transform="translate(1.000000, 4.000000)">
            <path
              d="M4,1 C2.34314575,1 1,2.34314575 1,4 L1,11.9774633 C1,13.6343175 2.34314575,14.9774633 4,14.9774633 L12,14.9774633 C13.6568542,14.9774633 15,13.6343175 15,11.9774633 L15,4 C15,2.34314575 13.6568542,1 12,1 L4,1 Z"
              stroke="currentColor"
              strokeWidth={strokeWidth || 2}
            ></path>
            <path
              d="M7.46314442,5.76459472 L10.226712,7.20875095 C10.7161924,7.46453855 10.905638,8.06869771 10.6498504,8.5581782 C10.5495881,8.75004223 10.3897717,8.9041893 10.194413,8.99745948 L7.43084551,10.3168708 C6.93244956,10.5548202 6.3355237,10.3436865 6.0975743,9.84529059 C6.03333813,9.71074498 6,9.56353836 6,9.41444508 L6,6.65087754 C6,6.09859279 6.44771525,5.65087754 7,5.65087754 C7.1612932,5.65087754 7.32019302,5.68989267 7.46314442,5.76459472 Z"
              fill="currentColor"
            ></path>
            <path
              d="M15,6.49429084 L15,9.48429831 L19.3989438,12.7925938 C19.5722296,12.9229161 19.7831779,12.993387 20,12.993387 C20.5522847,12.993387 21,12.5456718 21,11.993387 L21,3.99759637 C21,3.78139069 20.9299287,3.57100812 20.8002885,3.3979812 C20.4691302,2.95599408 19.842372,2.86614958 19.4003848,3.19730789 L15,6.49429084 Z"
              stroke="currentColor"
              strokeWidth={strokeWidth || 2}
            ></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGCamcorder
