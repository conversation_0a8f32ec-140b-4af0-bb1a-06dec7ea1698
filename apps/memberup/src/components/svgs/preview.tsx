import React from 'react'

const SVGPreview: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={width || '16px'} height={height || '16px'} viewBox="0 0 16 16" version="1.1" style={{ ...styles }}>
      <g id="🎬🟣📁-Content---Index---Creator-View" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Content---Index---Cover---Empty---Creator-View" transform="translate(-1238, -404)" fill="currentColor">
          <g id="rich-tooltip" transform="translate(1216, 384)">
            <path
              d="M27,20 C27.5522847,20 28,20.4477153 28,21 C28,21.5128358 27.6139598,21.9355072 27.1166211,21.9932723 L27,22 L25,22 C24.4871642,22 24.0644928,22.3860402 24.0067277,22.8833789 L24,23 L24,33 C24,33.5128358 24.3860402,33.9355072 24.8833789,33.9932723 L25,34 L35,34 C35.5128358,34 35.9355072,33.6139598 35.9932723,33.1166211 L36,33 L36,31 C36,30.4871642 36.3860402,30.0644928 36.8833789,30.0067277 L37,30 C37.5128358,30 37.9355072,30.3860402 37.9932723,30.8833789 L38,31 L38,33 C38,34.5976809 36.75108,35.9036609 35.1762728,35.9949073 L35,36 L25,36 C23.4023191,36 22.0963391,34.75108 22.0050927,33.1762728 L22,33 L22,23 C22,21.4023191 23.24892,20.0963391 24.8237272,20.0050927 L25,20 L27,20 Z M31.7071068,27.7071068 C31.3466228,28.0675907 30.7793918,28.0953203 30.3871006,27.7902954 L30.2928932,27.7071068 C29.9324093,27.3466228 29.9046797,26.7793918 30.2097046,26.3871006 L30.2928932,26.2928932 L34.584,22 L33,22 C32.4477153,22 32,21.5522847 32,21 C32,20.4477153 32.4477153,20 33,20 L37,20 L37.081,20.003 L37.2007258,20.0202401 L37.3121425,20.0497381 L37.4232215,20.0936734 L37.5207088,20.1459955 L37.6167501,20.212786 L37.7071068,20.2928932 L37.8036654,20.4046934 L37.8753288,20.5159379 L37.9063462,20.5769009 L37.9401141,20.6583496 L37.9641549,20.734007 L37.9930928,20.8819045 L38,21 L38,25 C38,25.5128358 37.6139598,25.9355072 37.1166211,25.9932723 L37,26 C36.4477153,26 36,25.5522847 36,25 L36,23.414 L31.7071068,27.7071068 Z"
              id="Shape"
            ></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGPreview
