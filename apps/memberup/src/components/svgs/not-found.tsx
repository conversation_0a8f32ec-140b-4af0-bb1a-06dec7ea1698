import React from 'react'

const SVGNotFound: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg
      width={width || 175}
      height={height || 105}
      viewBox="0 0 175 105"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
        <g transform="translate(-632.000000, -244.000000)">
          <g transform="translate(633.000000, 245.000000)">
            <path
              d="M13.5169826,90 L12,90 C5.372583,90 0,84.627417 0,78 L0,12 C0,5.372583 5.372583,0 12,0 L148,0 C154.627417,0 160,5.372583 160,12 L160,14.9496166"
              id="Path"
              stroke="currentColor"
              strokeWidth={2}
              strokeDasharray="3,3"
            ></path>
            <rect stroke="currentColor" strokeWidth={2} x="15" y="15" width="158" height="88" rx="12"></rect>
            <rect fillOpacity="0.4" fill="currentColor" x="16" y="28" width="156" height="2"></rect>
            <g transform="translate(34.000000, 46.000000)">
              <circle stroke="currentColor" strokeWidth={2} cx="16" cy="16" r="15"></circle>
              <g transform="translate(4.000000, 4.000000)" fill="currentColor">
                <g transform="translate(5.000000, 5.000000)">
                  <path
                    d="M8.41425,7.00025 L13.70725,1.70725 C14.09825,1.31625 14.09825,0.68425 13.70725,0.29325 C13.31625,-0.09775 12.68425,-0.09775 12.29325,0.29325 L7.00025,5.58625 L1.70725,0.29325 C1.31625,-0.09775 0.68425,-0.09775 0.29325,0.29325 C-0.09775,0.68425 -0.09775,1.31625 0.29325,1.70725 L5.58625,7.00025 L0.29325,12.29325 C-0.09775,12.68425 -0.09775,13.31625 0.29325,13.70725 C0.48825,13.90225 0.74425,14.00025 1.00025,14.00025 C1.25625,14.00025 1.51225,13.90225 1.70725,13.70725 L7.00025,8.41425 L12.29325,13.70725 C12.48825,13.90225 12.74425,14.00025 13.00025,14.00025 C13.25625,14.00025 13.51225,13.90225 13.70725,13.70725 C14.09825,13.31625 14.09825,12.68425 13.70725,12.29325 L8.41425,7.00025 Z"
                    fillRule="nonzero"
                    fill="currentColor"
                    stroke="currentColor"
                  ></path>
                </g>
              </g>
            </g>
            <path
              d="M79.0429177,55 L83.8142252,55 L81.4285714,53.2421499 L79.0429177,55 Z M89.9000605,55 L94.671368,55 L92.2857143,53.2421499 L89.9000605,55 Z M100.757203,55 L105.528511,55 L103.142857,53.2421499 L100.757203,55 Z M111.614346,55 L116.385654,55 L114,53.2421499 L111.614346,55 Z M122.471489,55 L127.242797,55 L124.857143,53.2421499 L122.471489,55 Z M133.328632,55 L138.099939,55 L135.714286,53.2421499 L133.328632,55 Z M144.185775,55 L148.957082,55 L146.571429,53.2421499 L144.185775,55 Z"
              stroke="currentColor"
              strokeWidth={2}
            ></path>
            <path
              d="M79.0429177,63 L83.8142252,63 L81.4285714,61.2421499 L79.0429177,63 Z M89.9000605,63 L94.671368,63 L92.2857143,61.2421499 L89.9000605,63 Z M100.757203,63 L105.528511,63 L103.142857,61.2421499 L100.757203,63 Z M111.614346,63 L116.385654,63 L114,61.2421499 L111.614346,63 Z M122.471489,63 L127.242797,63 L124.857143,61.2421499 L122.471489,63 Z M133.328632,63 L138.099939,63 L135.714286,61.2421499 L133.328632,63 Z M144.185775,63 L148.957082,63 L146.571429,61.2421499 L144.185775,63 Z"
              stroke="currentColor"
              strokeWidth={2}
            ></path>
            <path
              d="M79.3634372,70.7735511 L83.1971357,70.5179712 L81.4545455,69.2400717 L79.3634372,70.7735511 Z M91.3634372,69.9735511 L93.1971357,69.8513045 L92.3636364,69.2400717 L91.3634372,69.9735511 Z M118.802864,70.1486955 L119.636364,70.7599283 L120.636563,70.0264489 L118.802864,70.1486955 Z M128.802864,69.4820288 L130.545455,70.7599283 L132.636563,69.2264489 L128.802864,69.4820288 Z"
              id="Path-3"
              stroke="currentColor"
              strokeWidth={2}
            ></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGNotFound
