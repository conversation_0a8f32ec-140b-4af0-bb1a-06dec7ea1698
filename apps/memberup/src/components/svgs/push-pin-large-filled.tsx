import React from 'react'

const SVGPushPinLargeFilled: React.FC<{
  className?: string
  width?: number
  height?: number
  styles?: React.CSSProperties
}> = ({ width, height, styles, className }) => {
  return (
    <svg
      className={className}
      width={width ?? '12px'}
      height={height ?? '16px'}
      viewBox="0 0 12 16"
      style={{ ...styles }}
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-392, -294)" fill="currentColor">
          <g transform="translate(368, 276)">
            <path
              d="M24,28 C24,26.8954305 24.8954305,26 26,26 L27,26 L27,20 L26,20 C25.4477153,20 25,19.5522847 25,19 C25,18.4477153 25.4477153,18 26,18 L34,18 C34.5522847,18 35,18.4477153 35,19 C35,19.5522847 34.5522847,20 34,20 L33,20 L33,26 L34,26 C35.0543618,26 35.9181651,26.8158778 35.9945143,27.8507377 L36,28 L31,28 L31,33 C31,33.5522847 30.5522847,34 30,34 C29.4477153,34 29,33.5522847 29,33 L29,28 L24,28 Z"
              id="Shape"
            ></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGPushPinLargeFilled
