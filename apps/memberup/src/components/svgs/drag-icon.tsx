import React from 'react'

const SVGDragIcon: React.FC<{
  width?: number
  height?: number
  strokeWidth?: number
  styles?: any
}> = ({ width, height, styles }) => {
  return (
    <svg width={width || '6px'} height={height || '12px'} style={{ ...styles }} viewBox="0 0 6 12" version="1.1">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/16px/drag" transform="translate(-5, -2)" fill="currentColor">
          <path
            d="M5,2 L7,2 L7,4 L5,4 L5,2 Z M9,2 L11,2 L11,4 L9,4 L9,2 Z M5,7 L7,7 L7,9 L5,9 L5,7 Z M9,7 L11,7 L11,9 L9,9 L9,7 Z M5,12 L7,12 L7,14 L5,14 L5,12 Z M9,12 L11,12 L11,14 L9,14 L9,12 Z"
            id="Icon"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGDragIcon
