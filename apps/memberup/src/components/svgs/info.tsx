import React from 'react'

const SVGInfoButton: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={'12px' || width} height={'12px' || height} viewBox="0 0 12 12" style={{ ...styles }}>
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/16px/info" transform="translate(-2, -2)" fill="currentColor">
          <path
            d="M8,2 C11.3137085,2 14,4.6862915 14,8 C14,11.3137085 11.3137085,14 8,14 C4.6862915,14 2,11.3137085 2,8 C2,4.6862915 4.6862915,2 8,2 Z M8,7 C7.48716416,7 7.06449284,7.38604019 7.00672773,7.88337887 L7,8 L7,11 C7,11.5522847 7.44771525,12 8,12 C8.51283584,12 8.93550716,11.6139598 8.99327227,11.1166211 L9,11 L9,8 C9,7.44771525 8.55228475,7 8,7 Z M9,4 L7,4 L7,6 L9,6 L9,4 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGInfoButton
