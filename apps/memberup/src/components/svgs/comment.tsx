import React from 'react'

const SVGComment: React.FC<{ fontSize?: number }> = ({ fontSize }) => {
  return (
    <svg
      width="1em"
      height="1em"
      fontSize={fontSize || '1.5rem'}
      viewBox="0 0 20 20"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-473.000000, -1835.000000)" fillRule="nonzero" stroke="currentColor">
          <g transform="translate(369.000000, 1215.000000)">
            <g transform="translate(24.000000, 618.000000)">
              <path d="M89.833346,2.5 C92.4066603,2.5 94.7398769,3.54688082 96.4298435,5.23684529 C98.1198099,6.92680952 99.1666921,9.26002267 99.1666921,11.8333333 C99.1666921,13.5357364 98.7013271,15.2063585 97.8220968,16.6589507 L97.8220968,16.6589507 L99.245037,20.9292349 L94.2106656,20.0763126 C92.8706889,20.7896163 91.3562602,21.1666667 89.833346,21.1666667 C87.2601498,21.1666667 84.9269033,20.1197489 83.2368915,18.4297393 C81.5469323,16.7397823 80.5,14.4066039 80.5,11.8333333 C80.5,9.26002267 81.5468821,6.92680952 83.2368486,5.23684529 C84.9268152,3.54688082 87.2600317,2.5 89.833346,2.5 Z"></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGComment
