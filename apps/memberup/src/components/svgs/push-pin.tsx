import React from 'react'

const SVGPushPin: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({ width, height }) => {
  return (
    <svg width={width || '10px'} height={height || '12px'} viewBox="0 0 10 12" version="1.1">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/16px/pin" transform="translate(-3, -2)" fill="currentColor">
          <path
            d="M11,2 C11.5522847,2 12,2.44771525 12,3 C12,3.55228475 11.5522847,4 11,4 L10,4 L10,8 L11.5,8 C12.3284271,8 13,8.67157288 13,9.5 L13,10 L9,10 L9,13 C9,13.5522847 8.55228475,14 8,14 C7.44771525,14 7,13.5522847 7,13 L7,10 L3,10 L3,9.5 C3,8.67157288 3.67157288,8 4.5,8 L6,8 L6,4 L5,4 C4.44771525,4 4,3.55228475 4,3 C4,2.44771525 4.44771525,2 5,2 L11,2 Z"
            id="Path"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGPushPin
