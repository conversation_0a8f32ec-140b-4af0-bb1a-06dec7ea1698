import React from 'react'

const <PERSON><PERSON><PERSON>: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={width || '21px'} height={height || '20px'} style={{ ...styles }} viewBox="0 0 21 20">
      <g id="🎬🟣📁-Content---Index---Creator-View" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Content---Index---Cover---Empty---Creator-View" transform="translate(-511, -662)" fill="currentColor">
          <g
            id="🌑-Dark/Card/image-+-header-+-description-+-icon-button/left/2.-hovered"
            transform="translate(272, 354)"
          >
            <g id="Group-2-Copy" transform="translate(19.5, 306)">
              <path
                d="M225,16 L235,16 C237.761424,16 240,18.2385763 240,21 C240,21.5522847 239.552285,22 239,22 C238.487164,22 238.064493,21.6139598 238.006728,21.1166211 L237.994907,20.8237272 C237.90704,19.3072462 236.692754,18.0929596 235.176273,18.0050927 L235,18 L225,18 C223.343146,18 222,19.3431458 222,21 C222,21.5522847 221.552285,22 221,22 C220.447715,22 220,21.5522847 220,21 C220,18.3112453 222.122307,16.1181819 224.78311,16.0046195 L225,16 L235,16 L225,16 Z M230,2 C233.313708,2 236,4.6862915 236,8 C236,11.3137085 233.313708,14 230,14 C226.686292,14 224,11.3137085 224,8 C224,4.6862915 226.686292,2 230,2 Z M230,4 C227.790861,4 226,5.790861 226,8 C226,10.209139 227.790861,12 230,12 C232.209139,12 234,10.209139 234,8 C234,5.790861 232.209139,4 230,4 Z"
                id="Shape"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGPerson
