import React from 'react'

const SVGDifference: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 16} height={height || 16} viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <path
        d="m9 0c1.5976809 0 2.9036609 1.24891996 2.9949073 2.82372721l.0050927.17627279v1h1c1.5976809 0 2.9036609 1.24891996 2.9949073 2.82372721l.0050927.17627279v6c0 1.5976809-1.24892 2.9036609-2.8237272 2.9949073l-.1762728.0050927h-6c-1.59768088 0-2.90366088-1.24892-2.99490731-2.8237272l-.00509269-.1762728v-1h-1c-1.59768088 0-2.90366088-1.24892-2.99490731-2.8237272l-.00509269-.1762728v-6c0-1.59768088 1.24891996-2.90366088 2.82372721-2.99490731l.17627279-.00509269zm4 6h-1v3c0 1.5976809-1.24892 2.9036609-2.8237272 2.9949073l-.1762728.0050927h-3v1c0 .5128358.38604019.9355072.88337887.9932723l.11662113.0067277h6c.5128358 0 .9355072-.3860402.9932723-.8833789l.0067277-.1166211v-6c0-.51283584-.3860402-.93550716-.8833789-.99327227zm-4-4h-6c-.51283584 0-.93550716.38604019-.99327227.88337887l-.00672773.11662113v6c0 .5128358.38604019.9355072.88337887.9932723l.11662113.0067277h1v-3c0-1.59768088 1.24891996-2.90366088 2.82372721-2.99490731l.17627279-.00509269h3v-1c0-.51283584-.3860402-.93550716-.8833789-.99327227z"
        fill="currentColor"
        fillRule="evenodd"
      />
    </svg>
  )
}

export default SVGDifference
