import React from 'react'

const SVGDefaultLink: React.FC<{ width?: number; height?: number; style?: any }> = ({ width, height, style }) => {
  return (
    <svg width={width || '32px'} height={height || '32px'} viewBox="0 0 128 128" version="1.1" style={style}>
      <g id="#ffffffff">
        <path
          fill="currentColor"
          opacity="1.00"
          d=" M 90.30 20.11 C 102.56 18.46 115.21 26.37 119.07 38.13 C 122.49 47.62 119.87 58.72 112.93 65.97 C 106.21 72.91 99.30 79.66 92.48 86.51 C 86.86 92.11 78.99 95.59 70.98 95.01 C 59.24 94.52 48.42 85.49 46.02 73.95 C 44.25 65.93 46.37 57.04 51.83 50.88 C 55.78 47.24 62.58 51.66 61.31 56.74 C 60.08 60.21 57.52 63.24 57.47 67.08 C 56.77 74.17 61.89 81.14 68.83 82.66 C 74.03 84.00 79.71 82.23 83.46 78.45 C 90.46 71.46 97.50 64.51 104.43 57.44 C 109.39 52.42 109.97 43.87 105.82 38.18 C 101.95 32.38 93.65 30.26 87.42 33.34 C 84.85 34.52 82.28 36.79 79.26 35.67 C 75.11 34.63 73.60 28.74 76.81 25.87 C 80.67 22.80 85.39 20.74 90.30 20.11 Z"
        />
        <path
          fill="currentColor"
          opacity="1.00"
          d=" M 49.50 33.61 C 62.46 30.37 76.89 38.38 81.11 51.01 C 84.22 59.77 82.33 70.21 76.13 77.16 C 72.19 80.71 65.36 76.35 66.70 71.28 C 67.91 67.79 70.49 64.74 70.53 60.89 C 71.13 54.34 66.85 47.80 60.60 45.76 C 55.09 43.75 48.64 45.39 44.56 49.53 C 37.57 56.56 30.56 63.57 23.64 70.67 C 18.79 75.45 18.04 83.60 21.80 89.25 C 25.63 95.65 34.67 97.97 41.17 94.37 C 43.68 92.96 46.60 91.07 49.54 92.63 C 53.38 93.98 54.06 99.54 51.16 102.16 C 47.82 105.02 43.58 106.78 39.29 107.64 C 28.24 109.79 16.16 104.10 10.87 94.14 C 5.10 84.23 6.86 70.87 14.63 62.53 C 21.11 55.70 27.98 49.25 34.60 42.56 C 38.70 38.38 43.70 34.85 49.50 33.61 Z"
        />
      </g>
    </svg>
  )
}

export default SVGDefaultLink
