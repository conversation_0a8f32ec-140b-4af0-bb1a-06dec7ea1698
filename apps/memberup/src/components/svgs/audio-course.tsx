import React from 'react'

const SVGAudioCourse: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={'14px' || width} height={'16px' || height} style={{ ...styles }} viewBox="0 0 14 16">
      <g id="🎬-Content---Detail---Video---Member-View" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Content---Detail---Video---Member-View" transform="translate(-33, -530)" fill="currentColor">
          <path
            d="M46,536 C46.5522847,536 47,536.447715 47,537 C47,540.526122 44.3928118,543.443302 41.0010101,543.928967 L41,545 C41,545.552285 40.5522847,546 40,546 C39.4477153,546 39,545.552285 39,545 L39,543.92911 C35.6077016,543.443877 33,540.526469 33,537 C33,536.447715 33.4477153,536 34,536 C34.5522847,536 35,536.447715 35,537 C35,539.761424 37.2385763,542 40,542 C42.7614237,542 45,539.761424 45,537 C45,536.447715 45.4477153,536 46,536 Z M40,530 <PERSON>41.6568542,530 43,531.343146 43,533 L43,537 C43,538.656854 41.6568542,540 40,540 C38.3431458,540 37,538.656854 37,537 L37,533 C37,531.343146 38.3431458,530 40,530 Z M40,532 C39.4477153,532 39,532.447715 39,533 L39,537 C39,537.552285 39.4477153,538 40,538 C40.5522847,538 41,537.552285 41,537 L41,533 C41,532.447715 40.5522847,532 40,532 Z"
            id="Icon"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGAudioCourse
