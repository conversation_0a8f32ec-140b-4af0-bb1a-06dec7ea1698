import React from 'react'

const SVGCloseNew: React.FC<{ fontSize?: number }> = ({ fontSize }) => {
  return (
    <svg
      width="14.0005px"
      height="14.00025px"
      viewBox="0 0 14.0005 14.00025"
      fontSize={fontSize || '1.5rem'}
      version="1.1"
    >
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/24px/close" transform="translate(-5, -5)" fill="currentColor">
          <path
            d="M13.41425,12.00025 L18.70725,6.70725 C19.09825,6.31625 19.09825,5.68425 18.70725,5.29325 C18.31625,4.90225 17.68425,4.90225 17.29325,5.29325 L12.00025,10.58625 L6.70725,5.29325 C6.31625,4.90225 5.68425,4.90225 5.29325,5.29325 C4.90225,5.68425 4.90225,6.31625 5.29325,6.70725 L10.58625,12.00025 L5.29325,17.29325 C4.90225,17.68425 4.90225,18.31625 5.29325,18.70725 C5.48825,18.90225 5.74425,19.00025 6.00025,19.00025 C6.25625,19.00025 6.51225,18.90225 6.70725,18.70725 L12.00025,13.41425 L17.29325,18.70725 C17.48825,18.90225 17.74425,19.00025 18.00025,19.00025 C18.25625,19.00025 18.51225,18.90225 18.70725,18.70725 C19.09825,18.31625 19.09825,17.68425 18.70725,17.29325 L13.41425,12.00025 Z"
            id="Path"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGCloseNew
