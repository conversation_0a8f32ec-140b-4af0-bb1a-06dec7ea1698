import React from 'react'

const SVGPhotoNew: React.FC<{ width?: number; height?: number }> = ({ width, height }) => {
  return (
    <svg width={width || 16} height={height || 16} viewBox="0 0 16 16" version="1.1">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/20px/photo" transform="translate(-2, -2)" fill="currentColor">
          <path
            d="M15,2 C16.6568542,2 18,3.34314575 18,5 L18,15 C18,16.6568542 16.6568542,18 15,18 L5,18 C3.34314575,18 2,16.6568542 2,15 L2,5 C2,3.34314575 3.34314575,2 5,2 L15,2 Z M15,4 L5,4 C4.44771525,4 4,4.44771525 4,5 L4,12.697 L6.18626653,9.41876181 C6.50017051,8.97929623 7.09949173,8.87371288 7.54124102,9.158983 L7.6401844,9.23177872 L12.7852568,13.5193391 L16,7.745 L16,5 C16,4.44771525 15.5522847,4 15,4 Z M6.5,8 C7.32842712,8 8,7.32842712 8,6.5 C8,5.67157288 7.32842712,5 6.5,5 C5.67157288,5 5,5.67157288 5,6.5 C5,7.32842712 5.67157288,8 6.5,8 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGPhotoNew
