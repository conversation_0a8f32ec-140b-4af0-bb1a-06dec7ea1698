import React from 'react'

const SV<PERSON><PERSON>ashtag: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={width || '12px'} height={height || '12px'} style={{ ...styles }} viewBox="0 0 12 12" version="1.1">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/feature/16px/hashtag" transform="translate(-2, -2)" fill="currentColor">
          <path
            d="M10,2 C10.5522847,2 11,2.44771525 11,3 L11,5 L13,5 C13.5522847,5 14,5.44771525 14,6 C14,6.55228475 13.5522847,7 13,7 L11,7 L11,9 L13,9 C13.5522847,9 14,9.44771525 14,10 C14,10.5522847 13.5522847,11 13,11 L11,11 L11,13 C11,13.5522847 10.5522847,14 10,14 C9.44771525,14 9,13.5522847 9,13 L9,11 L7,11 L7,13 C7,13.5522847 6.55228475,14 6,14 C5.44771525,14 5,13.5522847 5,13 L5,11 L3,11 C2.44771525,11 2,10.5522847 2,10 C2,9.44771525 2.44771525,9 3,9 L5,9 L5,7 L3,7 C2.44771525,7 2,6.55228475 2,6 C2,5.44771525 2.44771525,5 3,5 L5,5 L5,3 C5,2.44771525 5.44771525,2 6,2 C6.55228475,2 7,2.44771525 7,3 L7,5 L9,5 L9,3 C9,2.44771525 9.44771525,2 10,2 Z M9,9 L9,7 L7,7 L7,9 L9,9 Z"
            id="Combined-Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGHashtag
