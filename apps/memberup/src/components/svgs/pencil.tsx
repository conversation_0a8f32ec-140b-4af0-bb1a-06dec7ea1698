import React from 'react'

const SVGPencil: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={width || '14px'} height={height || '14px'} style={{ ...styles }} viewBox="0 0 14 14" version="1.1">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/20px/edit" transform="translate(-3, -3)" fill="currentColor">
          <path
            d="M13.6159329,3.20970461 L13.7101403,3.29289322 L16.7101403,6.29289322 C17.0706242,6.65337718 17.0983538,7.22060824 16.7933289,7.61289944 L16.7101403,7.70710678 L7.7071068,16.7101403 C7.5508265,16.8664206 7.3481451,16.9656318 7.131444,16.9943611 L7,17.0030335 L4,17.0030335 C3.48716416,17.0030335 3.06449284,16.6169933 3.00672773,16.1196546 L3,16.0030335 L3,13.0030335 C3,12.7820197 3.07316447,12.5685495 3.20608063,12.395004 L3.29289322,12.2959267 L12.2959267,3.29289322 C12.6564107,2.93240926 13.2236417,2.90467972 13.6159329,3.20970461 Z M13.0030335,5.41421356 L5,13.4172471 L5,15.0030335 L6.58578644,15.0030335 L14.5888199,7 L13.0030335,5.41421356 Z"
            id="Combined-Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGPencil
