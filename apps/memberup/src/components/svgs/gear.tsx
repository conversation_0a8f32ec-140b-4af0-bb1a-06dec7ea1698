import React from 'react'

const SVGGear: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={width || '12px'} height={height || '12px'} style={{ ...styles }} viewBox="0 0 12 12">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/16px/settings" transform="translate(-2, -2)" fill="currentColor">
          <path
            d="M8.3922531,2 L8.49975684,2.23484607 L8.587401,2.43839023 L8.74563237,2.82111038 L8.96398098,3.36740531 L9.00051587,3.44621412 C9.06589971,3.56819511 9.16377008,3.66965464 9.28306952,3.73938139 L9.37638928,3.78555139 L9.99393851,4.03940213 L10.0918292,4.0719736 C10.2243985,4.106158 10.3638861,4.10339076 10.4949952,4.06397535 L10.5915171,4.027547 L11.2836647,3.71361846 L11.6497469,3.55580722 L11.6497469,3.55580722 L11.9287734,3.44361239 L12.5217814,4.03415609 L12.4327488,4.27528896 L12.2501178,4.727531 L12.2501178,4.727531 L11.9592475,5.41054309 L11.9255886,5.50516085 C11.8894216,5.63346226 11.8881119,5.76916398 11.9217958,5.89813954 L11.9536222,5.99338933 L12.204424,6.60450182 L12.2434826,6.68548795 C12.3133151,6.81051935 12.4175001,6.91288936 12.5434619,6.98052123 L12.6418199,7.02454304 L13.4461162,7.32394701 L13.7647534,7.45004195 L14,7.55046483 L14,8.39725131 L13.6683737,8.546777 L13.3157308,8.69461181 L12.6248844,8.97232009 L12.5468013,9.00890651 C12.4259441,9.07425807 12.3254112,9.17156069 12.2561555,9.28997701 L12.2102494,9.38257873 L11.9594841,9.99037461 L11.9266702,10.0886679 C11.8922438,10.2217972 11.8950934,10.361921 11.9349039,10.4935405 L11.971687,10.5904184 L12.2752659,11.2522018 L12.4467778,11.6433525 L12.561128,11.9255435 L11.967748,12.5178772 L11.8150249,12.4625883 L11.5836017,12.3733343 L11.1607199,12.2011008 L10.5812892,11.9553346 L10.4877287,11.9234466 C10.361003,11.8893922 10.2274872,11.8891911 10.1006594,11.9228634 L10.0070033,11.9544693 L9.39168095,12.2083946 L9.31190557,12.2469751 C9.18867763,12.315834 9.08743725,12.418084 9.01980116,12.5417241 L8.97555641,12.6382774 L8.72154837,13.3193097 L8.56556003,13.717654 L8.44578707,14 L7.60711483,14 L7.53843638,13.8525399 L7.41178878,13.5617438 L7.19294162,13.0295593 L7.03915959,12.6415175 L7.00305263,12.5612043 C6.93811934,12.4368572 6.83960137,12.3332654 6.71892874,12.2621802 L6.62444886,12.215145 L6.00649806,11.9600272 L5.92096967,11.9306131 C5.78217234,11.8920651 5.63523748,11.8943539 5.49790051,11.9369989 L5.3969745,11.9765419 L5.00438558,12.1572817 L4.46000126,12.3973387 L4.2460904,12.4866625 L4.06961053,12.5564161 L3.47691021,11.9644744 L3.56599642,11.7247951 L3.74886391,11.2734337 L4.04044818,10.5891887 L4.07424628,10.4942736 C4.11054207,10.3655618 4.11175385,10.2294057 4.07775467,10.1000683 L4.04565125,10.0045667 L3.79441137,9.39591374 L3.75513687,9.31491582 C3.68495201,9.18989252 3.58038645,9.08766072 3.45408953,9.02030699 L3.35548986,8.97652019 L2.67933876,8.72542304 L2.17597385,8.5260507 L2,8.44975909 L2,7.60213603 L2.14706645,7.5340343 L2.5548172,7.35925227 L3.37049902,7.03136903 L3.44931562,6.99486315 C3.57131196,6.92952514 3.67280057,6.83169728 3.74256811,6.71243103 L3.78877085,6.61913434 L4.03997423,6.00869262 L4.07264505,5.91057541 C4.10691763,5.77769463 4.10406237,5.63786627 4.06439382,5.50649535 L4.02774484,5.40979364 L3.72477834,4.74747293 L3.5095741,4.25031356 L3.4398248,4.07413359 L4.03248341,3.48282001 L4.29635412,3.5802091 L4.68893891,3.73676151 L5.41574084,4.04311907 L5.51029903,4.07599695 C5.63844832,4.11116294 5.77371809,4.11164011 5.90211229,4.07737909 L5.99690009,4.04516914 L6.61123672,3.78952967 L6.69031471,3.75104202 C6.81248113,3.68244636 6.91293848,3.5809916 6.98032568,3.45841212 L7.0244879,3.36269998 L7.28583552,2.66360414 L7.43814956,2.27639399 L7.55605449,2 L8.3922531,2 Z M8.0004722,6 C6.89801425,6 6,6.89751702 6,7.99913298 C6,9.10124731 6.89851355,10 8.0004722,10 C9.10164042,10 10,9.10108767 10,7.99913298 C10,6.89767654 9.10213958,6 8.0004722,6 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGGear
