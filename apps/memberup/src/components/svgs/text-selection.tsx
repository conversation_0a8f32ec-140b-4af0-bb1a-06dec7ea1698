import React from 'react'

const SVGTextSelection: React.FC<{
  width?: number
  height?: number
  styles?: any
}> = ({ width, height, styles }) => {
  return (
    <svg width={width || '12px'} height={height || '6px'} style={{ ...styles }} viewBox="0 0 12 6">
      <g id="🎬-Content---Index---Customize" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g
          id="Content---Index---Customize---About---Page-Header---Text---Empty"
          transform="translate(-527, -209)"
          fill="currentColor"
        >
          <g id="item-/-toggle" transform="translate(460, 184)">
            <g id="Group-6" transform="translate(61, 16)">
              <g id="Group-8" transform="translate(6, 9)">
                <path
                  d="M1,0 L11,0 C11.5522847,-1.01453063e-16 12,0.44771525 12,1 C12,1.55228475 11.5522847,2 11,2 L1,2 C0.44771525,2 3.38176876e-17,1.55228475 0,1 C-6.76353751e-17,0.44771525 0.44771525,6.76353751e-17 1,0 Z M3,4 L9,4 C9.55228475,4 10,4.44771525 10,5 C10,5.55228475 9.55228475,6 9,6 L3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 Z"
                  id="Combined-Shape"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGTextSelection
