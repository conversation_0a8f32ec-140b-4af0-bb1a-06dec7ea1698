import React from 'react'

const <PERSON><PERSON>yeLarge: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={width || '20px'} height={height || '17px'} style={{ ...styles }} viewBox="0 0 20 17">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/24px/visibility-on" transform="translate(-2, -3)" fill="currentColor">
          <path
            d="M12,3.53553391 C15.5878499,3.53553391 18.755364,5.96794789 21.5112563,10.6790474 L21.7171765,11.03665 L22,11.5355339 L21.5112563,12.3920204 C18.755364,17.1031199 15.5878499,19.5355339 12,19.5355339 C8.41215012,19.5355339 5.24463601,17.1031199 2.48874372,12.3920204 L2.28282345,12.0344178 L2,11.5355339 L2.28282345,11.03665 C5.08652302,6.09108884 8.32245387,3.53553391 12,3.53553391 Z M12,5.53553391 C9.29691839,5.53553391 6.72828619,7.45107099 4.30531955,11.5355339 C6.72828619,15.6199968 9.29691839,17.5355339 12,17.5355339 C14.6296758,17.5355339 17.1288735,15.7256053 19.4870502,11.8802494 L19.6947584,11.5356653 L19.4867171,11.1908666 C17.1248787,7.34321421 14.6258789,5.53553391 12,5.53553391 Z M12,8.53553391 C13.6568542,8.53553391 15,9.87867971 15,11.5355339 C15,13.1923881 13.6568542,14.5355339 12,14.5355339 C10.3431458,14.5355339 9,13.1923881 9,11.5355339 C9,9.87867971 10.3431458,8.53553391 12,8.53553391 Z"
            id="Shape"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGEyeLarge
