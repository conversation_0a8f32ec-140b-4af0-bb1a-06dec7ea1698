import React from 'react'

const SVGCommentFilled: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={width || '12px'} height={height || '12px'} viewBox="0 0 12 12" version="1.1" style={{ ...styles }}>
      <g id="Icons/system/12px/message" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <rect fill="none" opacity="0.16" x="0" y="0" width="12" height="12"></rect>
        <path
          d="M6,1 C3.23864865,1 1,3.23852025 1,5.99971323 C1,6.88236531 1.23081081,7.71042592 1.63243243,8.43038462 L1.0940053,9.60775511 C0.868910504,10.0999665 1.08545129,10.6814581 1.57766269,10.9065529 C1.83639835,11.0248761 2.1337835,11.0249179 2.39255243,10.9066674 L3.57108108,10.3681113 L3.57108108,10.3681113 C4.29054054,10.7691694 5.11783784,10.9994265 6,10.9994265 C8.76135135,10.9994265 11,8.76090621 11,5.99971323 C11,3.23852025 8.76135135,1 6,1 Z"
          id="icon-12px"
          fill="currentColor"
        ></path>
      </g>
    </svg>
  )
}

export default SVGCommentFilled
