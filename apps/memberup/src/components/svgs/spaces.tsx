import React from 'react'

const SVGSpaces: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 16} height={height || 16} viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-26.000000, -597.000000)" fill="currentColor">
          <g transform="translate(8.000000, 537.000000)">
            <g transform="translate(0.000000, 48.000000)">
              <g transform="translate(16.000000, 10.000000)">
                <path
                  d="M16,2 C17.1045695,2 18,2.8954305 18,4 L18,16 C18,17.1045695 17.1045695,18 16,18 L4,18 C2.8954305,18 2,17.1045695 2,16 L2,4 C2,2.8954305 2.8954305,2 4,2 L16,2 Z M16,4 L16,10 L15,10 L11,10 C10.4477153,10 10,9.55228475 10,9 L10,4 L4,4 L4,16 L16,16 L16,4 Z"
                  id="Shape"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGSpaces
