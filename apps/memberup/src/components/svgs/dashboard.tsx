import React from 'react'

const SVGDashboard: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 20} height={height || 16} viewBox="0 0 20 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-2.000000, -4.000000)" stroke="currentColor" strokeWidth={strokeWidth || 2}>
          <g transform="translate(2.000000, 4.000000)">
            <rect x="1" y="1" width="7" height="6" rx="2"></rect>
            <rect x="1" y="11" width="7" height="4" rx="2"></rect>
            <rect x="12" y="1" width="7" height="4" rx="2"></rect>
            <rect x="12" y="9" width="7" height="6" rx="2"></rect>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGDashboard
