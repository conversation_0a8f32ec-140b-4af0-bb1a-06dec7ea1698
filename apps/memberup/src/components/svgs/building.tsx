import React from 'react'

const SVGBuilding: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 24} height={height || 24} viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-486.000000, -92.000000)" fill="currentColor">
          <g transform="translate(466.000000, 72.000000)">
            <g transform="translate(20.000000, 20.000000)">
              <path d="M7,14 C7,14.5522847 6.55228475,15 6,15 L5,15 C4.44771525,15 4,14.5522847 4,14 C4,13.4477153 4.44771525,13 5,13 L6,13 C6.55228475,13 7,13.4477153 7,14 Z M11,13 L10,13 C9.44771525,13 9,13.4477153 9,14 C9,14.5522847 9.44771525,15 10,15 L11,15 C11.5522847,15 12,14.5522847 12,14 C12,13.4477153 11.5522847,13 11,13 Z M6,17 L5,17 C4.44771525,17 4,17.4477153 4,18 C4,18.5522847 4.44771525,19 5,19 L6,19 C6.55228475,19 7,18.5522847 7,18 C7,17.4477153 6.55228475,17 6,17 Z M11,17 L10,17 C9.44771525,17 9,17.4477153 9,18 C9,18.5522847 9.44771525,19 10,19 L11,19 C11.5522847,19 12,18.5522847 12,18 C12,17.4477153 11.5522847,17 11,17 Z M6,5 L5,5 C4.44771525,5 4,5.44771525 4,6 C4,6.55228475 4.44771525,7 5,7 L6,7 C6.55228475,7 7,6.55228475 7,6 C7,5.44771525 6.55228475,5 6,5 Z M11,5 L10,5 C9.44771525,5 9,5.44771525 9,6 C9,6.55228475 9.44771525,7 10,7 L11,7 C11.5522847,7 12,6.55228475 12,6 C12,5.44771525 11.5522847,5 11,5 Z M6,9 L5,9 C4.44771525,9 4,9.44771525 4,10 C4,10.5522847 4.44771525,11 5,11 L6,11 C6.55228475,11 7,10.5522847 7,10 C7,9.44771525 6.55228475,9 6,9 Z M11,9 L10,9 C9.44771525,9 9,9.44771525 9,10 C9,10.5522847 9.44771525,11 10,11 L11,11 C11.5522847,11 12,10.5522847 12,10 C12,9.44771525 11.5522847,9 11,9 Z M24,10 L24,19 C23.9966939,21.7600532 21.7600532,23.9966939 19,24 L5,24 C2.23994685,23.9966939 0.00330611633,21.7600532 0,19 L0,5 C0.00330611633,2.23994685 2.23994685,0.00330611633 5,0 L11,0 C13.7600532,0.00330611633 15.9966939,2.23994685 16,5 L19,5 C21.7600532,5.00330612 23.9966939,7.23994685 24,10 Z M5,22 L14,22 L14,5 C14,3.34314575 12.6568542,2 11,2 L5,2 C3.34314575,2 2,3.34314575 2,5 L2,19 C2,20.6568542 3.34314575,22 5,22 Z M22,10 C22,8.34314575 20.6568542,7 19,7 L16,7 L16,22 L19,22 C20.6568542,22 22,20.6568542 22,19 L22,10 Z M19,13 C18.4477153,13 18,13.4477153 18,14 C18,14.5522847 18.4477153,15 19,15 C19.5522847,15 20,14.5522847 20,14 C20,13.4477153 19.5522847,13 19,13 Z M19,17 C18.4477153,17 18,17.4477153 18,18 C18,18.5522847 18.4477153,19 19,19 C19.5522847,19 20,18.5522847 20,18 C20,17.4477153 19.5522847,17 19,17 Z M19,9 C18.4477153,9 18,9.44771525 18,10 C18,10.5522847 18.4477153,11 19,11 C19.5522847,11 20,10.5522847 20,10 C20,9.44771525 19.5522847,9 19,9 Z"></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGBuilding
