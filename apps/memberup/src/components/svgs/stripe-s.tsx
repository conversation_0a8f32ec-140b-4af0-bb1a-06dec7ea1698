import React from 'react'

const SVGStripeS: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 14} height={height || 18} viewBox="0 0 14 18" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth={strokeWidth || 1} fill="none" fillRule="evenodd">
        <g transform="translate(-686.000000, -496.000000)" fill="currentColor">
          <g transform="translate(620.000000, 321.000000)">
            <g transform="translate(44.500000, 160.000000)">
              <g transform="translate(16.000000, 12.000000)">
                <path d="M11.1795276,8.36043956 C11.1795276,7.58901099 11.8346457,7.29230769 12.919685,7.29230769 C14.4755906,7.29230769 16.4409449,7.74725275 17.9968504,8.55824176 L17.9968504,3.90989011 C16.2976378,3.25714286 14.6188976,3 12.919685,3 C8.76377953,3 6,5.0967033 6,8.5978022 C6,14.0571429 13.7795276,13.1868132 13.7795276,15.5406593 C13.7795276,16.4505495 12.9606299,16.7472527 11.8141732,16.7472527 C10.1149606,16.7472527 7.94488189,16.0747253 6.22519685,15.1648352 L6.22519685,19.8725275 C8.12913386,20.6637363 10.0535433,21 11.8141732,21 C16.0724409,21 19,18.9626374 19,15.421978 C18.9795276,9.52747253 11.1795276,10.5758242 11.1795276,8.36043956 Z"></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGStripeS
