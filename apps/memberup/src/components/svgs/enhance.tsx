import React from 'react'

const SVGComment: React.FC<{ width?: number; height?: number }> = ({ width, height }) => {
  return (
    <svg width={width || 24} height={height || 24} viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-244.000000, -308.000000)">
          <g transform="translate(16.000000, 308.000000)">
            <g transform="translate(228.000000, 0.000000)">
              <path
                d="M18,0.5 C19.5187831,0.5 20.8937831,1.11560847 21.8890873,2.1109127 C22.8843915,3.10621694 23.5,4.48121694 23.5,6 L23.5,18 C23.5,19.5187831 22.8843915,20.8937831 21.8890873,21.8890873 C20.8937831,22.8843915 19.5187831,23.5 18,23.5 L6,23.5 C4.48121694,23.5 3.10621694,22.8843915 2.1109127,21.8890873 C1.11560847,20.8937831 0.5,19.5187831 0.5,18 L0.5,6 C0.5,4.48121694 1.11560847,3.10621694 2.1109127,2.1109127 C3.10621694,1.11560847 4.48121694,0.5 6,0.5 Z"
                stroke="currentColor"
              ></path>
              <g transform="translate(5.000000, 5.000000)" fill="currentColor" fillRule="nonzero">
                <path d="M11.5000624,14 C11.2723033,14.0001743 11.0673885,13.8618261 10.9826622,13.650677 L10.512602,12.4753279 L9.33411011,11.9797121 C9.1274173,11.8885411 8.99569028,11.6823941 8.99994821,11.4567634 C9.00420613,11.2311328 9.14361732,11.0300897 9.35360312,10.9467622 L10.5165006,10.5067711 L10.9826622,9.34310318 C11.0703174,9.13519502 11.2741917,9 11.5000624,9 C11.7259332,9 11.9298075,9.13519502 12.0174627,9.34310318 L12.485852,10.5112211 L13.6554329,10.9790245 C13.8640465,11.0661777 13.999848,11.2699313 13.999848,11.4957776 C13.999848,11.7216238 13.8640465,11.9253775 13.6554329,12.0125307 L12.485852,12.4803341 L12.0174627,13.648452 C11.9334446,13.8604753 11.7283727,13.9998115 11.5000624,14 Z M4.99984839,11.9998642 C4.70409679,12.0034137 4.20229986,10.8324843 3.49445761,8.48707605 C1.16088134,7.7544098 -0.00393453828,7.24075881 9.98446194e-06,6.9461231 C0.00398126792,6.65148738 1.16879714,6.17023384 3.49445761,5.5023625 C4.2369315,3.15555652 4.75672659,1.98835904 5.05384287,2.00077007 C5.34964596,2.00123666 5.83770739,3.16843414 6.51802716,5.5023625 C8.83211466,6.2085478 9.98915841,6.70788117 9.98915841,7.0003626 C9.98915841,7.29284403 8.83211466,7.78841518 6.51802716,8.48707605 C5.80165959,10.8324843 5.2956,12.0034137 4.99984839,11.9998642 Z M11.0003149,4.00027292 C10.7381908,4.00027292 10.5096797,3.82193297 10.4459835,3.56766549 L10.2419668,2.75045463 L9.42304225,2.53043632 C9.16978643,2.46239287 8.99534144,2.23071627 8.99993665,1.96851896 C9.00453185,1.70632165 9.18698783,1.48090002 9.44247242,1.42177264 L10.2425382,1.2360429 L10.4459835,0.43254747 C10.509703,0.178303799 10.7382082,0 11.0003149,0 C11.2624215,0 11.4909268,0.178303799 11.5546462,0.43254747 L11.7569485,1.24290061 L12.5673009,1.44577464 C12.8215443,1.50949411 12.999848,1.7379996 12.999848,2.00010648 C12.999848,2.26221336 12.8215443,2.49071885 12.5673009,2.55443832 L11.7569485,2.75731235 L11.5546462,3.56766549 C11.49095,3.82193297 11.2624389,4.00027292 11.0003149,4.00027292 Z"></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGComment
