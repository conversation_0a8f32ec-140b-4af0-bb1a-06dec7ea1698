import { joi<PERSON>esolver } from '@hookform/resolvers/joi'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { insertStr } from '@memberup/shared/src/libs/string-utils'
import NavigationOutlinedIcon from '@mui/icons-material/NavigationOutlined'
import SentimentSatisfiedOutlinedIcon from '@mui/icons-material/SentimentSatisfiedOutlined'
import Button from '@mui/material/Button'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import InputAdornment from '@mui/material/InputAdornment'
import TextField from '@mui/material/TextField'
import { makeStyles } from '@mui/styles'
import Joi from 'joi'
import dynamic from 'next/dynamic'
import React, { useCallback, useMemo, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { Channel as StreamChannel } from 'stream-chat'

const EmojiPicker = dynamic(() => import('@/memberup/components/common/pickers/emoji-picker'), {
  ssr: false,
})

const useStyles = makeStyles((theme) => ({
  root: {},
  contentBox: {
    position: 'relative',
    '& .MuiOutlinedInput-root': {
      backgroundColor: theme.palette.action.disabledBackground,
      borderRadius: 12,
      paddingRight: 8,
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none',
      },
    },
    '& .MuiInputAdornment-root': {
      height: 18,
      alignSelf: 'self-end',
    },
  },
  emojiPickerWrapper: {
    position: 'absolute',
    right: 8,
    top: -390,
  },
  replyButton: {
    padding: 0,
    minWidth: 22,
    marginLeft: 4,
  },
}))

type FormDataType = {
  text: string
}

const FormValue: FormDataType = {
  text: '',
}

const FormSchema = Joi.object({
  text: Joi.string().required(),
}).options({ allowUnknown: true })

const AppMessageInput: React.FC<{
  channel: StreamChannel
  visibleCancel?: boolean
  handleCancel?: () => void
}> = ({ channel, visibleCancel, handleCancel }) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const submitButtonRef = useRef(null)
  const selectionRef = useRef({
    start: 1,
    end: -1,
  })
  const { control, reset, formState, getValues, setValue, trigger, handleSubmit } =
    useForm<FormDataType>({
      mode: 'onBlur',
      reValidateMode: 'onChange',
      defaultValues: FormValue,
      resolver: joiResolver(FormSchema),
    })

  const [requestMessage, setRequestMessage] = useState(false)
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)

  const handleCreateMessage = (payload: FormDataType) => {
    try {
      setRequestMessage(true)
      channel
        .sendMessage({
          text: payload.text,
        })
        .then((res) => {
          if (res.message?.id) {
            setValue('text', '')
          }
        })
        .catch((err) => {})
        .finally(() => {
          setRequestMessage(false)
        })
    } catch (err: any) {}
  }

  const handleClickEmoji = useCallback(
    (emoji) => {
      setShowEmojiPicker(false)
      const temp = getValues('text')
      setValue('text', insertStr(temp, emoji, selectionRef.current.start, selectionRef.current.end))
    },
    [getValues, setValue]
  )

  const renderEmojiPicker = useMemo(() => {
    if (typeof window === 'undefined') return null
    return (
      <div className={classes.emojiPickerWrapper}>
        <EmojiPicker onClickEmoji={handleClickEmoji} />
      </div>
    )
  }, [classes.emojiPickerWrapper, handleClickEmoji])

  return (
    <div className={classes.root}>
      <form autoComplete="off" onSubmit={handleSubmit(handleCreateMessage)}>
        <Grid container spacing={1}>
          {/* <Grid item>
            <ProfileImage
              imageUrl={profile?.image}
              name={profile?.first_name || profile?.email}
            />
          </Grid> */}
          <Grid className={classes.contentBox} item xs>
            <Controller
              render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                <FormControl error={Boolean(error)} className="form-control" fullWidth>
                  <TextField
                    disabled={requestMessage}
                    placeholder="Write a comment..."
                    variant="outlined"
                    size="small"
                    value={value}
                    onChange={(e) => {
                      onChange(e.target.value)
                    }}
                    onBlur={(e) => {
                      selectionRef.current = {
                        start: e.target.selectionStart,
                        end: e.target.selectionEnd,
                      }
                      onBlur()
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault()
                        e.stopPropagation()
                        submitButtonRef.current?.click()
                      }
                    }}
                    InputProps={{
                      multiline: true,
                      maxRows: 4,
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            aria-label="show emoji picker"
                            color="inherit"
                            size="small"
                            onClick={() => setShowEmojiPicker((prevValue) => !prevValue)}
                          >
                            <SentimentSatisfiedOutlinedIcon color="disabled" />
                          </IconButton>
                          <IconButton
                            color="primary"
                            disabled={!formState?.isValid || requestMessage}
                            ref={submitButtonRef}
                            size="small"
                            type="submit"
                          >
                            <NavigationOutlinedIcon sx={{ transform: 'rotate(90deg)' }} />
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                </FormControl>
              )}
              control={control}
              name="text"
            />

            {showEmojiPicker && (
              <ClickAwayListener onClickAway={(e) => setShowEmojiPicker(false)}>
                {renderEmojiPicker}
              </ClickAwayListener>
            )}
          </Grid>
        </Grid>

        {visibleCancel && (
          <div className="text-right" style={{ paddingTop: 4 }}>
            <Button
              className={classes.replyButton}
              variant="text"
              size="small"
              disabled={requestMessage}
              onClick={() => handleCancel?.()}
            >
              Cancel
            </Button>
          </div>
        )}
      </form>
    </div>
  )
}

AppMessageInput.displayName = 'AppMessageInput'

export default AppMessageInput
