/* import next image */
import Image from 'next/image'
import * as pdfjsLib from 'pdfjs-dist/webpack.mjs'
import { useEffect, useRef, useState } from 'react'

const PDFtoImage = ({
  pdfData,
  width = 816,
  height = 1056,
  zoomLevel = 2,
  onThumbnailGenerated,
  showPreview = false,
}) => {
  const canvasRef = useRef(null)
  const [imageSrc, setImageSrc] = useState(null)

  useEffect(() => {
    const generateImageSrc = async () => {
      try {
        const blob = new Blob([pdfData], { type: 'application/pdf' })
        const objectUrl = URL.createObjectURL(blob)

        const loadingTask = pdfjsLib.getDocument(objectUrl)
        const pdf = await loadingTask.promise
        const page = await pdf.getPage(1)

        const originalViewport = page.getViewport({ scale: 2 })

        const scale = Math.min(width / originalViewport.width, height / originalViewport.height) * zoomLevel

        const viewport = originalViewport.clone({ scale: scale })

        const canvas = canvasRef.current
        const context = canvas.getContext('2d')

        canvas.height = height
        canvas.width = width

        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        }

        await page.render(renderContext).promise

        const dataUrl = canvas.toDataURL()
        setImageSrc(dataUrl)

        if (onThumbnailGenerated) {
          onThumbnailGenerated(dataUrl)
        }

        URL.revokeObjectURL(objectUrl)
      } catch (error) {
        console.error('Failed to fetch PDF.', error)
      }
    }

    generateImageSrc()
  }, [pdfData, width, height])

  return (
    <>
      <canvas ref={canvasRef} style={{ display: 'none' }} />
      {showPreview && imageSrc && (
        <div
          style={{
            width: `${width}px`,
            height: `${height}px`,
            overflow: 'hidden',
            borderRadius: '10px',
          }}
        >
          <Image
            src={imageSrc}
            alt="pdf"
            width={45}
            height={45}
            style={{ width: '100%', height: '100%', objectFit: 'cover', objectPosition: 'top' }}
          />
        </div>
      )}
    </>
  )
}

export default PDFtoImage
