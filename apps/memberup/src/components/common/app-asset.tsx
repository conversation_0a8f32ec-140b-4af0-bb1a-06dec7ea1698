import AudioFileIcon from '@mui/icons-material/AudioFile'
import DownloadForOfflineIcon from '@mui/icons-material/DownloadForOffline'
import HighlightOffIcon from '@mui/icons-material/HighlightOff'
import ImageIcon from '@mui/icons-material/Image'
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf'
import TextSnippetIcon from '@mui/icons-material/TextSnippet'
import Box from '@mui/material/Box'
import IconButton from '@mui/material/IconButton'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import { saveAs } from 'file-saver'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { formatBytes } from '@memberup/shared/src/libs/file'
import { IAsset } from '@memberup/shared/src/types/interfaces'

function AppAsset({ asset, onDelete }: { asset: IAsset | File; onDelete?: () => void }) {
  const theme = useTheme()
  const handleDownload = () => {
    const temp = asset as IAsset
    if (temp.url) {
      saveAs(temp.url, temp.name)
    }
  }
  const getFileIcon = () => {
    const temp = asset as IAsset
    if (temp.type === 'image/') {
      if (temp.url) {
        return <AppImg src={temp.url} alt="asset image" width={140} height={140} />
      }
      return <ImageIcon fontSize="large" />
    }
    if (temp.type === 'application/pdf') return <PictureAsPdfIcon fontSize="large" />
    if (temp.type?.indexOf('audio/') >= 0) return <AudioFileIcon fontSize="large" />
    return <TextSnippetIcon fontSize="large" />
  }

  return (
    <Box
      sx={{
        position: 'relative',
        overflow: 'hidden',
        width: 140,
      }}
    >
      <Box
        className="d-flex align-center app-image-wrapper justify-center"
        sx={{
          backgroundColor: theme.palette.divider,
          borderRadius: '12px',
          overflow: 'hidden',
          position: 'relative',
          height: 140,
          width: 140,
          '& .download-overlay': {
            display: 'none',
          },
          '& .delete-asset': {
            display: 'none',
          },
          '&:hover': {
            '& .download-overlay': {
              display: 'flex',
            },
            '& .delete-asset': {
              display: 'flex',
            },
          },
        }}
      >
        {getFileIcon()}
        {Boolean(onDelete) && (
          <Box
            className="delete-asset"
            sx={{
              width: 20,
              height: 20,
              borderRadius: '50%',
              backgroundColor: theme.palette.background.paper,
              alignItems: 'center',
              justifyContent: 'center',
              position: 'absolute',
              right: 4,
              top: 4,
              zIndex: 2,
            }}
          >
            <IconButton size="small" onClick={onDelete}>
              <HighlightOffIcon />
            </IconButton>
          </Box>
        )}
        {(asset as IAsset).url && (
          <Box
            className="h-100 w-100 download-overlay"
            sx={{
              backgroundColor: 'rgba(128, 128, 128, 0.1)',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'absolute',
              top: 0,
              left: 0,
              zIndex: 1,
            }}
          >
            <Box
              sx={{
                width: 46.63,
                height: 46.63,
                borderRadius: '50%',
                backgroundColor: theme.palette.background.paper,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <IconButton onClick={handleDownload}>
                <DownloadForOfflineIcon fontSize="large" />
              </IconButton>
            </Box>
          </Box>
        )}
      </Box>
      <Typography className="text-ellipsis" sx={{ mt: 2, ml: 1 }}>
        <b>{asset.name}</b>
      </Typography>
      <Typography color="text.disabled" variant="body2" sx={{ mt: '2px', ml: 1 }}>
        {formatBytes(asset.size)}
      </Typography>
    </Box>
  )
}

AppAsset.displayName = 'AppAsset'

export default AppAsset
