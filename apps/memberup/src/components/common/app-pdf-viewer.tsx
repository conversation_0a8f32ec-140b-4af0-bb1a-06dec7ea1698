import dynamic from 'next/dynamic'
import React from 'react'

const SharedAppPdfViewer = dynamic(() => import('@memberup/shared/src/components/common/app-pdf-viewer'), {
  ssr: false,
})

function AppPdfViewer(props: { showHeader?: boolean; showDownloadButton?: boolean; name?: string; url: string }) {
  const { name, url, ...rest } = props

  return <SharedAppPdfViewer name={name} url={url} {...rest} />
}

AppPdfViewer.displayName = 'AppPdfViewer'

export default React.memo(AppPdfViewer)
