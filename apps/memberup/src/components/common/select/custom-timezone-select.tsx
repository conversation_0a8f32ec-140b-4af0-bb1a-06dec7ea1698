import MenuItem from '@mui/material/MenuItem'
import Select from '@mui/material/Select'
import { allTimezones, useTimezoneSelect } from 'react-timezone-select'

const CustomTimezoneSelect = ({ className, sx, value, onChange, disabled = false }) => {
  const { options, parseTimezone } = useTimezoneSelect({
    labelStyle: 'original',
    timezones: allTimezones,
  })

  const handleTimezoneChange = (event) => {
    const timezone = parseTimezone(event.target.value)
    if (timezone) {
      onChange(timezone)
    }
  }

  return (
    <Select
      value={value?.value || value || ''}
      onChange={handleTimezoneChange}
      className={className}
      sx={sx}
      data-cy="timezone-select"
      disabled={disabled}
    >
      {options.map((option) => (
        <MenuItem key={option.value} value={option.value} data-cy="timezone">
          {option.label}
        </MenuItem>
      ))}
    </Select>
  )
}

export default CustomTimezoneSelect
