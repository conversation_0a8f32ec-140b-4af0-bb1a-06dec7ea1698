import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import CardHeader from '@mui/material/CardHeader'
import CardMedia from '@mui/material/CardMedia'
import Divider from '@mui/material/Divider'
import IconButton from '@mui/material/IconButton'
import Menu from '@mui/material/Menu'
import MenuItem from '@mui/material/MenuItem'
import useTheme from '@mui/material/styles/useTheme'
import useMediaQuery from '@mui/material/useMediaQuery'
import { makeStyles } from '@mui/styles'
import * as React from 'react'

import SVGDownload from '../svgs/download'
import AppPdfViewer from './app-pdf-viewer'
import MediaTypeIcon from './media-type-icon'
import {
  DeleteFileHandler,
  DownloadFileHandler,
  OpenInNewTabHandler,
  RemovePreviewHandler,
  TAttachment,
} from '@memberup/shared/src/types/types'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import { selectUser } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => {
  return {
    header: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderRadius: '16px 0 0',
      opacity: 1,
      backgroundColor: theme.palette.mode === 'dark' ? '#29292c' : '#edeeef', // NOTE: This was picked with the color picker, which is the solid representation of that color over the background.
      // Make the title grow to fill the available space
      '& .MuiCardHeader-title': {
        flex: 1,
      },
    },
    title: {
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',

      opacity: 1,
      color: theme.palette.mode === 'dark' ? '#e3e3e4' : '#1e1e1e',
      fontFamily: 'Graphik Medium',
      fontSize: '14px',
      fontWeight: 500,
      fontStyle: 'normal',
      letterSpacing: '0px',
      textAlign: 'left',
      lineHeight: '16px',
    },

    subheader: {
      opacity: 1,
      color: theme.palette.mode === 'dark' ? '#8d94a3' : '#585D66',
      fontFamily: 'Graphik Regular',
      fontSize: '12px',
      fontWeight: 400,
      fontStyle: 'normal',
      letterSpacing: '0px',
      textAlign: 'left',
    },
    actionIcon: {
      color: theme.palette.mode === 'dark' ? '#8d94a3' : '#585d66',
    },
  }
})

const MediaPdfPreview: React.FC<{
  feed: any
  file: TAttachment
  handleDownloadFile: DownloadFileHandler
  previewMode?: boolean
  handleRemovePreview?: RemovePreviewHandler
  handleOpenInNewTab: OpenInNewTabHandler
  handleDeleteFile: DeleteFileHandler
}> = ({
  feed,
  file,
  handleDownloadFile,
  previewMode = false,
  handleRemovePreview,
  handleOpenInNewTab,
  handleDeleteFile,
}) => {
  const classes = useStyles()

  const { isCurrentUserAdmin } = useCheckUserRole()
  const user = useAppSelector((state) => selectUser(state))
  const isFeedCreator = feed.user.id === user?.id
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  const editable = isCurrentUserAdmin || isFeedCreator

  function formatBytesToMegabytes(bytes: number) {
    const megabytes = bytes / 1048576 // 2^20 = 1,048,576
    return `${megabytes.toFixed(2)} MB` // Format to 2 decimal places and append "MB"
  }

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const handleMenuButtonClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }

  if (!file) {
    return null
  }

  return (
    <Box sx={{ display: 'flex', mt: '20px', pb: 0 }}>
      <Card
        sx={{
          borderRadius: '12px',
          border: '1px solid rgba(88,93,102, 0.12)',
          boxShadow: '0px 0px 0px 1px rgba(88,93,102, 0.12) inset',
          opacity: 1,
          maxWidth: isMobile ? '100%' : '672px',
          maxHeight: '450px',
          margin: 'auto',
          marginBottom: '16px',
        }}
      >
        <CardHeader
          className={classes.header}
          avatar={<MediaTypeIcon file={file} />}
          title={file.filename}
          classes={{
            title: classes.title,
            subheader: classes.subheader,
          }}
          action={
            <>
              <IconButton
                className={classes.actionIcon}
                aria-label="settings"
                onClick={() => handleDownloadFile(file)}
                sx={{
                  color: theme.palette.mode === 'dark' ? '#8c93a2' : '#575c65',
                  '&:hover': {
                    color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000',
                  },
                }}
              >
                <SVGDownload />
              </IconButton>
              <IconButton
                sx={{
                  backgroundColor: open ? (theme.palette.mode === 'dark' ? '#323235' : '#dfe3e4') : 'inherit',
                  color: open
                    ? theme.palette.mode === 'dark'
                      ? '#ffffff !important'
                      : '#000000 !important'
                    : theme.palette.mode === 'dark'
                      ? '#8d94a3'
                      : '#585d66',
                  '&:hover': {
                    color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000',
                  },
                }}
                aria-label="settings"
                onClick={handleMenuButtonClick}
              >
                <MoreHorizIcon />
              </IconButton>
            </>
          }
          subheader={(file.size_in_bytes && formatBytesToMegabytes(file.size_in_bytes)) || '-'}
        />

        {previewMode ? (
          <CardMedia component="img" image={file.thumbnail} alt="" />
        ) : (
          <Box sx={{ width: '672px', height: '400px' }}>
            <AppPdfViewer showHeader={false} showDownloadButton={false} url={file.url} />
          </Box>
        )}

        <CardContent>
          <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
            onClick={handleClose}
            sx={{
              '& .MuiBackdrop-root': {
                backgroundColor: 'transparent',
              },
            }}
            PaperProps={{
              sx: {
                mt: '6px',
                '& .MuiList-root': {
                  paddingTop: 0,
                  paddingBottom: 0,
                },
                '& .MuiDivider-root': {
                  marginTop: 0,
                  marginBottom: 0,
                },
                '& .MuiMenuItem-root': {
                  p: '12px',
                  alignItems: 'flex-start',
                },
                '& .MuiListItemIcon-root': {
                  color: '#8D94A3',
                  fontSize: 18,
                  minWidth: 28,
                },
                '& .MuiSvgIcon-root': {
                  fontSize: 18,
                },
                '& .MuiTypography-body2': {
                  color: '#8D94A3',
                },
              },
            }}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            <MenuItem onClick={() => handleOpenInNewTab(file.url)} data-cy="open-in-new-tab">
              Open In New Tab
            </MenuItem>

            {editable && [
              <Divider sx={{ my: 0.5 }} />,
              <MenuItem onClick={() => handleRemovePreview?.(feed, file)} data-cy="remove-pdf-preview">
                Remove Preview
              </MenuItem>,
              <MenuItem onClick={() => handleDeleteFile(feed, file)} data-cy="delete-pdf-file">
                Delete File
              </MenuItem>,
            ]}
          </Menu>
        </CardContent>
      </Card>
    </Box>
  )
}

export default MediaPdfPreview
