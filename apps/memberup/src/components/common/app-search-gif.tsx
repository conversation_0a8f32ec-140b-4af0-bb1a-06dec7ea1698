import { Gip<PERSON>Fetch } from '@giphy/js-fetch-api'
import { IGif } from '@giphy/js-types'
import { Grid as GiphyGrid, SearchContextManager } from '@giphy/react-components'
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import useTheme from '@mui/material/styles/useTheme'
import TextField from '@mui/material/TextField'
import useMediaQuery from '@mui/material/useMediaQuery'
import React, { useState } from 'react'

import SVGCloseNew from '../svgs/close-new'
import SVGSearch from '../svgs/search'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import { selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const GIPHY_API_KEY = process.env.NEXT_PUBLIC_GIPHY_API_KEY
const gf = new GiphyFetch(GIPHY_API_KEY)

const AppSearchGifContent: React.FC<{
  onGifClick: (gif: IGif, e: React.SyntheticEvent<HTMLElement, Event>) => void
  onClose: () => void
}> = ({ onGifClick, onClose }) => {
  const theme = useTheme()
  const [searchInput, setSearchInput] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  const membershipSetting = useAppSelector(selectMembershipSetting)

  const fetchGifs = (offset: number) => {
    return searchTerm ? gf.search(searchTerm, { offset, limit: 10 }) : gf.trending({ offset, limit: 10 })
  }

  return (
    <Box className="d-flex direction-column h-100">
      <Grid container spacing={2} sx={{ p: '16px', pr: isMobile ? '38px' : '28px', pb: 0 }}>
        <Grid item xs={11} sm={11}>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              backgroundColor: membershipSetting?.theme_mode === THEME_MODE_ENUM.dark ? '#212126' : '#f2f2f3',
              borderRadius: '12px',
              height: '48px',
            }}
          >
            <TextField
              fullWidth
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  setSearchTerm(searchInput)
                }
              }}
              variant="outlined"
              placeholder="Search GIFs"
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    border: 'none',
                  },
                  backgroundColor: 'transparent',
                },
              }}
              InputProps={{
                style: {
                  color: '#FFFFFF',
                },
                sx: {
                  '& .MuiOutlinedInput-input': {
                    color: membershipSetting?.theme_mode === THEME_MODE_ENUM.dark ? '#FFFFFF' : '#000000',
                    '&::placeholder': {
                      color:
                        membershipSetting?.theme_mode === THEME_MODE_ENUM.dark
                          ? '#8d94a3 !important'
                          : '#585D66 !important',
                      opacity: 1,
                    },
                  },
                },
              }}
            />
            <IconButton
              onClick={() => setSearchTerm(searchInput)}
              sx={{
                marginLeft: '-40px',
                padding: '8px',
                color: '#8D94A3',
              }}
            >
              <SVGSearch />
            </IconButton>
          </Box>
        </Grid>
        <Grid item xs={1} sm={1}>
          <IconButton
            sx={{
              width: '48px',
              height: '48px',
              color: '#8D94A3',
              backgroundColor: membershipSetting?.theme_mode === THEME_MODE_ENUM.dark ? '#212126' : '#f2f2f3',
            }}
            onClick={onClose}
          >
            <SVGCloseNew />
          </IconButton>
        </Grid>
      </Grid>
      <Box
        className="w-100"
        sx={{
          m: isMobile ? '15px 0px' : '16px',
          textAlign: '-webkit-center',
          flex: 1,
          overflow: 'auto',
        }}
      >
        <GiphyGrid
          key={searchTerm}
          columns={isMobile ? 2 : 3}
          noLink={true}
          hideAttribution={true}
          width={isMobile ? 300 : 555}
          fetchGifs={fetchGifs}
          onGifClick={onGifClick}
        />
      </Box>
    </Box>
  )
}

const AppSearchGif: React.FC<{
  onSelectGif: (git: any) => void
  onClose: () => void
}> = ({ onSelectGif, onClose }) => {
  return (
    <SearchContextManager apiKey={GIPHY_API_KEY}>
      <AppSearchGifContent
        onGifClick={(gif, e) => {
          e.preventDefault()
          e.stopPropagation()
          onSelectGif({
            id: gif.id,
            is_gif: true,
            title: gif.title,
            type: gif.type,
            url: gif.url,
            embed_url: gif.embed_url,
          })
        }}
        onClose={onClose}
      />
    </SearchContextManager>
  )
}

export default AppSearchGif
