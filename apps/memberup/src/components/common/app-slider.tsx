import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew'
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos'
import IconButton from '@mui/material/IconButton'
import useTheme from '@mui/material/styles/useTheme'
import React, { useMemo } from 'react'
import Slider from 'react-slick'

const PrevArrow = (props) => {
  const { currentSlide, slideCount, ...rest } = props
  const theme = useTheme()
  if (currentSlide === 0) return null
  return (
    <IconButton
      {...rest}
      sx={{
        backgroundColor: `${theme.components.MuiCard.styleOverrides.root['backgroundColor']}!important`,
        boxShadow: '-0.5px 1px 2px rgba(0,0,0, 0.1)',
        width: '40px!important',
        height: '40px!important',
        ml: '15px',
        '&.slick-prev': {
          opacity: 0.95,
          zIndex: 1,
          top: 'calc(50% - 20px)',
          '&:before': { display: 'none' },
          '&:hover': {
            opacity: 1,
          },
        },
      }}
      aria-hidden="true"
    >
      <ArrowBackIosNewIcon sx={{ color: 'gray' }} />
    </IconButton>
  )
}

const NextArrow = (props) => {
  const { currentSlide, slideCount, ...rest } = props
  const theme = useTheme()
  if (Math.ceil(currentSlide) === slideCount - 1) return null
  return (
    <IconButton
      {...rest}
      sx={{
        backgroundColor: `${theme.components.MuiCard.styleOverrides.root['backgroundColor']}!important`,
        boxShadow: '1px 1px 1px rgba(0,0,0, 0.1)',
        width: '40px!important',
        height: '40px!important',
        mr: '15px',
        '&.slick-next': {
          opacity: 0.95,
          top: 'calc(50% - 20px)',
          '&:before': { display: 'none' },
          '&:hover': {
            opacity: 1,
          },
        },
      }}
      aria-hidden="true"
    >
      <ArrowForwardIosIcon sx={{ color: 'gray' }} />
    </IconButton>
  )
}

const AppSlider: React.FC<{
  settings: any
  slidesToShow?: number
  visibleButtons?: boolean
  children
}> = ({ settings, slidesToShow, visibleButtons = true, children }) => {
  const slickSettings = useMemo(
    () => ({
      className: 'slider',
      infinite: false,
      slidesToShow: 4,
      slidesToScroll: 1,
      afterChange: function (index) {},
      nextArrow: visibleButtons && <NextArrow />,
      prevArrow: visibleButtons && <PrevArrow />,
      ...settings,
    }),
    [slidesToShow, settings, visibleButtons],
  )

  return <Slider {...slickSettings}>{children}</Slider>
}

AppSlider.displayName = 'AppSlider'

export default AppSlider
