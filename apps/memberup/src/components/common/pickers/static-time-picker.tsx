import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import useTheme from '@mui/material/styles/useTheme'
import TextField from '@mui/material/TextField'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { StaticTimePicker } from '@mui/x-date-pickers/StaticTimePicker'
import React, { useState } from 'react'

import { adjustRGBA } from '@memberup/shared/src/libs/color'

const CustomStaticTimePicker: React.FC<{
  disabled
  value
  style?
  onChange: (e) => void
  onCancel: () => void
  [x: string]: any
}> = ({ disabled, value, style, onChange, onCancel, ...rest }) => {
  const theme = useTheme()
  const [newValue, setNewValue] = useState(value || new Date())

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box
        sx={{
          overflow: 'hidden',
          width: { xs: 320 },
          '& .MuiTimePickerToolbar-ampmSelection': {
            '& .MuiTimePickerToolbar-ampmLabel.Mui-selected': {
              fontWeight: 'bold',
            },
          },
          '& .MuiTimePickerToolbar-separator': {
            marginLeft: '4px',
            marginRight: '4px',
          },
          '& .MuiDialogActions-root': {
            display: 'none',
          },
          ...(style || {}),
        }}
      >
        <StaticTimePicker
          disabled={disabled}
          displayStaticWrapperAs="mobile"
          value={newValue}
          onChange={(e) => {
            setNewValue(e)
          }}
          renderInput={(params) => <TextField size="small" {...(params as any)} />}
          {...rest}
        />
        {!disabled && (
          <Box className="text-right" sx={{ backgroundColor: '#000000', p: '8px 12px' }}>
            <Button className="no-padding" variant="text" onClick={onCancel}>
              Cancel
            </Button>
            <Button
              className="no-padding"
              variant="text"
              disabled={value === newValue}
              sx={{
                '&.Mui-disabled': {
                  color: 'rgba(255, 255, 255, 0.4)',
                },
                backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
              }}
              onClick={() => onChange(newValue)}
            >
              Save
            </Button>
          </Box>
        )}
      </Box>
    </LocalizationProvider>
  )
}

export default CustomStaticTimePicker
