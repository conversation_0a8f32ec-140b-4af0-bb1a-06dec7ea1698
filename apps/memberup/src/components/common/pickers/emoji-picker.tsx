import Box from '@mui/material/Box'
import useTheme from '@mui/material/styles/useTheme'
import EmojiPicker, {
  EmojiClickData,
  EmojiStyle,
  SkinTonePickerLocation,
  SkinTones,
  SuggestionMode,
} from 'emoji-picker-react'
import React from 'react'

const AppEmojiPicker: React.FC<{ onClickEmoji: (emoji, event?: MouseEvent) => void }> = ({ onClickEmoji }) => {
  const theme = useTheme()
  const onClick = (emojiData: EmojiClickData, event: MouseEvent) => {
    if (emojiData.emoji === '🫡') return
    onClickEmoji(emojiData.emoji, event)
  }
  return (
    <Box
      sx={{
        '& .EmojiPickerReact': {
          backgroundColor: theme.palette.background.paper,
          color: theme.palette.text.primary,
          zIndex: 9999,
          '& [data-name="suggested"]': {
            display: 'none',
          },
          '& .epr-search-container input.epr-search, & li.epr-emoji-category>.epr-emoji-category-label': {
            // backgroundColor: theme.palette.divider,
            // borderColor: theme.palette.divider,
            display: 'none',
          },
          '& .epr-preview': {
            borderTopColor: theme.palette.divider,
          },
        },
        '& aside.EmojiPickerReact.epr-main': {
          borderColor: theme.palette.divider,
        },
      }}
    >
      <EmojiPicker
        lazyLoadEmojis={true}
        skinTonePickerLocation={SkinTonePickerLocation.PREVIEW}
        suggestedEmojisMode={SuggestionMode.RECENT}
        defaultSkinTone={SkinTones.NEUTRAL}
        //emojiStyle={EmojiStyle.APPLE}
        onEmojiClick={onClick}
      />
    </Box>
  )
}

export default AppEmojiPicker
