import TextField from '@mui/material/TextField'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { TimePicker } from '@mui/x-date-pickers/TimePicker'
import React from 'react'

const CustomTimePicker: React.FC<{
  value
  label?: string
  handleChange: (e) => void
  [x: string]: any
}> = ({ value, label, handleChange, ...rest }) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <TimePicker
        label={label}
        value={value}
        onChange={(newValue) => {
          handleChange(newValue)
        }}
        renderInput={(params) => <TextField size="small" {...(params as any)} />}
        {...rest}
      />
    </LocalizationProvider>
  )
}

export default CustomTimePicker
