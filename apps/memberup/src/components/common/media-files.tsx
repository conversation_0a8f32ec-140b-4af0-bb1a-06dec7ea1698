import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import IconButton from '@mui/material/IconButton'
import Paper from '@mui/material/Paper'
import { styled } from '@mui/material/styles'
import useTheme from '@mui/material/styles/useTheme'
import Grid from '@mui/material/Unstable_Grid2'
import { makeStyles } from '@mui/styles'
import React from 'react'

import SVGDownload from '../svgs/download'
import MediaTypeIcon from './media-type-icon'
import { shortenFileName } from '@/shared-libs/string-utils'
import { DownloadFileHandler, TAttachment } from '@/shared-types/types'

const Icon = styled(Paper)({
  width: '40px',
  height: '40px',

  borderRadius: '12px',
  opacity: 1,
  backgroundColor: 'rgba(88,93,102, 0.08)',
})

const Item = styled(Paper)({
  height: '56px',
  padding: '8px',

  // Copied
  borderRadius: '16px',
  opacity: 1,
  backgroundColor: 'rgba(88,93,102, 0.04)', // Note: Only the last background-color will be applied. The previous one has been overwritten.
})

const useStyles = makeStyles((theme) => {
  return {
    cardHeader: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderRadius: '16px',
      opacity: 1,
      padding: '12px',
      backgroundColor: theme.palette.mode === 'dark' ? '#29292c' : '#edeeef', // NOTE: This was picked with the color picker, which is the solid representation of that color over the background.
      // Make the title grow to fill the available space
      '& .MuiCardHeader-title': {
        flex: 1,
      },
      '& .MuiCardHeader-action': {
        alignSelf: 'center',
        margin: 0,
      },
    },
    title: {
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      maxWidth: '220px',
      opacity: 1,
      color: theme.palette.mode === 'dark' ? '#e3e3e4' : '#1e1e1e',
      fontFamily: 'Graphik Medium',
      fontSize: '14px',
      fontWeight: 500,
      fontStyle: 'normal',
      letterSpacing: '0px',
      textAlign: 'left',
      lineHeight: '16px',
    },

    subheader: {
      opacity: 1,
      color: theme.palette.mode === 'dark' ? '#8d94a3' : '#585D66',
      fontFamily: 'Graphik Regular',
      fontSize: '12px',
      fontWeight: 400,
      fontStyle: 'normal',
      letterSpacing: '0px',
      textAlign: 'left',
    },
  }
})

function formatBytesToMegabytes(bytes: number) {
  const megabytes = bytes / 1048576 // 2^20 = 1,048,576
  return `${megabytes.toFixed(2)} MB` // Format to 2 decimal places and append "MB"
}

const MediaFiles: React.FC<{
  files: TAttachment[]
  handleDownloadFile: DownloadFileHandler
  verticalOnly?: boolean
}> = ({ files, handleDownloadFile, verticalOnly }) => {
  const classes = useStyles()
  const theme = useTheme()
  const windowWidth = window.innerWidth

  if (files.length === 0) return null

  return (
    <Box className="media-files" sx={{ width: '100%', marginTop: '16px', marginBottom: '16px' }}>
      <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 3 }}>
        {files.map((file, index) => (
          <Grid key={index} xs={12} sm={verticalOnly ? 12 : 6}>
            <Card>
              <CardHeader
                className={classes.cardHeader}
                avatar={<MediaTypeIcon file={file} />}
                action={
                  <IconButton
                    sx={{
                      color: theme.palette.mode === 'dark' ? '#8c93a2' : '#575c65',
                      '&:hover': {
                        color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000',
                      },
                    }}
                    aria-label="settings"
                    onClick={() => handleDownloadFile(file)}
                  >
                    <SVGDownload />
                  </IconButton>
                }
                title={shortenFileName(file.filename, windowWidth < 600 ? 2 : 20)}
                classes={{
                  title: classes.title,
                  subheader: classes.subheader,
                }}
                subheader={(file.size_in_bytes && formatBytesToMegabytes(file.size_in_bytes)) || ''}
              />
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  )
}

export default MediaFiles
