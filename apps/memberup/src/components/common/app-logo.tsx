import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import React, { useMemo } from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { selectMembership, selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

function AppLogo(props: { height?: any; width?: any; style?: any; textStyle?: any }) {
  const { height, width, style, textStyle } = props
  const membership = useAppSelector((state) => selectMembership(state))
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))

  const renderLogo = useMemo(() => {
    if (!membershipSetting?.logo) return null
    return (
      <AppImg
        src={membershipSetting.logo}
        cropArea={membershipSetting.logo_crop_area}
        cropAreaAspect={3.65}
        height={height || 52}
        width={width || 208}
        alt="logo"
        style={{ ...style }}
      />
    )
  }, [membershipSetting?.logo])

  return (
    <Box
      className="app-image-wrapper"
      sx={{
        position: 'relative',
        width: '100%',
        height: '100%',
        color: 'inherit',
        overflow: 'hidden',
      }}
    >
      {membershipSetting?.logo ? (
        renderLogo
      ) : (
        <Typography
          className="bold mt-1 text-ellipsis lg:mt-3"
          color="inherit"
          variant="h4"
          sx={{ ...textStyle }}
          style={{
            fontSize: '22px',
            width: '100%',
            textTransform: 'uppercase',
            wordBreak: 'break-word',
            position: 'relative',
          }}
        >
          {membership?.name || ''}
        </Typography>
      )}
    </Box>
  )
}

AppLogo.displayName = 'AppLogo'

export default React.memo(AppLogo)
