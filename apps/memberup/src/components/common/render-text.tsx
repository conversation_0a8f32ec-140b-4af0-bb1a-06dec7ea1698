import Popper from '@mui/material/Popper'
import useTheme from '@mui/material/styles/useTheme'
import { AnimatePresence, motion } from 'framer-motion'
import { useEffect, useState } from 'react'

import MentionCard from './MentionCard'
import { MENTION_MARKUP_EVERYONE } from '@memberup/shared/src/types/consts'
import { IUser } from '@memberup/shared/src/types/interfaces'
import { isMention } from '@/memberup/libs/mentions'
import { getFullName } from '@/shared-libs/profile'

function RenderText({
  part,
  userData,
  viewType = 'single',
}: {
  part: string
  userData: IUser | null | { first_name: string; last_name: string }
  viewType?: 'condensed' | 'single' | 'comment'
}) {
  const theme = useTheme()
  const [anchorEl, setAnchorEl] = useState(null)

  const [isAnimatingOut, setIsAnimatingOut] = useState(false)

  const handleMouseEnter = (e) => {
    if (part !== 'everyone' || viewType !== 'condensed') {
      setAnchorEl(e.currentTarget)
      setIsAnimatingOut(false)
    }
  }

  const handleMouseLeave = (e) => {
    if (part !== 'everyone' || viewType !== 'condensed') {
      setIsAnimatingOut(true)
    }
  }

  useEffect(() => {
    if (isAnimatingOut) {
      const timer = setTimeout(() => {
        setAnchorEl(null)
      }, 500) // Match this with your animation duration

      return () => clearTimeout(timer)
    }
  }, [isAnimatingOut])

  const open = Boolean(anchorEl)
  const id = open ? 'simple-poper' : undefined

  const fadeOut = {
    opacity: 0,
    y: -10,
    transition: { duration: 0.5 },
  }

  const renderContent = () => {
    if (isMention(part)) {
      return (
        <>
          <span
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            className="mention-name"
            style={{
              color: theme.palette.primary.main,
              fontFamily: 'Graphik Semibold',
              fontWeight: 400,
            }}
          >
            {getFullName(userData.first_name, userData.last_name, '')}
          </span>
          {part !== MENTION_MARKUP_EVERYONE && viewType !== 'condensed' && (
            <AnimatePresence>
              {open && (
                <Popper id={id} open={open} anchorEl={anchorEl} style={{ zIndex: 9999 }}>
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={fadeOut}
                    transition={{ duration: 0.5 }}
                  >
                    <MentionCard userData={userData} />
                  </motion.div>
                </Popper>
              )}
            </AnimatePresence>
          )}{' '}
        </>
      )
    }
    //return viewType === 'condensed' ? part : <p>{part}</p>
    return viewType === 'condensed' ? part : part
  }

  return renderContent()
}

export default RenderText
