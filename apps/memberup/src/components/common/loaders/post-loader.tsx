import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Skeleton from '@mui/material/Skeleton'
import Stack from '@mui/material/Stack'
import useTheme from '@mui/material/styles/useTheme'
import React from 'react'

const PostLoader = () => {
  const theme = useTheme()
  return (
    <Card
      sx={{
        borderRadius: '8px',
        padding: '12px',
        width: '100%',
        backgroundColor: theme.palette.mode === 'dark' ? '#212124' : '#fff',
      }}
    >
      <CardContent style={{ padding: '8px', paddingBottom: 0 }}>
        <Stack spacing={2}>
          <Stack direction="row" alignItems="center" spacing={3} sx={{ mb: 3 }}>
            <Skeleton variant="circular" width={36} height={36} />
            <Stack spacing={2}>
              <Skeleton variant="rounded" width={90} height={12} />
              <Skeleton variant="rounded" width={116} height={12} />
            </Stack>
          </Stack>
          <Skeleton variant="rounded" height={12} />
          <Skeleton variant="rounded" height={12} />
          <Skeleton variant="rounded" height={12} style={{ marginBottom: '24px', width: '80%' }} />
          <Stack direction="row" alignItems="center" spacing={3}>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Skeleton variant="circular" width={24} height={24} />
              <Skeleton variant="rounded" width={54} height={10} />
            </Stack>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Skeleton variant="circular" width={24} height={24} />
              <Skeleton variant="rounded" width={72} height={10} />
            </Stack>
            {/* <Stack
              direction="row"
              alignItems="center"
              justifyContent="flex-end"
              spacing={2}
              flex={1}
            >
              <Stack direction="row">
                <Skeleton variant="circular" width={20} height={20} />
                {[1, 2, 3, 4, 5, 6].map((item) => (
                  <Skeleton
                    key={item}
                    variant="circular"
                    width={20}
                    height={20}
                    sx={{
                      ml: '-8px',
                      borderWidth: 2,
                      borderStyle: 'solid',
                      borderColor: 'transparent',
                    }}
                  />
                ))}
              </Stack>
              <Skeleton variant="rounded" width={70} height={10} />
            </Stack> */}
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  )
}

export default React.memo(PostLoader)
