import Skeleton from '@mui/material/Skeleton'
import Stack from '@mui/material/Stack'
import React from 'react'

const SpaceLoader: React.FC<{
  animation?: 'pulse' | 'wave' | false
}> = ({ animation = 'pulse' }) => {
  return (
    <Stack spacing={2}>
      <Skeleton animation={animation} variant="rounded" height={240} sx={{ borderRadius: '12px' }} />
    </Stack>
  )
}

export default React.memo(SpaceLoader)
