import CircularProgress from '@mui/material/CircularProgress'
import React from 'react'

const LoadingSpinner: React.FC<{
  size?: number
  textNode?: React.ReactNode
}> = ({ size = 32, textNode }) => {
  return (
    <div className="app-loading-wrapper" style={{ minHeight: `${size}px` }}>
      <div
        className="app-loading-backdrop"
        onClick={(e) => {
          e.preventDefault()
          e.stopPropagation()
        }}
      ></div>
      <CircularProgress size={size} />
      {textNode}
    </div>
  )
}

export default React.memo(LoadingSpinner)
