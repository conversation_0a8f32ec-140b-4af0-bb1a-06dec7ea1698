import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import React from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { selectMembershipAssetsPath } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    padding: 8,
    textAlign: 'center',
    '& .MuiButton-root': {
      color: 'inherit',
      borderColor: 'inherit',
    },
    '& img': {
      maxWidth: '100%',
    },
  },
}))

const DesktopOnly: React.FC<{ onGoBack: () => void }> = ({ onGoBack }) => {
  const classes = useStyles()
  const membershipAssetsPath = useAppSelector((state) => selectMembershipAssetsPath(state))

  return (
    <Box className={classes.root}>
      <Box>
        <Typography variant="h4">Your experience is everthing to us</Typography>
        <br />
        <br />
        <Typography className="color02" variant="body1" sx={{ maxWidth: 350 }}>
          This feature is only available in bigger screens. To use this feature, please sign in on desktop.
        </Typography>
        <br />
        <br />
        <AppImg
          src={`${membershipAssetsPath}/images/desktop-only.png`}
          height={100}
          width={100}
          alt="Responsive Notice"
          style={{
            display: 'inline-block',
          }}
        />
        <br />
        <br />
        <br />
        <br />
        <Button className="round-small" variant="outlined" onClick={onGoBack}>
          Go Back
        </Button>
      </Box>
    </Box>
  )
}

export default DesktopOnly
