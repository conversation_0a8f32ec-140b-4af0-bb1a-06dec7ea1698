import { IUser } from '@memberup/shared/src/types/interfaces'
import { UserDetailsHoverCard } from '@/components/community/user-details-hover-card'
import { cn } from '@/lib/utils'
import { getFullName } from '@/shared-libs/profile'

function RenderMention({
  isEveryone,
  userData,
}: {
  isEveryone: boolean
  userData: IUser | null | { first_name: string; last_name: string }
}) {
  const renderContent = () => {
    const spanClassName = 'text-community-primary font-semibold'

    if (isEveryone) {
      return <span className={spanClassName}>@{userData.first_name}</span>
    }

    return (
      <UserDetailsHoverCard username={(userData as IUser).username}>
        <span className={cn(spanClassName, 'cursor-pointer')}>
          @{getFullName(userData.first_name, userData.last_name, '')}
        </span>
      </UserDetailsHoverCard>
    )
  }
  return renderContent()
}

export default RenderMention
