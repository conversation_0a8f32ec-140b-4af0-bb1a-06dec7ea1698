import { makeStyles } from '@mui/styles'
import { ContentBlock, ContentState } from 'draft-js'
import * as React from 'react'

const useStyles = makeStyles((theme) => ({
  postLink: {
    color: theme.palette.primary.main,
    fontWeight: 'bold',
    textDecoration: 'none',
    cursor: 'pointer',
    '& :hover': {
      textDecoration: 'underline',
    },
  },
}))

export function DecoratedLink({
  className,
  children,
  entityKey,
  target,
  contentState,
}: {
  className: string
  children: React.ReactNode
  entityKey: string
  target?: string
  contentState: any
}) {
  const entity = contentState.getEntity(entityKey)
  const entityData = entity ? entity.getData() : undefined
  const href = (entityData && entityData.url) || undefined

  const classes = useStyles()

  return (
    <a className={classes.postLink} title={href} href={href} target={'_blank'} rel="noopener">
      {children}
    </a>
  )
}
