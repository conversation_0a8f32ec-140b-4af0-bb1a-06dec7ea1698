import React, { useEffect, useRef, useState } from 'react'

import MemberRequestCard from '@/components/community/members/member-request-card'
import { SkeletonBox } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { toast } from '@/components/ui/sonner'
import { useMemberRequests } from '@/hooks/useMemberRequests'
import { unexpectedError } from '@/lib/error-messages'
import { approveMemberRequestApi, rejectMemberRequestApi } from '@/shared-services/apis/membership.api'
import { IMemberRequest } from '@/shared-types/interfaces'

const APPROVE_ACTION = 'approve'
const DECLINE_ACTION = 'decline'

export default function MembersRequests() {
  const { memberRequests, loading: requestsLoading, fetchMemberRequests } = useMemberRequests()
  const [openConfirm, setOpenConfirm] = useState(false)
  const [selectedMemberRequest, setSelectedMemberRequest] = useState<IMemberRequest | null>(null)
  const [action, setAction] = useState(null)
  const [loading, setLoading] = useState(false)
  const isFirstLoad = useRef(true)

  useEffect(() => {
    if (!requestsLoading) {
      isFirstLoad.current = false
    }
  }, [requestsLoading])

  const handleApproveClick = (memberRequest: IMemberRequest) => {
    setOpenConfirm(true)
    setSelectedMemberRequest(memberRequest)
    setAction(APPROVE_ACTION)
  }

  const handleDeclineClick = (memberRequest: IMemberRequest) => {
    setOpenConfirm(true)
    setSelectedMemberRequest(memberRequest)
    setAction(DECLINE_ACTION)
  }

  const handleConfirmProceed = async () => {
    if (!selectedMemberRequest) return

    setLoading(true)
    try {
      if (action === APPROVE_ACTION) {
        await approveMemberRequestApi(selectedMemberRequest.id)
        await fetchMemberRequests()
        toast.success('The member request has been approved successfully')
      } else {
        await rejectMemberRequestApi(selectedMemberRequest.id)
        await fetchMemberRequests()
        toast.success('The member request has been rejected successfully')
      }
    } catch (error) {
      toast.error(unexpectedError)
    }

    setLoading(false)
    setOpenConfirm(false)
  }

  const handleConfirmClose = () => {
    setOpenConfirm(false)
    setSelectedMemberRequest(null)
    setAction(null)
  }

  return (
    <>
      {requestsLoading && isFirstLoad.current && <SkeletonBox />}

      {memberRequests.map((mr) => (
        <MemberRequestCard
          key={mr.id}
          memberRequest={mr}
          onApproveClick={handleApproveClick}
          onDeclineClick={handleDeclineClick}
        />
      ))}

      {memberRequests.length === 0 && (
        <div className="inline-flex h-[214px] w-full flex-col items-center justify-center gap-2.5 rounded-lg bg-black-500 p-4">
          <div className="font-['Graphik'] text-[14px] font-normal leading-loose text-black-100">
            You have 0 requests.
          </div>
        </div>
      )}
      <ConfirmModal
        onConfirm={handleConfirmProceed}
        open={openConfirm}
        onCancel={handleConfirmClose}
        title={'Please confirm'}
        loading={loading}
      >
        <>
          <div>{`Are you sure you want to ${action} the member request?`}</div>
        </>
      </ConfirmModal>
    </>
  )
}
