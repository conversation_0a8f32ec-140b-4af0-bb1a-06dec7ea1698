import Box from '@mui/material/Box'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import Link from 'next/link'
import React from 'react'

import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { checkAdminOrCreatorRole, getFullName } from '@memberup/shared/src/libs/profile'
import { IUser } from '@memberup/shared/src/types/interfaces'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import SVGVerified from '@/memberup/components/svgs/verified'
import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const MemberItem: React.FC<{
  member: IUser
  handleClick: () => void
}> = ({ member, handleClick }) => {
  const theme = useTheme()
  const isDarkTheme = useAppTheme()
  const membership = useAppSelector((state) => selectMembership(state))
  const isAdminOrCreator = checkAdminOrCreatorRole(member?.role)
  const fullName = getFullName(member.first_name, member.last_name, 'No Name', 12)

  return (
    <Link href={`/@${member.username}`}>
      <Box
        sx={{
          position: 'relative',
          color: theme.palette.text.disabled,
          width: 104,
        }}
        data-cy="member-item"
        data-member-name={fullName}
      >
        <AppProfileImage
          imageUrl={member.profile?.image || member.image}
          cropArea={member.profile?.image_crop_area || member.image_crop_area}
          name={fullName}
          r1={0.14}
          r2={0.5}
          size={104}
          fontSize={32}
        />
        {isAdminOrCreator && (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              position: 'absolute',
              left: 17,
              top: 88,
              backgroundColor: 'rgba(88,93,102,0.6)',
              backdropFilter: 'blur(10px)',
              borderRadius: '12px',
              pl: '7px',
              width: 70,
              height: 24,
            }}
          >
            <Typography
              sx={{
                color: '#FFFFFF',
                fontFamily: 'Graphik Semibold',
                fontSize: 10,
                letterSpacing: 0.5,
                lineHeight: 0.8,
                pt: '2px',
              }}
            >
              ADMIN&nbsp;
            </Typography>
            <SVGVerified width={20} height={20} />
          </Box>
        )}
        {(member.online_status || member.online) && (
          <Box
            sx={{
              position: 'absolute',
              right: -4,
              top: 80,
              backgroundColor: isDarkTheme ? '#AEE78B' : '#48B705',
              width: 20,
              height: 20,
              borderRadius: '50%',
              borderStyle: 'solid',
              borderColor: theme.palette.background.paper,
              borderWidth: '2px',
            }}
          />
        )}
        <Box className="text-center" sx={{ mt: '12px' }}>
          <Typography
            component="span"
            color="text.secondary"
            variant="subtitle1"
            noWrap
            gutterBottom
            sx={{ fontFamily: 'Graphik Semibold' }}
            data-cy="member-item"
          >
            {fullName}
          </Typography>
          {/* <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: 12,
          }}>
            <RoomOutlinedIcon fontSize="small" />
            <span>{member.location || 'No location'}</span>
          </Box> */}
        </Box>
      </Box>
    </Link>
  )
}

export default MemberItem
