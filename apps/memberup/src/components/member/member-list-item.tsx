import Grid from '@mui/material/Grid'
import { makeStyles } from '@mui/styles'
import React from 'react'

import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { TAppCropArea } from '@memberup/shared/src/types/types'

const useStyles = makeStyles((theme) => ({
  avatarWrapper: {
    paddingRight: 12,
    '&.noPadding': {
      paddingRight: 0,
    },
  },
}))

const MemberListItem: React.FC<{
  image?: string
  imageSize?: number
  cropArea?: TAppCropArea
  name: string
  r1?: number
  r2?: number
  primary?: React.ReactNode
  secondary?: React.ReactNode
  third?: React.ReactNode
  action?: React.ReactNode
  handleClick?: () => void
}> = ({ image, imageSize, cropArea, name, r1, r2, primary, secondary, third, action, handleClick }) => {
  return (
    <Grid
      container
      alignItems="center"
      flexWrap="nowrap"
      sx={{
        position: 'relative',
        cursor: 'pointer',
      }}
      onClick={() => handleClick?.()}
    >
      <Grid
        item
        sx={{
          pr: primary ? '12px' : 0,
        }}
      >
        <AppProfileImage imageUrl={image} cropArea={cropArea} name={name} r1={r1} r2={r2} size={imageSize} />
      </Grid>
      {Boolean(primary) && (
        <Grid item xs sx={{ overflow: 'hidden' }}>
          <Grid container spacing={1}>
            <Grid item xs={12}>
              {primary}
            </Grid>
            {Boolean(secondary) && (
              <Grid item xs={12}>
                {secondary}
              </Grid>
            )}
            {Boolean(third) && (
              <Grid item xs={12}>
                {third}
              </Grid>
            )}
          </Grid>
        </Grid>
      )}
      {Boolean(action) && <Grid item>{action}</Grid>}
    </Grid>
  )
}

export default MemberListItem
