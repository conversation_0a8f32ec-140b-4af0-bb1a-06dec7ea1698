import { GearIcon } from '@radix-ui/react-icons'
import { capitalize } from 'lodash'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'

import { getDateTimeFromNow } from '@memberup/shared/src/libs/date-utils'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { getCapitalized } from '@memberup/shared/src/libs/string-utils'
import { IUser } from '@memberup/shared/src/types/interfaces'
import { UserDetailsHoverCard } from '@/components/community/user-details-hover-card'
import { Calendar20Icon, Clock20Icon, Map20Icon } from '@/components/icons'
import { ProfilePicture } from '@/components/images/profile-picture'
import { AdminMemberSettings } from '@/components/settings/admin-member-settings/AdminMemberSettings'
import { Badge, Button } from '@/components/ui'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { useStore } from '@/hooks/useStore'
import { cn } from '@/lib/utils'
import useCheckUserRole from '@/src/components/hooks/use-check-user-role'
import SVGVerified from '@/src/components/svgs/verified'

const MemberCard: React.FC<{
  member: IUser
  onDeleteMemberClick: (memberId: string) => void
}> = ({ member, onDeleteMemberClick }) => {
  const { isCurrentUserAdmin } = useCheckUserRole()
  const router = useRouter()
  const user = useStore((state) => state.auth.user)

  const fullName = getFullName(member.first_name, member.last_name, 'No Name')
  const [openAdminMemberSettings, setOpenAdminMemberSettings] = useState(false)

  const handleOpenAdminMemberSettings = () => {
    setOpenAdminMemberSettings(true)
  }

  const formattedLastActivityAt = member.profile.last_activity_at
    ? getCapitalized(getDateTimeFromNow(member.profile.last_activity_at))
    : 'Never seen'

  const isStaff = ['admin', 'owner'].includes(member.role)

  return (
    <div
      className={cn(
        member.status === 'accepted' ? 'opacity-100' : 'opacity-35',
        'mb-5 w-full rounded-[10px] bg-black-500 p-5',
      )}
    >
      <AdminMemberSettings open={openAdminMemberSettings} onOpenChange={setOpenAdminMemberSettings} member={member} />
      <div className="flex gap-4">
        <ProfilePicture
          className="h-10 w-10"
          src={member.profile.image}
          cropArea={member.profile.image_crop_area}
          alt={fullName}
          width={40}
          height={40}
        />
        <UserDetailsHoverCard username={member.username}>
          <Link className="block grow" href={`/@${member.username}`}>
            <div className="text-white flex items-center gap-1 text-base font-semibold leading-normal">
              {fullName}
              {isStaff && <SVGVerified className="ml-0.5" width={16} height={16} />}
            </div>
            <div className="flex flex-row items-center gap-2">
              <div className="text-xs font-normal leading-none text-[#8d94a3]">@{member.username}</div>
              {!isStaff && (
                <div className="text-xs font-normal leading-none text-[#8d94a3]">
                  <Badge variant="secondary">{capitalize(member.status)}</Badge>
                </div>
              )}
            </div>
          </Link>
        </UserDetailsHoverCard>
        {user && user.id !== member.id && (
          <div className="flex items-center gap-2">
            <Link href={`/chat?member=${member.id}`}>
              <Button size="sm" type="submit" variant="outline">
                Chat
              </Button>
            </Link>
            {isCurrentUserAdmin && (
              <>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="sm" type="submit" variant="outline">
                      <GearIcon />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="">
                    <DropdownMenuItem
                      className="cursor-pointer text-red-500"
                      onClick={() => onDeleteMemberClick(member.id)}
                    >
                      Remove Member
                    </DropdownMenuItem>
                    <DropdownMenuItem className="cursor-pointer" onClick={() => handleOpenAdminMemberSettings()}>
                      Member Settings
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
        )}
      </div>

      <div className="text-white mt-5 font-['Graphik'] text-sm font-normal leading-snug">{member.profile.bio}</div>
      <div className="mt-5 space-y-3">
        {getDateTimeFromNow(member.profile.last_activity_at) && (
          <div className="flex items-center">
            <Clock20Icon className="mr-2 text-black-200 dark:text-black-100"></Clock20Icon>
            <div className="text-white text-ssm font-normal text-black-700 dark:text-white-500">
              Active {formattedLastActivityAt}
            </div>
          </div>
        )}
        <div className="flex items-center">
          <Calendar20Icon className="mr-2 text-black-200 dark:text-black-100" />
          <div className="text-white text-ssm font-normal text-black-700 dark:text-white-500">
            Joined {getDateTimeFromNow(member.createdAt)}
          </div>
        </div>
        {member.profile.location && (
          <div className="flex items-center">
            <Map20Icon className="mr-2 text-black-200 dark:text-black-100" />
            <div className="text-white text-ssm font-normal text-black-700 dark:text-white-500">
              {member.profile.location}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default MemberCard
