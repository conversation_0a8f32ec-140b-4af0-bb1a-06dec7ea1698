import { joiResolver } from '@hookform/resolvers/joi'
import CloseIcon from '@mui/icons-material/Close'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Divider from '@mui/material/Divider'
import FormControl from '@mui/material/FormControl'
import FormControlLabel from '@mui/material/FormControlLabel'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import useTheme from '@mui/material/styles/useTheme'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import Joi from 'joi'
import React, { useEffect, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'

import { AppDropzone } from '@memberup/shared/src/components/common/app-dropzone'
import { AppSwitch } from '@memberup/shared/src/components/common/input/switch'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { IMAGE_ACCEPT_ONLY } from '@memberup/shared/src/types/consts'
import { ILive } from '@memberup/shared/src/types/interfaces'
import SVGAddImage from '@/memberup/components/svgs/add-image'
import { selectRequestUpdateLive, updateLive } from '@/memberup/store/features/liveSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
    },
    '& .MuiDialogTitle-root .MuiTypography-h6': {
      fontSize: 16,
    },
    '& .MuiInputBase-root': {
      // color: theme.palette.text.secondary,
    },
    '& .MuiFormControlLabel-root': {
      marginRight: 0,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: 24,
  },
  dialogContent: {
    minHeight: 320,
    lineHeight: 1,
    padding: '0 24px 24px 24px',
    '& img': {
      borderRadius: 16,
    },
  },
  label: {
    marginBottom: 8,
  },
  appDropzoneWrapper: {
    borderColor: theme.palette.divider,
    borderRadius: 12,
    borderStyle: 'solid',
    borderWidth: 2,
    height: 230,
  },
}))

type FormDataType = {
  title: string
  description?: string
  allow_comment?: boolean
  notify_members?: boolean
}

const FormValue: FormDataType = {
  title: '',
  description: '',
  allow_comment: true,
  notify_members: true,
}

const FormSchema = Joi.object({
  title: Joi.string().required().messages({
    'string.empty': `Title cannot be an empty field`,
    'any.required': `Title is required.`,
  }),
}).options({ allowUnknown: true })

const EditStreamInfo: React.FC<{ live: ILive; open: boolean; onClose: () => void }> = ({ live, open, onClose }) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const theme = useTheme()
  const requestUpdateLiveRef = useRef(false)
  const dispatch = useAppDispatch()
  const requestUpdateLive = useAppSelector((state) => selectRequestUpdateLive(state))
  const hookFormMethods = useForm<FormDataType>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: FormValue,
    resolver: joiResolver(FormSchema),
  })
  const { control, register, reset, formState, getValues, setValue, trigger, handleSubmit } = hookFormMethods
  const [announcementPostImageFile, setAnnouncementPostImageFile] = useState<File>(null)

  useEffect(() => {
    if (mountedRef.current && live) {
      reset(
        {
          title: live.title || '',
          description: live.description || '',
          allow_comment: live.allow_comment || false,
          notify_members: live.allow_comment || false,
        },
        { keepIsSubmitted: false },
      )
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [live, reset])

  useEffect(() => {
    if (requestUpdateLiveRef.current === true && !requestUpdateLive) {
      requestUpdateLiveRef.current = requestUpdateLive
      onClose()
    }
    requestUpdateLiveRef.current = requestUpdateLive
  }, [onClose, requestUpdateLive])

  const handleDropFile = (f) => {
    setAnnouncementPostImageFile(f)
  }

  const handleFormSubmit = (data) => {
    const payload = {
      ...data,
    }
    if (announcementPostImageFile) {
      payload.announcementPostImageFile = announcementPostImageFile
    }
    dispatch(updateLive({ id: live.id, data: payload }))
  }

  return (
    <Dialog
      maxWidth="xs"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      aria-labelledby="edit-stream-info-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="edit-stream-info-dialog-title">
        Edit Stream Info
        <IconButton size="small" aria-label="close" className="close color02" onClick={onClose}>
          <CloseIcon fontSize="inherit" />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography className={classes.label} variant="subtitle1" color="inherit">
                About this Live Video
              </Typography>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className={clsx('form-control', 'light')} fullWidth>
                    <TextField
                      placeholder="Enter Title"
                      variant="outlined"
                      size="small"
                      error={Boolean(error)}
                      helperText={error?.message}
                      value={value}
                      onChange={(e) => {
                        onChange(e.target.value)
                      }}
                      onBlur={onBlur}
                    />
                  </FormControl>
                )}
                control={control}
                name="title"
              />
              <br />
              <br />
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className={clsx('form-control', 'light')} fullWidth>
                    <TextField
                      placeholder="Description"
                      variant="outlined"
                      size="small"
                      multiline={true}
                      minRows={4}
                      error={Boolean(error)}
                      helperText={error?.message}
                      value={value}
                      onChange={(e) => {
                        onChange(e.target.value)
                      }}
                      onBlur={onBlur}
                    />
                  </FormControl>
                )}
                control={control}
                name="description"
              />
            </Grid>
            <Grid item xs={12}>
              <Divider />
            </Grid>
            <Grid item xs={12}>
              <Grid container alignItems="center">
                <Grid item xs>
                  <Typography variant="body1" color="inherit">
                    Allow Commenting
                  </Typography>
                </Grid>
                <Grid item>
                  <Controller
                    render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                      <FormControlLabel
                        control={
                          <AppSwitch
                            checked={value}
                            onChange={(e) => {
                              onChange(e.target.checked)
                            }}
                            onBlur={onBlur}
                            name="allow_comment"
                          />
                        }
                        label=""
                      />
                    )}
                    control={control}
                    name="allow_comment"
                  />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <Divider />
            </Grid>
            <Grid item xs={12}>
              <Grid container alignItems="center">
                <Grid item xs>
                  <Typography variant="body1" color="inherit">
                    Notify members
                  </Typography>
                </Grid>
                <Grid item>
                  <Controller
                    render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                      <FormControlLabel
                        control={
                          <AppSwitch
                            checked={value}
                            onChange={(e) => {
                              onChange(e.target.checked)
                            }}
                            onBlur={onBlur}
                            name="notify_members"
                          />
                        }
                        label=""
                      />
                    )}
                    control={control}
                    name="notify_members"
                  />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <Typography className={classes.label} variant="subtitle1">
                Announcement Post Image
              </Typography>
              <div className={clsx(classes.appDropzoneWrapper, 'background-color06', 'color03')}>
                <AppDropzone
                  file={announcementPostImageFile}
                  accept={IMAGE_ACCEPT_ONLY}
                  onDropFile={handleDropFile}
                  placeholder={
                    <div className="text-center">
                      <SVGAddImage />
                      <br />
                      <br />
                      <Typography variant="subtitle1">Click or drag here to upload</Typography>
                      <br />
                      <Typography color="text.disabled" variant="body2">
                        When you schedule your live video, an
                        <br />
                        announcement will be posted immediately
                      </Typography>
                    </div>
                  }
                />
              </div>
            </Grid>
            <Grid item xs={12}>
              <Button
                className="round-small"
                sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
                variant="contained"
                color="primary"
                type="submit"
                fullWidth
                disabled={!formState.isValid}
              >
                {requestUpdateLive ? <CircularProgress size={16} color="inherit" /> : 'Save'}
              </Button>
            </Grid>
          </Grid>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default EditStreamInfo
