import { joiResolver } from '@hookform/resolvers/joi'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import CloseIcon from '@mui/icons-material/Close'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Divider from '@mui/material/Divider'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import { CSSObject } from '@mui/material/styles'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import Joi from 'joi'
import { useRouter } from 'next/router'
import React, { useEffect, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'

import { AppDropzone } from '@memberup/shared/src/components/common/app-dropzone'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { IMAGE_ACCEPT_ONLY } from '@memberup/shared/src/types/consts'
import { LIVE_STREAM_BRODCAST_ENUM } from '@memberup/shared/src/types/enum'
import DatePicker from '@/memberup/components/common/pickers/date-picker'
import TimePicker from '@/memberup/components/common/pickers/time-picker'
import SVGAddImage from '@/memberup/components/svgs/add-image'
import SVGCamcorder from '@/memberup/components/svgs/camcorder'
import { createLive, selectActiveLive, selectRequestCreateLive } from '@/memberup/store/features/liveSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
    },
    '& .MuiDialogTitle-root .MuiTypography-h6': {
      fontSize: 16,
    },
    '& .MuiInputBase-root': {
      // color: theme.palette.text.secondary,
    },
    '& .MuiFormControlLabel-root': {
      marginRight: 0,
    },
    '& .MuiOutlinedInput-root': {
      backgroundColor: (theme.components.MuiCssBaseline.styleOverrides as CSSObject).body['& .background-color06'][
        'backgroundColor'
      ],
      borderRadius: 12,
      borderWidth: 2,
      '& .MuiOutlinedInput-notchedOutline': {
        backgroundColor: (theme.components.MuiCssBaseline.styleOverrides as CSSObject).body['& .background-color06'][
          'backgroundColor'
        ],
        borderColor: theme.palette.divider,
      },
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: 24,
  },
  dialogContent: {
    minHeight: 320,
    lineHeight: 1,
    padding: '0 24px 24px 24px',
    '& img': {
      borderRadius: 16,
    },
  },
  broadCast: {
    borderRadius: 12,
    height: 48,
    padding: 8,
    cursor: 'pointer',
    '&.active': {
      color: theme.palette.primary.dark,
    },
  },
  iconWrapper: {
    backgroundColor: theme.palette.action.disabledBackground,
    borderRadius: 12,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 18,
    width: 32,
    height: 32,
  },
  label: {
    marginBottom: 8,
  },
  appDropzoneWrapper: {
    borderColor: theme.palette.divider,
    borderRadius: 12,
    borderStyle: 'solid',
    borderWidth: 2,
    height: 230,
  },
}))

const Boradcasts = [
  {
    key: LIVE_STREAM_BRODCAST_ENUM.now,
    text: 'Go Live Now',
  },
  {
    key: LIVE_STREAM_BRODCAST_ENUM.schedule,
    text: 'Schedule a Live Video',
  },
]

type FormDataType = {
  broadcast: string
  scheduled_date?: Date
  scheduled_time?: Date
  title: string
  description?: string
  announcement_post_image?: string
}

const FormValue: FormDataType = {
  broadcast: 'schedule',
  scheduled_date: new Date(),
  scheduled_time: new Date(),
  title: '',
  description: '',
  announcement_post_image: '',
}

const FormSchema = Joi.object({
  title: Joi.string().required().messages({
    'string.empty': `Title cannot be an empty field`,
    'any.required': `Title is required.`,
  }),
}).options({ allowUnknown: true })

const EditStream: React.FC<{ open: boolean; onClose: () => void }> = ({ open, onClose }) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const requestCreateLiveRef = useRef(false)
  const router = useRouter()
  const dispatch = useAppDispatch()
  const activeLive = useAppSelector((state) => selectActiveLive(state))
  const requestCreateLive = useAppSelector((state) => selectRequestCreateLive(state))

  const hookFormMethods = useForm<FormDataType>({
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    defaultValues: FormValue,
    resolver: joiResolver(FormSchema),
  })
  const { control, register, reset, formState, getValues, setValue, trigger, handleSubmit, watch } = hookFormMethods
  const [announcementPostImageFile, setAnnouncementPostImageFile] = useState<File>(null)

  useEffect(() => {
    if (requestCreateLiveRef.current === true && !requestCreateLive) {
      onClose()
      const scheduled_date = getValues('scheduled_date')
      if (scheduled_date) {
        router.back()
      }
    }
    requestCreateLiveRef.current = requestCreateLive
  }, [getValues, onClose, requestCreateLive, router])

  const handleDropFile = (f) => {
    setAnnouncementPostImageFile(f)
  }

  const handleFormSubmit = async (data) => {
    if (!activeLive) return
    const { scheduled_date, scheduled_time, ...rest } = data
    const tempDate = scheduled_date?.getTime() || 0
    const tempTime = scheduled_time?.getTime() || 0
    const payload = {
      ...rest,
      scheduled_timestamp: Math.max(Math.floor(tempDate / 1000), Math.floor(tempTime / 1000)),
      stream_id: activeLive?.stream_id,
      playback: activeLive?.playback,
      thumbnail: activeLive?.thumbnail,
    }

    if (announcementPostImageFile) {
      payload.announcementPostImageFile = announcementPostImageFile
    }
    dispatch(createLive(payload))
  }

  const boradcast = watch('broadcast')

  return (
    <Dialog
      maxWidth="xs"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      aria-labelledby="edit-stream-info-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="edit-stream-info-dialog-title">
        About this stream
        <IconButton size="small" aria-label="close" className="close large color02" onClick={onClose}>
          <CloseIcon fontSize="inherit" />
        </IconButton>
      </DialogTitle>
      <DialogContent className={clsx(classes.dialogContent, 'color03')}>
        <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography className={classes.label} variant="subtitle1">
                Broadcast Setup
              </Typography>
              <input type="hidden" {...register('broadcast')} />
              <Grid container spacing={1}>
                {Boradcasts.map((brodcast, index) => (
                  <Grid item xs={12} key={`broadcast-${brodcast.key}`}>
                    <Grid
                      className={clsx(classes.broadCast, {
                        active: boradcast === brodcast.key,
                        'background-color05': boradcast === brodcast.key,
                        'background-color07': boradcast !== brodcast.key,
                      })}
                      container
                      alignItems="center"
                      onClick={() => setValue('broadcast', brodcast.key, { shouldValidate: true })}
                    >
                      <Grid item>
                        <div className={classes.iconWrapper}>
                          <SVGCamcorder />
                        </div>
                      </Grid>
                      <Grid item xs>
                        <Typography variant="body1" color="inherit">
                          {brodcast.text}
                        </Typography>
                      </Grid>
                      {boradcast === brodcast.key && (
                        <Grid item>
                          <CheckCircleIcon />
                        </Grid>
                      )}
                    </Grid>
                  </Grid>
                ))}
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <Divider />
            </Grid>
            <Grid item xs={12}>
              <Typography className={classes.label} variant="subtitle1">
                Announcement Post
              </Typography>
              <Typography variant="body2" color="inherit">
                When you schedule your live video, an announcement will be posted immediately
              </Typography>
            </Grid>
            {boradcast === 'schedule' && (
              <Grid item xs={12}>
                <Typography className={classes.label} variant="subtitle1">
                  Date and Time of Live Video
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Controller
                      render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                        <FormControl error={Boolean(error)} className={clsx('form-control', 'light')} fullWidth>
                          <DatePicker
                            label="Date"
                            value={value}
                            handleChange={(e) => {
                              onChange(e)
                            }}
                          />
                        </FormControl>
                      )}
                      control={control}
                      name="scheduled_date"
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <Controller
                      render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                        <FormControl error={Boolean(error)} className={clsx('form-control', 'light')} fullWidth>
                          <TimePicker
                            label="Time"
                            value={value}
                            handleChange={(e) => {
                              onChange(e)
                            }}
                          />
                        </FormControl>
                      )}
                      control={control}
                      name="scheduled_time"
                    />
                  </Grid>
                </Grid>
              </Grid>
            )}
            <Grid item xs={12}>
              <Typography className={classes.label} variant="subtitle1">
                Title of stream
              </Typography>
              <Grid container spacing={1}>
                <Grid item xs={12}>
                  <Controller
                    render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                      <FormControl error={Boolean(error)} className={clsx('form-control', 'light')} fullWidth>
                        <TextField
                          placeholder="Title (optional)"
                          variant="outlined"
                          size="small"
                          error={Boolean(error)}
                          helperText={error?.message}
                          value={value}
                          onChange={(e) => {
                            onChange(e.target.value)
                          }}
                          onBlur={onBlur}
                        />
                      </FormControl>
                    )}
                    control={control}
                    name="title"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                      <FormControl error={Boolean(error)} className={clsx('form-control', 'light')} fullWidth>
                        <TextField
                          placeholder="Description"
                          variant="outlined"
                          size="small"
                          multiline={true}
                          minRows={4}
                          error={Boolean(error)}
                          helperText={error?.message}
                          value={value}
                          onChange={(e) => {
                            onChange(e.target.value)
                          }}
                          onBlur={onBlur}
                        />
                      </FormControl>
                    )}
                    control={control}
                    name="description"
                  />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <Typography className={classes.label} variant="subtitle1">
                Announcement Post Image
              </Typography>
              <div className={clsx(classes.appDropzoneWrapper, 'background-color06', 'color03')}>
                <AppDropzone
                  file={announcementPostImageFile}
                  accept={IMAGE_ACCEPT_ONLY}
                  onDropFile={handleDropFile}
                  placeholder={
                    <div className="text-center">
                      <SVGAddImage />
                      <br />
                      <br />
                      <Typography variant="subtitle1">Click or drag here to upload</Typography>
                      <br />
                      <Typography color="text.disabled" variant="body2">
                        When you schedule your live video, an
                        <br />
                        announcement will be posted immediately
                      </Typography>
                    </div>
                  }
                />
              </div>
            </Grid>
            <Grid item xs={12}>
              <Button
                className="round-small"
                variant="contained"
                color="primary"
                type="submit"
                fullWidth
                disabled={!formState.isValid || !activeLive}
              >
                {requestCreateLive ? <CircularProgress size={16} color="inherit" /> : 'Go Live'}
              </Button>
            </Grid>
          </Grid>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default EditStream
