import { joiResolver } from '@hookform/resolvers/joi'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import Slide, { SlideProps } from '@mui/material/Slide'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import Joi from 'joi'
import React from 'react'
import { Controller, useForm } from 'react-hook-form'

import SVGClose from '@/memberup/components/svgs/close'
import { useAppDispatch } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    width: '60%',
    maxWidth: '100%',
    minWidth: 380,
    marginLeft: 'auto',
    '& .MuiDialog-container': {
      alignItems: 'unset',
    },
    '& .MuiDialog-paper': {
      backgroundColor: theme.palette.background.paper,
    },
    '& .MuiDialog-paperFullWidth': {
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      maxWidth: '100%',
      margin: 0,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    paddingTop: 38,
  },
  dialogContent: {
    minHeight: 320,
    lineHeight: 1,
    padding: 16,
    '&.MuiDialogContent-root': {
      paddingTop: 16,
    },
    '& img': {
      borderRadius: 16,
    },
  },
  content: {
    maxWidth: 682,
    margin: 'auto',
    padding: 16,
  },
  photoWrapper: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    margin: 'auto',
    width: 200,
    height: 200,
    overflow: 'hidden',
    backgroundColor: '#EBE2DD',
    borderRadius: '42%',
  },
  profilePicture: {
    width: 200,
    height: 200,
    objectFit: 'cover',
  },
  personIcon: {
    fontSize: 96,
    opacity: 0.2,
  },
  activityCardWrapper: {
    width: '100%',
    marginBottom: 16,
    lineHeight: 1,
  },
  noPost: {
    borderRadius: 8,
    padding: 12,
  },
}))

type FormDataType = {
  title: string
  description: string
  thumbnail: string
  thumbnail_mask: string
  date_range: number[]
  time: number
  visibility: string
}

const FormValue: FormDataType = {
  title: '',
  description: '',
  thumbnail: '',
  thumbnail_mask: '',
  date_range: [],
  time: null,
  visibility: '',
}

const FormSchema = Joi.object({
  bio: Joi.string(),
}).options({ allowUnknown: true })

// eslint-disable-next-line react/display-name
const Transition = React.forwardRef<unknown, SlideProps>((props, ref) => (
  <Slide direction="left" {...props} ref={ref} />
))

const AddNewGiveaway: React.FC<{
  open: boolean
  onClose: () => void
}> = ({ open, onClose }) => {
  const classes = useStyles()
  const dispatch = useAppDispatch()
  const { control, formState, getValues, trigger, handleSubmit } = useForm<FormDataType>({
    mode: 'onBlur',
    reValidateMode: 'onChange',
    defaultValues: FormValue,
    resolver: joiResolver(FormSchema),
  })

  const handleFormSubmit = (data) => {}

  return (
    <Dialog
      maxWidth="lg"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      TransitionComponent={Transition}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 500,
          enter: 500,
          exit: 300,
        },
      }}
      aria-labelledby="profile-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="profile-dialog-title">
        Add New Giveaway
        <IconButton size="medium" aria-label="close" className="close large color02" onClick={onClose}>
          <SVGClose fontSize={16} />
        </IconButton>
      </DialogTitle>
      <DialogContent id="profile-dialog-content" className={classes.dialogContent}>
        <div className={classes.content}>
          <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
            <Grid container spacing={4}>
              <Grid item xs={12} sm={6}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="body1" color="inherit" gutterBottom>
                      Giveaway Title (required)
                    </Typography>
                    <Controller
                      render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                        <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                          <TextField
                            placeholder="Enter Title"
                            variant="outlined"
                            error={Boolean(error)}
                            helperText={error?.message}
                            value={value}
                            onChange={(e) => {
                              onChange(e.target.value)
                            }}
                            onBlur={onBlur}
                          />
                        </FormControl>
                      )}
                      control={control}
                      name="title"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="body1" color="inherit" gutterBottom>
                      Description
                    </Typography>
                    <Controller
                      render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                        <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                          <TextField
                            placeholder="Description"
                            variant="outlined"
                            error={Boolean(error)}
                            helperText={error?.message}
                            value={value}
                            onChange={(e) => {
                              onChange(e.target.value)
                            }}
                            onBlur={onBlur}
                          />
                        </FormControl>
                      )}
                      control={control}
                      name="description"
                    />
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12} sm={6}></Grid>
            </Grid>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default AddNewGiveaway
