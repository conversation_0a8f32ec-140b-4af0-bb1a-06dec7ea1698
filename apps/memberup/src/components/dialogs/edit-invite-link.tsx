import SVGClose from '@/memberup/components/svgs/close'
import { joiResolver } from '@hookform/resolvers/joi'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import {
  createInviteLinkApi,
  updateInviteLinkApi,
} from '@memberup/shared/src/services/apis/invite-link.api'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { IInviteLink } from '@memberup/shared/src/types/interfaces'
import useTheme from '@mui/material/styles/useTheme'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import Joi from 'joi'
import React, { useEffect, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: '24px 32px',
    paddingBottom: 0,
  },
}))

type FormDataType = {
  name: string
}

const FormValue: FormDataType = {
  name: '',
}

const FormSchema = Joi.object({
  name: Joi.string().required().messages({
    'string.empty': `Name cannot be an empty field`,
    'any.required': 'Name is required.',
  }),
}).options({ allowUnknown: true })

const EditInviteLink: React.FC<{
  data?: IInviteLink
  open: boolean
  onClose: (e?: IInviteLink) => void
}> = ({ data, open, onClose }) => {
  const classes = useStyles()
  const theme = useTheme()
  const mountedRef = useMounted(true)
  const [requestUpsert, setRequestUpsert] = useState(false)

  const { control, reset, formState, setValue, setError, handleSubmit } = useForm<FormDataType>({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: FormValue,
    resolver: joiResolver(FormSchema),
  })

  useEffect(() => {
    if (!mountedRef.current || !open) return
    reset(
      {
        name: data?.name || '',
      },
      { keepIsSubmitted: false }
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, open])

  const handleFormSubmit = async (formData) => {
    try {
      setRequestUpsert(true)
      const payload: any = {
        name: formData.name || '',
      }

      const api = data?.id ? updateInviteLinkApi(data.id, payload) : createInviteLinkApi(payload)
      api
        .then((res) => {
          onClose(res.data.data)
        })
        .catch((err) => {
          if (err.response?.data?.message) {
            setError('name', { message: err.response.data.message })
          }
        })
        .finally(() => {
          if (!mountedRef.current) return
          setRequestUpsert(false)
        })
    } catch (err) {
      setRequestUpsert(false)
    }
  }

  return (
    <Dialog
      maxWidth="xs"
      className={classes.root}
      open={open}
      onClose={() => onClose()}
      aria-labelledby="edit-invite-link-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="edit-invite-link-dialog-title">
        <IconButton
          size="small"
          aria-label="close"
          className="close color03"
          onClick={() => onClose()}
        >
          <SVGClose fontSize={16} />
        </IconButton>
        {data?.name ? 'Edit Invite Link' : 'Create Invite Link'}
      </DialogTitle>
      <DialogContent
        id="edit-invite-link-dialog-content"
        sx={{
          minHeight: 180,
          padding: '32px',
          paddingTop: '42px!important',
          lineHeight: 1,
          '& .MuiOutlinedInput-root': {
            '& .MuiOutlinedInput-notchedOutline': {
              border: 'none',
            },
          },
        }}
      >
        <form className="form" autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Typography className="font-family-graphik-medium" variant="body1" gutterBottom>
                Invite Link Name
              </Typography>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <TextField
                    placeholder="Enter Invite link name"
                    size="small"
                    variant="outlined"
                    value={value}
                    disabled={requestUpsert}
                    error={Boolean(error)}
                    helperText={error?.message}
                    fullWidth
                    InputProps={{
                      classes: { root: 'background-color06' },
                      sx: {
                        borderRadius: 2,
                      },
                    }}
                    onChange={(e) => {
                      onChange(e.target.value)
                    }}
                    onBlur={onBlur}
                  />
                )}
                control={control}
                name="name"
              />
              <br />
              <br />
              <br />
            </Grid>
            <Grid item xs={6}>
              <Button
                className="app-button round-small"
                variant="outlined"
                disabled={requestUpsert}
                fullWidth
                onClick={() => onClose()}
              >
                Cancel
              </Button>
            </Grid>
            <Grid item xs={6}>
              <Button
                className="app-button round-small"
                sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
                type="submit"
                variant="contained"
                fullWidth
                disabled={requestUpsert || !formState.isDirty}
              >
                {requestUpsert ? <CircularProgress size={16} /> : 'Save'}
              </Button>
            </Grid>
          </Grid>
        </form>
      </DialogContent>
    </Dialog>
  )
}

EditInviteLink.displayName = 'EditInviteLink'

export default EditInviteLink
