import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import LockOutlinedIcon from '@mui/icons-material/LockOutlined'
import PersonIcon from '@mui/icons-material/Person'
import Accordion from '@mui/material/Accordion'
import AccordionDetails from '@mui/material/AccordionDetails'
import AccordionSummary from '@mui/material/AccordionSummary'
import Box from '@mui/material/Box'
import Divider from '@mui/material/Divider'
import Radio from '@mui/material/Radio'
import Stack from '@mui/material/Stack'
import useTheme from '@mui/material/styles/useTheme'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import React, { useEffect, useState } from 'react'

import SVGLock from '../../svgs/lock'
import SVGPerson from '../../svgs/person'
import SVGSmallLock from '../../svgs/small-lock'
import {
  selectMembershipSetting,
  selectRequestUpdateMembershipSetting,
  updateMembershipSetting,
} from '@/memberup/store/features/membershipSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { useMounted } from '@/shared-components/hooks/use-mounted'
import { VISIBILITY_ENUM } from '@/shared-types/enum'

const defaultIntroHtml =
  '<p>Please use this space to let people know how they can request access to your community.</p>'
const defaultIntroClosedHtml =
  '<p>Please use this space to let people know how they can request access to your community.</p>'
const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      backgroundColor: 'rgba(58,59,61,0.92)',
      height: '100%',
      width: '100%',
      maxHeight: '100%',
      maxWidth: '100%',
      margin: 0,
    },
  },
  dialogContent: {
    display: 'flex',
    alignItems: 'center',
    position: 'relative',
    maxWidth: 1440,
    margin: 'auto',
  },
}))

const CommunityAccessEditor: React.FC<{
  visibility: string
  introHtml: string
  setValue
}> = ({ setValue, introHtml, visibility }) => {
  const theme = useTheme()
  const [expandedIntro, setExpandedIntro] = useState(false)
  const [remainingChars, setRemainingChars] = useState(150 - introHtml.replace(/<[^>]*>?/gm, '').length)
  const isPrivateMembership = visibility === VISIBILITY_ENUM.private

  const handleChangeVisibility = (newVisibility: string) => {
    setValue('visibility', newVisibility, { shouldDirty: true })
  }
  const tabs = [
    {
      name: 'Public (default)',
      icon: <SVGPerson width={16} height={16} styles={{ color: theme.palette.mode === 'dark' ? '#8d94a2' : '#000' }} />,
      value: VISIBILITY_ENUM.public,
      description: 'Anybody can sign up to your community',
    },
    {
      name: 'Private',
      icon: <SVGSmallLock styles={{ color: theme.palette.mode === 'dark' ? '#8d94a2' : '#000' }} />,
      value: VISIBILITY_ENUM.private,
      description: 'Only those you invite can view or join your community',
    },
  ]

  return (
    <>
      <Box
        sx={{
          backgroundColor: theme.palette.mode === 'dark' ? theme.palette.background.paper : 'transparent',
          borderRadius: 3,
          width: '100%',
          marginTop: '15px !important',
          maxWidth: 648,
        }}
      >
        <Stack spacing={4}>
          <Stack direction="column" spacing={2}>
            {tabs.map((tab, index) => (
              <Box
                key={tab.value}
                sx={{
                  borderRadius: '12px',
                  borderColor: visibility === tab.value ? theme.palette.text.primary : theme.palette.divider,
                  borderStyle: 'solid',
                  borderWidth: '2px',
                  cursor: 'pointer',
                  flex: 1,
                  padding: '14px 16px',
                  backgroundColor: theme.palette.mode === 'dark' ? '#212124' : '#eceeef',
                }}
                onClick={() => handleChangeVisibility(tab.value)}
              >
                <Stack alignItems="center" direction="row" spacing={2}>
                  <Radio
                    checked={visibility === tab.value}
                    onChange={() => handleChangeVisibility(tab.value)}
                    sx={{
                      color:
                        visibility === tab.value
                          ? theme.palette.mode === 'dark'
                            ? '#fff !important'
                            : '#000 !important'
                          : '',
                      '&:hover': {
                        backgroundColor: 'transparent',
                      },
                    }}
                  />
                  <Box
                    sx={{
                      //backgroundColor: theme.palette.divider,
                      borderRadius: 20,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: 28,
                      height: 28,
                    }}
                  >
                    {tab.icon}
                  </Box>
                  <Stack>
                    <Typography
                      className="bold"
                      variant="subtitle1"
                      dangerouslySetInnerHTML={{ __html: tab.name }}
                    ></Typography>
                    <Typography
                      sx={{
                        opacity: 1,
                        color: 'rgba(105,111,122,1)',
                        fontFamily: 'Graphik Regular',
                        fontSize: '13px',
                        fontWeight: 400,
                        fontStyle: 'normal',
                        letterSpacing: '0px',
                        textAlign: 'left',
                        lineHeight: '20px',
                      }}
                      dangerouslySetInnerHTML={{ __html: tab.description }}
                    ></Typography>
                  </Stack>
                </Stack>
              </Box>
            ))}
          </Stack>
          <Box>
            {isPrivateMembership ? (
              <Accordion
                expanded={expandedIntro}
                sx={{
                  borderRadius: '15px !important',
                  borderColor: theme.palette.divider,
                  borderStyle: 'solid',
                  borderWidth: 1,
                  padding: 3,
                  marginTop: '8px',
                }}
                style={{ background: 'transparent' }}
                onChange={() => setExpandedIntro(!expandedIntro)}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{
                    alignItems: 'flex-start',
                    '& .MuiAccordionSummary-content': { flexDirection: 'column' },
                  }}
                >
                  <Typography variant="subtitle1" gutterBottom>
                    Edit Sign Up Page Text
                  </Typography>
                  <Typography color="text.disabled" variant="body2">
                    This is the text that people will see if they navigate to your login URL and click sign up.
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <br />
                  <Divider />
                  <br />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="subtitle1">Preview</Typography>
                    <Typography variant="body2" color="text.disabled">
                      {remainingChars}
                    </Typography>
                  </Box>
                  <Box
                    className="background-color19"
                    sx={{
                      borderRadius: 3,
                      backgroundColor: theme.palette.divider,
                      marginTop: 1,
                      height: 192,
                      fontFamily: 'Graphik Regular !important',
                      fontSize: '14px !important',
                      padding: '0px 0px 0px 0px',
                      '& .ql-editor.ql-blank::before': {
                        fontFamily: 'Graphik Regular',
                        fontSize: 14,
                        padding: '0px',
                        left: '0px !important',
                      },
                    }}
                  >
                    <TextField
                      sx={{
                        height: 'auto',
                        backgroundColor: 'transparent',
                        '& .MuiInputBase-root': {
                          backgroundColor: 'transparent',
                        },
                        '& .MuiOutlinedInput-root': {
                          backgroundColor: 'transparent',
                        },
                      }}
                      fullWidth
                      multiline
                      variant="outlined"
                      value={introHtml.replace(/<[^>]*>?/gm, '')}
                      placeholder="Type your instructions..."
                      onChange={(event) => {
                        setValue('intro_html', event.target.value, { shouldDirty: true })
                        setRemainingChars(150 - event.target.value.length)
                      }}
                      inputProps={{
                        maxLength: 150,
                        style: { height: '160px' },
                      }}
                    />
                  </Box>
                </AccordionDetails>
              </Accordion>
            ) : null}
          </Box>
        </Stack>
      </Box>
    </>
  )
}

CommunityAccessEditor.displayName = 'CommunityAccess'

export default CommunityAccessEditor
