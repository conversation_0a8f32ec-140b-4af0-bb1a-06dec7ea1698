import { joiResolver } from '@hookform/resolvers/joi'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import FormControl from '@mui/material/FormControl'
import FormHelperText from '@mui/material/FormHelperText'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import useTheme from '@mui/material/styles/useTheme'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import clsx from 'clsx'
import Joi from 'joi'
import { jwtDecode } from 'jwt-decode'
import { useSession } from 'next-auth/react'
import React, { useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { resetEmailApi, resetEmailSendCodeApi } from '@memberup/shared/src/services/apis/reset-email.api'
import useAppCookie from '@/memberup/components/hooks/use-app-cookie'
import SVGClose from '@/memberup/components/svgs/close'
import { resetUser, selectUser } from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { showToast } from '@/shared-libs/toast'

type SendEmailChangingCodeFormDataType = {
  default_email: string
  new_email: string
  current_password: string
}

const SendEmailChangingCodeForm: React.FC<{
  onContinue: (e) => void
}> = ({ onContinue }) => {
  const mountedRef = useMounted(true)
  const theme = useTheme()
  const user = useAppSelector((state) => selectUser(state))
  const [requestUpdateEmail, setRequestUpdateEmail] = useState(false)
  const [error, setError] = useState('')

  const SendEmailChangingCodeFormSchema = Joi.object({
    default_email: Joi.string().required().messages({
      'string.empty': 'Please enter your current email address',
      'any.required': 'Current email is required.',
    }),
    new_email: Joi.string()
      .required()
      .email({ tlds: { allow: false } })
      .custom((value, helpers) => {
        // Convert both emails to lowercase for a case-insensitive comparison
        if (value.toLowerCase() === user?.email?.toLowerCase()) {
          return helpers.error('any.invalid')
        }
        return value // Return the value if validation passes
      })
      .messages({
        'string.email': 'Email must be a valid email address.',
        'string.empty': 'Please enter a valid email address',
        'any.required': 'Email is required.',
        'any.invalid': 'Email must be different.',
      }),
    current_password: Joi.string().required().min(8).messages({
      'string.empty': 'Current Password cannot be an empty field',
      'string.min': 'Current Password should have a minimum length of 8',
      'any.required': 'Current Password is required.',
    }),
  }).options({ allowUnknown: true })

  const { control, formState, handleSubmit, register } = useForm<SendEmailChangingCodeFormDataType>({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      default_email: user?.email || '',
      new_email: '',
      current_password: '',
    },
    resolver: joiResolver(SendEmailChangingCodeFormSchema),
  })

  const handleFormSubmit = (formData) => {
    setRequestUpdateEmail(true)
    resetEmailSendCodeApi(formData.new_email, formData.current_password)
      .then((res) => {
        if (res.data.success) {
          onContinue(true)
        }
      })
      .catch((err) => {
        setError(err.response.data.message || `Couldn't send the code. Try again.`)
      })
      .finally(() => {
        if (mountedRef.current) {
          setRequestUpdateEmail(false)
        }
      })
  }

  return (
    <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)} data-cy="send-email-changing-code-form">
      <input type="hidden" {...register('default_email')} />
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography color="text.disabled">
            Change your email below. We will send you an email to verify your new email.
          </Typography>
          <br />
        </Grid>
        <Grid item xs={12}>
          <Typography variant="subtitle1" gutterBottom>
            Password
          </Typography>
          <Controller
            render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
              <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                <TextField
                  placeholder="Enter current password"
                  variant="outlined"
                  disabled={requestUpdateEmail}
                  error={Boolean(error)}
                  helperText={error?.message}
                  size="small"
                  type="password"
                  value={value}
                  inputProps={{
                    autoComplete: 'current_password',
                    form: {
                      autoComplete: 'off',
                    },
                  }}
                  onChange={(e) => {
                    setError('')
                    onChange(e)
                  }}
                  onBlur={onBlur}
                  data-cy="current-password"
                />
              </FormControl>
            )}
            control={control}
            name="current_password"
          />
        </Grid>
        <Grid item xs={12}>
          <Typography variant="subtitle1" gutterBottom>
            Email
          </Typography>
          <Controller
            render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
              <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                <TextField
                  placeholder="Enter your email"
                  variant="outlined"
                  disabled={requestUpdateEmail}
                  error={Boolean(error)}
                  helperText={error?.message}
                  size="small"
                  value={value}
                  onChange={(e) => {
                    setError('')
                    onChange(e.target.value.toLowerCase())
                  }}
                  onBlur={onBlur}
                  data-cy="new-email"
                />
              </FormControl>
            )}
            control={control}
            name="new_email"
          />
          {Boolean(formState.errors?.default_email?.message) && (
            <FormHelperText error sx={{ ml: '14px', mr: '14px' }}>
              {formState.errors.default_email.message}
            </FormHelperText>
          )}
        </Grid>
        {Boolean(error) && (
          <Grid item xs={12}>
            <FormHelperText error>{error}</FormHelperText>
          </Grid>
        )}
        <Grid className="text-right" item xs={12}>
          <Button
            className="app-button round-small"
            sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
            variant="contained"
            color="primary"
            disabled={requestUpdateEmail}
            size="small"
            type="submit"
            data-cy="send-email-changing-code-submit-button"
          >
            {requestUpdateEmail ? <CircularProgress size={12} /> : 'CONTINUE'}
          </Button>
        </Grid>
      </Grid>
    </form>
  )
}

type VerifyEmailChangingCodeFormDataType = {
  code: string
}

const VerifyEmailChangingCodeFormSchema = Joi.object({
  code: Joi.string().required().min(6).max(6).messages({
    'string.empty': 'The verification code cannot be an empty field.',
    'string.min': 'The verification code should have a length of 6.',
    'string.max': 'The verification code should have a length of 6.',
    'any.required': 'The verification code is required.',
  }),
}).options({ allowUnknown: true })

const VerifyEmailChangingCodeForm: React.FC<{
  onContinue: (e) => void
}> = ({ onContinue }) => {
  const mountedRef = useMounted(true)
  const dispatch = useAppDispatch()
  const { update: updateSession } = useSession()
  const { getAppCookie, updateAppCookieAuthToken } = useAppCookie()
  const [requestUpdateEmail, setRequestUpdateEmail] = useState(false)
  const [error, setError] = useState('')
  const { control, formState, handleSubmit, register } = useForm<VerifyEmailChangingCodeFormDataType>({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      code: '',
    },
    resolver: joiResolver(VerifyEmailChangingCodeFormSchema),
  })

  const handleFormSubmit = (formData) => {
    setRequestUpdateEmail(true)
    resetEmailApi(formData.code)
      .then((res) => {
        if (res.data.success) {
          showToast('Email updated', 'success')
          const authToken = getAppCookie()
          const decodedToken = authToken
            ? jwtDecode<{ email: string; password: string; redirectTo: string }>(authToken)
            : null
          if (decodedToken?.password) {
            updateAppCookieAuthToken(res.data.data.email, decodedToken.password)
          }
          updateSession({
            email: res.data.data.email,
          })
          dispatch(
            resetUser({
              user: res.data.data,
            }),
          )
          onContinue(true)
        }
      })
      .catch((err) => {
        setError(err.response.data.message || `Couldn't verify the code. Try again.`)
      })
      .finally(() => {
        if (mountedRef.current) {
          setRequestUpdateEmail(false)
        }
      })
  }

  return (
    <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography color="text.disabled">Enter it below to verify your new email.</Typography>
          <br />
        </Grid>
        <Grid item xs={12}>
          <Controller
            render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
              <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                <TextField
                  placeholder="Verification code"
                  variant="outlined"
                  disabled={requestUpdateEmail}
                  error={Boolean(error)}
                  helperText={error?.message}
                  size="small"
                  value={value}
                  onChange={(e) => {
                    setError('')
                    onChange(e)
                  }}
                  onBlur={onBlur}
                />
              </FormControl>
            )}
            control={control}
            name="code"
          />
          {Boolean(error) && (
            <FormHelperText error sx={{ ml: '14px', mr: '14px', mt: '4px' }}>
              {error}
            </FormHelperText>
          )}
        </Grid>
        <Grid className="text-right" item xs={12}>
          <Button
            className="app-button round-small"
            variant="contained"
            color="primary"
            disabled={requestUpdateEmail}
            size="small"
            type="submit"
          >
            {requestUpdateEmail ? <CircularProgress size={12} /> : 'VERIFY'}
          </Button>
        </Grid>
      </Grid>
    </form>
  )
}

const ResetEmail: React.FC<{
  open: boolean
  onClose: (e) => void
}> = ({ open, onClose }) => {
  const [step, setStep] = useState(0)

  return (
    <Dialog
      maxWidth="xs"
      fullWidth={true}
      open={open}
      onClose={() => onClose(null)}
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: '12px',
        },
      }}
      aria-labelledby="change-email-dialog-title"
    >
      <DialogTitle
        id="change-email-dialog-title"
        sx={{
          borderBottom: 'none',
          p: '24px',
        }}
      >
        {step === 0 ? 'Change Email' : 'We sent you a code'}
        <IconButton size="small" aria-label="close" className="close color03" onClick={() => onClose(null)}>
          <SVGClose fontSize={14} />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ p: '24px' }}>
        {step === 0 ? (
          <SendEmailChangingCodeForm
            onContinue={(e) => {
              if (e) {
                setStep(1)
              } else {
                onClose(null)
              }
            }}
          />
        ) : (
          <VerifyEmailChangingCodeForm
            onContinue={(e) => {
              onClose(e || null)
            }}
          />
        )}
      </DialogContent>
    </Dialog>
  )
}

export default React.memo(ResetEmail)
