import { joiResolver } from '@hookform/resolvers/joi'
import Autocomplete from '@mui/material/Autocomplete'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import useTheme from '@mui/material/styles/useTheme'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import Joi from 'joi'
import React, { useEffect, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import PhoneInput from 'react-phone-number-input'

import { AppPhoneNumberInput } from '@memberup/shared/src/components/common/input/phone-number-input'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { showToast } from '@memberup/shared/src/libs/toast'
import {
  getStripeCurrentPaymentMethodApi,
  updateStripePaymentMethodApi,
} from '@memberup/shared/src/services/apis/stripe.api'
import { COUNTRIES } from '@memberup/shared/src/types/consts'
import SVGClose from '@/memberup/components/svgs/close'
import { selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'

import 'react-phone-number-input/style.css'

import { toast } from 'react-toastify'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
    },
    '& .MuiDialogTitle-root .MuiTypography-h6': {
      fontSize: 16,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: 24,
  },
  dialogContent: {
    paddingLeft: 24,
    paddingRight: 24,
    paddingTop: 24,
    paddingBottom: 24,
  },
}))

type FormDataType = {
  city?: string
  country?: string
  line1?: string
  line2?: string
  postal_code?: string
  state?: string
  email?: string
  name?: string
  phone?: string
  exp_month?: number
  exp_year?: number
}

const FormValue: FormDataType = {
  email: '',
  name: '',
}

const FormSchema = Joi.object({
  email: Joi.string()
    .optional()
    .allow('')
    .email({ tlds: { allow: false } }),
  name: Joi.string().optional().messages({
    'string.empty': `Name cannot be an empty field`,
  }),
}).options({ allowUnknown: true })

const EditBillingInfo: React.FC<{
  open: boolean
  isMembership: boolean
  onClose: () => void
}> = ({ open, isMembership, onClose }) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const theme = useTheme()
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const userProfile = useAppSelector((state) => selectUserProfile(state))
  const [requestUpdatePaymentMethod, setRequestUpdatePaymentMethod] = useState(false)
  const { control, reset, formState, handleSubmit } = useForm<FormDataType>({
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    defaultValues: FormValue,
    resolver: joiResolver(FormSchema),
  })

  useEffect(() => {
    if (!mountedRef.current || !open) return
    if (
      (isMembership && !userProfile.stripe_payment_method_id) ||
      (!isMembership && !membershipSetting.stripe_payment_method_id)
    )
      return
    getStripeCurrentPaymentMethodApi(isMembership)
      .then((res) => {
        if (!mountedRef.current) return
        const data = res.data.data
        const billing_details = data?.billing_details
        reset(
          {
            city: billing_details?.address?.city || '',
            country: billing_details?.address?.country || '',
            line1: billing_details?.address?.line1 || '',
            line2: billing_details?.address?.line2 || '',
            postal_code: billing_details?.address?.postal_code || '',
            state: billing_details?.address?.state || '',
            email: billing_details?.email || '',
            name: billing_details?.name || '',
            phone: billing_details?.phone || '',
            exp_month: data?.card?.exp_month || '',
            exp_year: data?.card?.exp_year || '',
          },
          { keepIsSubmitted: false },
        )
      })
      .catch((err) => {})
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open])

  const handleFormSubmit = (data) => {
    const payload = {
      billing_details: {
        email: data.email || undefined,
        name: data.name || undefined,
        phone: data.phone || undefined,
      },
    }
    if (data.city || data.country || data.line1 || data.line2 || data.postal_code || data.state) {
      payload.billing_details['address'] = {
        city: data.city || undefined,
        country: data.country || undefined,
        line1: data.line1 || undefined,
        line2: data.line2 || undefined,
        postal_code: data.postal_code || undefined,
        state: data.state || undefined,
      }
    }
    if (data.exp_month || data.exp_year) {
      payload['card'] = {
        exp_month: parseInt(data.exp_month) || undefined,
        exp_year: parseInt(data.exp_year) || undefined,
      }
    }
    setRequestUpdatePaymentMethod(true)

    updateStripePaymentMethodApi(isMembership, payload)
      .then((res) => {
        showToast('Payment updated', 'success', { autoClose: 3000, closeOnClick: true })
        setRequestUpdatePaymentMethod(false)
        onClose()
      })
      .catch((err) => {
        showToast('Server error. Try again.', 'error', { autoClose: 3000, closeOnClick: true })
        setRequestUpdatePaymentMethod(false)
      })
  }

  return (
    <Dialog
      maxWidth="xs"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={() => onClose()}
      aria-labelledby="edit-payment-info-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="edit-payment-info-dialog-title">
        Edit Billing Information
        <IconButton size="small" aria-label="close" className="close color03" onClick={(e) => onClose()}>
          <SVGClose fontSize={16} />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="body1" color="inherit" gutterBottom>
                Email
              </Typography>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                    <TextField
                      placeholder="Enter your email"
                      variant="outlined"
                      size="small"
                      error={Boolean(error)}
                      helperText={error?.message}
                      value={value}
                      onChange={(e) => {
                        onChange(e.target.value)
                      }}
                      onBlur={onBlur}
                    />
                  </FormControl>
                )}
                control={control}
                name="email"
                defaultValue=""
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body1" color="inherit" gutterBottom>
                Name
              </Typography>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                    <TextField
                      placeholder="Enter your full name"
                      variant="outlined"
                      size="small"
                      error={Boolean(error)}
                      helperText={error?.message}
                      value={value}
                      onChange={(e) => {
                        onChange(e.target.value)
                      }}
                      onBlur={onBlur}
                    />
                  </FormControl>
                )}
                control={control}
                name="name"
                defaultValue=""
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body1" color="inherit" gutterBottom>
                Address1
              </Typography>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                    <TextField
                      placeholder="Enter Address1"
                      variant="outlined"
                      size="small"
                      error={Boolean(error)}
                      helperText={error?.message}
                      value={value}
                      onChange={(e) => {
                        onChange(e.target.value)
                      }}
                      onBlur={onBlur}
                    />
                  </FormControl>
                )}
                control={control}
                name="line1"
                defaultValue=""
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body1" color="inherit" gutterBottom>
                Address2
              </Typography>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                    <TextField
                      placeholder="Enter Address2"
                      variant="outlined"
                      size="small"
                      error={Boolean(error)}
                      helperText={error?.message}
                      value={value}
                      onChange={(e) => {
                        onChange(e.target.value)
                      }}
                      onBlur={onBlur}
                    />
                  </FormControl>
                )}
                control={control}
                name="line2"
                defaultValue=""
              />
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body1" color="inherit" gutterBottom>
                City
              </Typography>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                    <TextField
                      placeholder="Enter City"
                      variant="outlined"
                      size="small"
                      error={Boolean(error)}
                      helperText={error?.message}
                      value={value}
                      onChange={(e) => {
                        onChange(e.target.value)
                      }}
                      onBlur={onBlur}
                    />
                  </FormControl>
                )}
                control={control}
                name="city"
                defaultValue=""
              />
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body1" color="inherit" gutterBottom>
                State
              </Typography>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                    <TextField
                      placeholder="Enter State"
                      variant="outlined"
                      size="small"
                      error={Boolean(error)}
                      helperText={error?.message}
                      value={value}
                      onChange={(e) => {
                        onChange(e.target.value)
                      }}
                      onBlur={onBlur}
                    />
                  </FormControl>
                )}
                control={control}
                name="state"
                defaultValue=""
              />
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body1" color="inherit" gutterBottom>
                Country
              </Typography>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                    <Autocomplete
                      id="country"
                      options={COUNTRIES}
                      disableClearable
                      size="small"
                      value={value ? COUNTRIES.find((c) => c.code === value) : null}
                      getOptionLabel={(option: any) => option?.name || ''}
                      onChange={(event: any, v, reason: string) => {
                        onChange(v?.code || '')
                      }}
                      onClose={(event: any, reason: string) => {
                        onBlur()
                      }}
                      renderInput={(params) => (
                        <TextField {...params} placeholder="Select Country" variant="outlined" />
                      )}
                    />
                  </FormControl>
                )}
                control={control}
                name="country"
                defaultValue=""
              />
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body1" color="inherit" gutterBottom>
                Zip
              </Typography>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                    <TextField
                      placeholder="Enter Zip"
                      variant="outlined"
                      size="small"
                      error={Boolean(error)}
                      helperText={error?.message}
                      value={value}
                      onChange={(e) => {
                        onChange(e.target.value)
                      }}
                      onBlur={onBlur}
                    />
                  </FormControl>
                )}
                control={control}
                name="postal_code"
                defaultValue=""
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body1" color="inherit" gutterBottom>
                Phone
              </Typography>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                    <PhoneInput
                      defaultCountry="US"
                      value={value}
                      onChange={(e) => {
                        onChange(e)
                      }}
                      inputComponent={AppPhoneNumberInput}
                      onBlur={onBlur}
                    />
                  </FormControl>
                )}
                control={control}
                name="phone"
                defaultValue=""
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body1" color="inherit" gutterBottom>
                Card
              </Typography>
              <Grid container alignItems="center" spacing={2}>
                <Grid item>
                  <Controller
                    render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                      <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                        <TextField
                          placeholder="Exp Month"
                          variant="outlined"
                          size="small"
                          type="number"
                          InputProps={{ inputProps: { min: 1, max: 12 } }}
                          error={Boolean(error)}
                          helperText={error?.message}
                          value={value}
                          onChange={onChange}
                          onBlur={onBlur}
                        />
                      </FormControl>
                    )}
                    control={control}
                    name="exp_month"
                    defaultValue={1}
                  />
                </Grid>
                <Grid item>/</Grid>
                <Grid item>
                  <Controller
                    render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                      <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                        <TextField
                          placeholder="Exp Year"
                          variant="outlined"
                          size="small"
                          type="number"
                          InputProps={{ inputProps: { min: 1970 } }}
                          error={Boolean(error)}
                          helperText={error?.message}
                          style={{ maxWidth: 80 }}
                          value={value}
                          onChange={onChange}
                          onBlur={onBlur}
                        />
                      </FormControl>
                    )}
                    control={control}
                    name="exp_year"
                    defaultValue={2022}
                  />
                </Grid>
                <Grid item xs></Grid>
              </Grid>
            </Grid>
            <Grid item xs={6}>
              <Button
                className="round-small"
                variant="outlined"
                color="inherit"
                disabled={requestUpdatePaymentMethod}
                fullWidth
                onClick={() => onClose()}
              >
                Cancel
              </Button>
            </Grid>
            <Grid item xs={6}>
              <Button
                className="round-small"
                sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
                variant="contained"
                color="primary"
                disabled={requestUpdatePaymentMethod || (formState.isDirty && !formState.isValid)}
                fullWidth
                type="submit"
              >
                {requestUpdatePaymentMethod ? <CircularProgress size={16} color="inherit" /> : 'Save'}
              </Button>
            </Grid>
          </Grid>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default React.memo(EditBillingInfo)
