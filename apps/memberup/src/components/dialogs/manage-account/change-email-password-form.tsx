import { joi<PERSON>esolver } from '@hookform/resolvers/joi'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Divider from '@mui/material/Divider'
import FormControl from '@mui/material/FormControl'
import FormHelperText from '@mui/material/FormHelperText'
import Grid from '@mui/material/Grid'
import useTheme from '@mui/material/styles/useTheme'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import clsx from 'clsx'
import Joi from 'joi'
import { useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { changePasswordA<PERSON> } from '@memberup/shared/src/services/apis/reset-password.api'
import ResetEmail from '@/memberup/components/dialogs/manage-account/reset-email'
import useAppCookie from '@/memberup/components/hooks/use-app-cookie'
import { showToast } from '@/shared-libs/toast'

type FormDataType = {
  current_password: string
  new_password: string
  confirm_password: string
}

const FormSchema = Joi.object({
  current_password: Joi.string().required().min(8).messages({
    'string.empty': 'Current Password cannot be an empty field.',
    'string.min': 'Current Password should have a minimum length of 8.',
    'any.required': 'Current Password is required.',
  }),
  new_password: Joi.string().required().min(8).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])')).messages({
    'string.empty': 'Password cannot be an empty field.',
    'string.min': 'New Password should have a minimum length of 8.',
    'string.pattern.base':
      'New Password must include at least one upper case letter, one lower case letter, and one numeric digit.',
    'any.required': 'New Password is required.',
  }),
  confirm_password: Joi.string().valid(Joi.ref('new_password')).messages({
    'string.empty': 'Password cannot be an empty field.',
    'any.only': `Password didn't match.`,
  }),
}).options({ allowUnknown: true })

export default function ChangeEmailPasswordForm({
  onSuccess,
  onCancel,
}: {
  onSuccess?: () => void
  onCancel?: () => void
}) {
  const mountedRef = useMounted(true)
  const { updateAppCookie } = useAppCookie()
  const theme = useTheme()
  const [openChangePassword, setOpenChangePassword] = useState(false)
  const [openResetEmail, setOpenResetEmail] = useState(false)
  const [requestUpdateUser, setRequestUpdateUser] = useState(false)
  const [error, setError] = useState('')
  const {
    control,
    formState: { isValid, isDirty },
    handleSubmit,
  } = useForm<FormDataType>({
    mode: 'onBlur',
    reValidateMode: 'onChange',
    defaultValues: {
      current_password: '',
      new_password: '',
      confirm_password: '',
    },
    resolver: joiResolver(FormSchema),
  })

  const handleContinue = (formData) => {
    if (formData.current_password && formData.new_password) {
      setRequestUpdateUser(true)
      changePasswordApi(formData.current_password, formData.new_password)
        .then((res) => {
          if (res.data.success) {
            showToast('Password updated', 'success')
            updateAppCookie(res.data.token)
          }
        })
        .catch((err) => {
          setError(err.response.data.message)
        })
        .finally(() => {
          if (!mountedRef.current) return
          setRequestUpdateUser(false)
        })
    }
  }

  return (
    <Box sx={{ pt: 2, pb: 2 }}>
      <form autoComplete="off" onSubmit={handleSubmit(handleContinue)} data-cy="change-email-password-form">
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Button
              className="no-padding"
              variant="text"
              size="small"
              sx={{ fontWeight: 300, fontSize: 13 }}
              endIcon={<ChevronRightIcon />}
              onClick={() => {
                setOpenResetEmail(true)
              }}
              data-cy="change-email"
            >
              Change Email
            </Button>
          </Grid>
          <Grid item xs={12}>
            <Divider />
          </Grid>
          <Grid item xs={12}>
            <Grid container spacing={2}>
              {!openChangePassword && (
                <Grid item xs={12}>
                  <Button
                    className="no-padding"
                    variant="text"
                    size="small"
                    sx={{ fontWeight: 300, fontSize: 13 }}
                    endIcon={<ChevronRightIcon />}
                    onClick={() => {
                      setOpenChangePassword(true)
                    }}
                  >
                    Change Password
                  </Button>
                </Grid>
              )}
              {openChangePassword && (
                <Grid item xs={12}>
                  <Grid container spacing={3} sx={{ mt: '-2px' }}>
                    <Grid item xs={12}>
                      <Typography variant="body1" gutterBottom>
                        Password
                      </Typography>
                      <Controller
                        render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                          <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                            <TextField
                              placeholder="Enter current password"
                              variant="outlined"
                              autoComplete="off"
                              disabled={requestUpdateUser}
                              error={Boolean(error)}
                              helperText={error?.message}
                              size="small"
                              type="password"
                              value={value}
                              onChange={(e) => {
                                setError('')
                                onChange(e)
                              }}
                              onBlur={onBlur}
                            />
                          </FormControl>
                        )}
                        control={control}
                        name="current_password"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="body1" gutterBottom>
                        New Password
                      </Typography>
                      <Controller
                        render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                          <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                            <TextField
                              placeholder="Enter new password"
                              variant="outlined"
                              disabled={requestUpdateUser}
                              error={Boolean(error)}
                              helperText={error?.message}
                              size="small"
                              type="password"
                              value={value}
                              onChange={(e) => {
                                setError('')
                                onChange((e.target.value || '').trim())
                              }}
                              onBlur={onBlur}
                            />
                          </FormControl>
                        )}
                        control={control}
                        name="new_password"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="body1" gutterBottom>
                        Confirm New Password
                      </Typography>
                      <Controller
                        render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                          <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                            <TextField
                              placeholder=""
                              variant="outlined"
                              disabled={requestUpdateUser}
                              error={Boolean(error)}
                              helperText={error?.message}
                              size="small"
                              type="password"
                              value={value}
                              onChange={(e) => {
                                setError('')
                                onChange(e)
                              }}
                              onBlur={onBlur}
                            />
                          </FormControl>
                        )}
                        control={control}
                        name="confirm_password"
                      />
                    </Grid>
                  </Grid>
                </Grid>
              )}
            </Grid>
          </Grid>
          <Grid item xs={12}>
            <Divider />
          </Grid>
          {Boolean(error) && (
            <Grid item xs={12}>
              <FormHelperText error>{error}</FormHelperText>
            </Grid>
          )}
          <Grid className="text-right" item xs={12}>
            <Button
              className="app-button round-small"
              sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
              variant="contained"
              color="primary"
              disabled={!openChangePassword || requestUpdateUser || !isValid || !isDirty}
              size="small"
              type="submit"
            >
              {requestUpdateUser ? <CircularProgress size={12} /> : 'Save'}
            </Button>
          </Grid>
        </Grid>
      </form>
      {openResetEmail && (
        <ResetEmail
          open={true}
          onClose={(e) => {
            setOpenResetEmail(false)
          }}
        />
      )}
    </Box>
  )
}
