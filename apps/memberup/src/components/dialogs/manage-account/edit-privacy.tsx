import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import FormControlLabel from '@mui/material/FormControlLabel'
import Grid from '@mui/material/Grid'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import { useEffect } from 'react'
import { Controller, useForm } from 'react-hook-form'

import { AppSwitch } from '@memberup/shared/src/components/common/input/switch'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { selectRequestUpdateProfile, selectUserProfile, updateUserProfile } from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    color: theme.palette.text.disabled,
    height: '100%',
    overflow: 'hidden',
  },
}))

type FormDataType = {
  hide_my_activity: boolean
}

const FormValue: FormDataType = {
  hide_my_activity: false,
}

export default function EditPrivacy({ handleCancel }: { handleCancel?: () => void }) {
  const classes = useStyles()
  const dispatch = useAppDispatch()
  const theme = useTheme()
  const mountedRef = useMounted(true)
  const userProfile = useAppSelector((state) => selectUserProfile(state))
  const requestUpdateProfile = useAppSelector((state) => selectRequestUpdateProfile(state))
  const { control, reset, formState, handleSubmit } = useForm<FormDataType>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: FormValue,
  })

  useEffect(() => {
    if (!mountedRef.current) return
    reset({
      hide_my_activity: userProfile?.hide_my_activity || false,
    })
    return () => {}
  }, [userProfile])

  const handleContinue = (payload) => {
    dispatch(updateUserProfile({ data: payload }))
  }

  return (
    <Box className={classes.root}>
      <form autoComplete="off" onSubmit={handleSubmit(handleContinue)}>
        <div className="formContent">
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Typography variant="body1">Manage your privacy settings and profile activity visibility.</Typography>
            </Grid>
            <Grid item xs={12}>
              <Grid container spacing={2}>
                <Grid item xs>
                  <Typography variant="body2" color="inherit">
                    Hide my activity on my profile
                  </Typography>
                </Grid>
                <Grid item>
                  <Controller
                    render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                      <FormControlLabel
                        control={
                          <AppSwitch
                            checked={value}
                            onChange={(e) => {
                              onChange(e.target.checked)
                            }}
                            name="hide_my_activity"
                          />
                        }
                        label=""
                      />
                    )}
                    control={control}
                    name="hide_my_activity"
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </div>
        <div className="buttonsWrapper text-center">
          <Button
            className="app-button round-small"
            color="primary"
            disabled={requestUpdateProfile}
            size="medium"
            variant="outlined"
            onClick={() => handleCancel?.()}
          >
            Cancel
          </Button>
          <Button
            className="app-button round-small"
            sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
            color="primary"
            disabled={requestUpdateProfile}
            type="submit"
            variant="contained"
          >
            {requestUpdateProfile ? <CircularProgress size={16} color="inherit" /> : 'Save'}
          </Button>
        </div>
      </form>
    </Box>
  )
}
