import Modal from '@mui/material/Modal'
import * as React from 'react'
import { useState } from 'react'

import { joinMembershipApi } from '@/shared-services/apis/membership.api'
import DynamicForm from '@/src/components/community/dynamic-form'
import { selectMembership, selectMembershipSetting } from '@/src/store/features/membershipSlice'
import { openDialog } from '@/src/store/features/uiSlice'
import { updateUserMembership } from '@/src/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/src/store/hooks'

export default function JoinCommunityQuestionsModal() {
  const dispatch = useAppDispatch()
  const membership = useAppSelector((state) => selectMembership(state))
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const [isSubmitting, setIsSubmitting] = useState(false)
  const handleOnClose = () => {
    dispatch(openDialog({ dialog: 'JoinCommunityQuestions', open: false }))
  }
  const handleOnSubmit = async (data) => {
    try {
      setIsSubmitting(true)
      const outputValues = JSON.parse(JSON.stringify(membershipSetting.form.fields))
      const arrayToObject = (array) => {
        return array.reduce((obj, item) => {
          obj[item.name] = item
          return obj
        }, {})
      }
      const result = arrayToObject(outputValues)
      Object.entries(data).forEach((entry) => {
        const [k, v] = entry
        result[k].value = v
      })
      const formData = {
        fields: Object.values(result),
      }
      const res = await joinMembershipApi(membership.id, formData)
      dispatch(updateUserMembership(res.data.data))
      setIsSubmitting(false)
      handleOnClose()
      dispatch(openDialog({ dialog: 'MembershipPending', open: true }))
    } catch (e) {
      setIsSubmitting(false)
    }
  }

  if (!membership) {
    return
  }

  return (
    <div>
      <Modal
        open={true}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
        data-test={'join-questions-modal'}
        onClose={() => handleOnClose()}
      >
        <div className="absolute left-1/2 top-1/2 flex w-[440px] translate-x-[-50%] translate-y-[-50%] flex-col items-center justify-center rounded-2xl bg-neutral-800 p-12">
          <div className="w-full flex-col items-start justify-start gap-6">
            <div className="flex flex-col items-center justify-start gap-1.5">
              <div className="text-white text-center font-['Graphik'] text-lg font-semibold leading-normal">
                Answer a Few Questions
              </div>
              <p className={'text-sm'}>to join {membership?.name} Community</p>
            </div>
            <DynamicForm
              fields={membershipSetting.form.fields}
              isSubmitting={isSubmitting}
              onSubmit={(data) => handleOnSubmit(data)}
            />
          </div>
        </div>
      </Modal>
    </div>
  )
}
