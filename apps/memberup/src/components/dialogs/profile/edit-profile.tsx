// import useGoogleMapApi from '@/memberup/components/hooks/use-googlemap-api'
import { joiResolver } from '@hookform/resolvers/joi'
import { Box, Grid, InputAdornment, MenuItem, Select } from '@mui/material'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import FormControl from '@mui/material/FormControl'
import IconButton from '@mui/material/IconButton'
import ListItem from '@mui/material/ListItem'
import Slide, { SlideProps } from '@mui/material/Slide'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import Joi from 'joi'
import dynamic from 'next/dynamic'
import React, { useEffect, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { useFilePicker } from 'use-file-picker'

import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { TAppCropArea } from '@memberup/shared/src/types/types'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import SVGClose from '@/memberup/components/svgs/close'
import { isUrlValid } from '@/memberup/libs/utils'
import {
  selectRequestUpdateProfile,
  selectUser,
  selectUserProfile,
  updateUserProfile,
} from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { useAppDispatch } from '@/memberup/store/store'

const AppImageCropper = dynamic(() => import('@/memberup/components/dialogs/image-cropper'), {
  ssr: false,
})

const useStyles = makeStyles((theme) => ({
  root: {
    width: '552px',
    maxWidth: '100%',
    minWidth: 380,
    marginLeft: 'auto',
    '& .MuiDialog-container': {
      alignItems: 'unset',
    },
    '& .MuiDialog-paper': {
      backgroundColor: theme.palette.background.paper,
    },
    '& .MuiDialog-paperFullWidth': {
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      maxWidth: '100%',
      margin: 0,
    },
    '& .MuiDivider-root': {
      borderColor: theme.palette.action.disabledBackground,
    },
    '& .MuiOutlinedInput-root': {
      backgroundColor: theme.palette.action.disabledBackground,
      borderRadius: 12,
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none',
      },
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    paddingTop: 38,
  },
  dialogContent: {
    minHeight: 320,
    lineHeight: 1,
    '& img': {
      borderRadius: 16,
    },
  },
  content: {
    maxWidth: 424,
    margin: 'auto',
  },
  title: {
    marginBottom: 32,
  },
  inputLabel: {
    fontSize: '14px',
    fontFamily: 'Graphik Medium',
    lineHeight: '16px',
    color: theme.palette.mode == 'dark' ? 'rgba(255, 255, 255, 0.87)' : 'rgba(0, 0, 0, 0.87)',
  },
  inputFields: {
    height: 48,
    '& .MuiInputBase-input': {
      boxSizing: 'border-box',
      height: '100%',
      fontFamily: 'Graphik Regular',
      fontSize: '14px',
    },
  },
  inputGrid: {
    marginTop: 16,
  },
  sectionContainer: {
    borderRadius: 16,
    border: `1px solid rgba(141, 148, 163, 0.12)`,
    padding: '16px 24px',
    width: '100%',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: '14px',
    fontFamily: 'Graphik Medium',
    lineHeight: '16px',
    color: theme.palette.text.primary,
    marginBottom: 8,
  },
  sectionSubTitle: {
    fontSize: '14px',
    fontFamily: 'Graphik Regular',
    lineHeight: '16px',
    color: 'rgb(141, 148, 163)',
  },
}))

type FormDataType = {
  name: string
  // about?: string
  bio: string
  image?: string
  personality_type?: string
  location?: string
  social?: {
    website?: string
    instagram?: string
    facebook?: string
    x?: string
    youtube?: string
    tiktok?: string
  }
  image_crop_area?: {
    x: number
    y: number
    width: number
    height: number
  }
}

const customSocialErrorMessages = {
  'string.max': 'Must be less than 50 characters',
  'string.pattern.base': 'Username is not valid',
}

const userNameRegex = /^[a-zA-Z0-9._]{0,50}$/

const removeAt = (value) => {
  if (value.startsWith('@')) {
    return value.slice(1)
  }
  return value
}
const FormSchema = Joi.object({
  name: Joi.string(),
  bio: Joi.string().max(150).allow(''),
  location: Joi.string().max(100).allow(''),
  social: Joi.object({
    website: Joi.string()
      .custom((value, helpers) => {
        let url = value
        if (!value.startsWith('http://') && !value.startsWith('https://')) {
          url = `https://${value}`
        }
        if (!isUrlValid(url)) {
          return helpers.error('uri.invalid')
        }
        return value
      })
      .max(50)
      .allow('')
      .messages({
        ...customSocialErrorMessages,
        'uri.invalid': 'Invalid URL',
      }),
    instagram: Joi.string().pattern(userNameRegex).max(50).allow('').messages(customSocialErrorMessages),
    facebook: Joi.string().pattern(userNameRegex).max(50).allow('').messages(customSocialErrorMessages),
    x: Joi.string().pattern(userNameRegex).max(50).allow('').messages(customSocialErrorMessages),
    tiktok: Joi.string().custom(removeAt).pattern(userNameRegex).max(50).allow('').messages(customSocialErrorMessages),
    youtube: Joi.string().custom(removeAt).pattern(userNameRegex).max(50).allow('').messages(customSocialErrorMessages),
  }),
}).options({ allowUnknown: true })

// eslint-disable-next-line react/display-name
const Transition = React.forwardRef<unknown, SlideProps>((props, ref) => (
  <Slide direction="left" {...props} ref={ref} />
))

const EditUserProfile: React.FC<{ open: boolean; onClose: () => void }> = ({ open, onClose }) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const requestUpdateProfileRef = useRef(false)
  const dispatch = useAppDispatch()
  const { theme, isDarkTheme } = useAppTheme()
  const user = useAppSelector((state) => selectUser(state))
  const profile = useAppSelector((state) => selectUserProfile(state))
  const { openFilePicker, plainFiles } = useFilePicker({
    accept: 'image/*',
    multiple: false,
    readAs: 'DataURL',
  })
  const requestUpdateProfile = useAppSelector((state) => selectRequestUpdateProfile(state))
  // const { isLoaded } = useGoogleMapApi()
  const [file, setFile] = useState<File>(null)
  const [openImageCropper, setOpenImageCropper] = useState<{
    url: string
    file?: File
    crop_area?: TAppCropArea
  }>(null)
  const hookFormMethods = useForm<FormDataType>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: {
      name: getFullName(user?.first_name, user?.last_name),
      bio: profile?.bio || '',
      personality_type: profile?.personality_type || 'DS',
      image: profile?.image || '',
      image_crop_area: profile?.image_crop_area || null,
      social: profile?.social || {},
      location: profile?.location || '',
    },
    resolver: joiResolver(FormSchema),
  })
  const { control, trigger, register, reset, formState, setValue, handleSubmit, watch } = hookFormMethods

  const bioValue = watch('bio')

  useEffect(() => {
    if (mountedRef.current && plainFiles?.[0]?.type?.indexOf('image') >= 0) {
      setOpenImageCropper({
        url: URL.createObjectURL(plainFiles[0]),
        file: plainFiles[0],
        crop_area: {
          x: 0,
          y: 0,
          width: 0,
          height: 0,
        },
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [plainFiles])

  useEffect(() => {
    if (open && requestUpdateProfileRef.current && !requestUpdateProfile) {
      onClose()
    }
    requestUpdateProfileRef.current = requestUpdateProfile
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, requestUpdateProfile])

  const profileImage = watch('image')
  const profileImageCropArea = watch('image_crop_area')

  const handleContinue = (formData) => {
    const data = {
      ...formData,
      completed_image: true,
    }
    if (file) {
      data.image_file = file
    }
    dispatch(updateUserProfile({ data }))
  }

  const handleChangePhoto = () => {
    openFilePicker()
  }

  const socialMediaInputs: {
    name: 'social.website' | 'social.instagram' | 'social.facebook' | 'social.x' | 'social.youtube' | 'social.tiktok'
    label: string
    placeholder?: string
    inputProps?: object
  }[] = [
    { name: 'social.website', label: 'Website', placeholder: 'https://' },
    {
      name: 'social.instagram',
      label: 'Instagram',
      inputProps: {
        startAdornment: <InputAdornment position="start">instagram.com/</InputAdornment>,
      },
    },
    {
      name: 'social.facebook',
      label: 'Facebook',
      inputProps: {
        startAdornment: <InputAdornment position="start">facebook.com/</InputAdornment>,
      },
    },
    {
      name: 'social.x',
      label: 'X',
      inputProps: { startAdornment: <InputAdornment position="start">x.com/</InputAdornment> },
    },
    {
      name: 'social.youtube',
      label: 'YouTube',
      inputProps: {
        startAdornment: <InputAdornment position="start">youtube.com/</InputAdornment>,
      },
    },
    {
      name: 'social.tiktok',
      label: 'TikTok',
      inputProps: { startAdornment: <InputAdornment position="start">tiktok.com/</InputAdornment> },
    },
  ]

  const personalityOptions = [
    { label: 'Dont Show', value: 'DS' },
    { label: 'ISTJ', value: 'ISTJ' },
    { label: 'ISTP', value: 'ISTP' },
    { label: 'ISFJ', value: 'ISFJ' },
    { label: 'INFJ', value: 'INFJ' },
    { label: 'INFP', value: 'INFP' },
    { label: 'INTJ', value: 'INTJ' },
    { label: 'INTP', value: 'INTP' },
    { label: 'ESTP', value: 'ESTP' },
    { label: 'ESTJ', value: 'ESTJ' },
    { label: 'ESFP', value: 'ESFP' },
    { label: 'ESFJ', value: 'ESFJ' },
    { label: 'ENFP', value: 'ENFP' },
    { label: 'ENFJ', value: 'ENFJ' },
    { label: 'ENTP', value: 'ENTP' },
    { label: 'ENTJ', value: 'ENTJ' },
  ]

  return (
    <Dialog
      maxWidth="lg"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      TransitionComponent={Transition}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 500,
          enter: 500,
          exit: 300,
        },
      }}
      aria-labelledby="edit-profile-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="edit-profile-dialog-title">
        <IconButton size="small" aria-label="close" className="close large color02" onClick={onClose}>
          <SVGClose fontSize={16} />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <div className={classes.content}>
          <Typography className={classes.title} variant="h4">
            Edit Profile
          </Typography>
          <form autoComplete="off" onSubmit={handleSubmit(handleContinue)}>
            <input type="hidden" {...register('image')} />
            <input type="hidden" {...register('image_crop_area')} />
            <Grid container spacing={2}>
              <Box className={classes.sectionContainer}>
                <Grid item xs={12}>
                  <Grid container spacing={2}>
                    <Grid item>
                      <AppProfileImage
                        imageUrl={profileImage}
                        cropArea={profileImageCropArea}
                        name={user?.first_name || user?.last_name}
                        r1={0.14}
                        r2={0.5}
                        size={104}
                        fontSize={32}
                      />
                    </Grid>
                    <Grid className={clsx('d-flex', 'align-center', 'justify-center')} item xs>
                      <Grid container>
                        <Grid item xs={12}>
                          <Button variant="text" onClick={handleChangePhoto}>
                            Change Photo
                          </Button>
                        </Grid>
                        <Grid item xs={12}>
                          <Button
                            variant="text"
                            style={{ color: 'rgb(243, 70, 70)' }}
                            onClick={() => {
                              setValue('image', null, { shouldDirty: true, shouldValidate: true })
                              setValue('image_crop_area', null, {
                                shouldDirty: true,
                                shouldValidate: true,
                              })
                            }}
                          >
                            Remove
                          </Button>
                          <br />
                          <p
                            style={{
                              fontSize: '12px',
                              margin: '0px',
                              paddingLeft: '16px',
                              paddingTop: '5px',
                            }}
                          >
                            Dimensions: 200px x 200px JPG or PNG.
                          </p>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid item xs={12} className={classes.inputGrid}>
                  <Typography className={classes.inputLabel} gutterBottom>
                    Your name
                  </Typography>
                  <Controller
                    render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                      <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                        <TextField
                          placeholder="Enter your name"
                          variant="outlined"
                          error={Boolean(error)}
                          helperText={error?.message}
                          className={classes.inputFields}
                          value={value}
                          onChange={(e) => {
                            onChange(e.target.value)
                          }}
                          onBlur={onBlur}
                          data-cy="profile-name-text-field"
                        />
                      </FormControl>
                    )}
                    control={control}
                    name="name"
                  />
                </Grid>
                <Grid
                  item
                  xs={12}
                  className={classes.inputGrid}
                  sx={{
                    '& .MuiTextField-root': { height: '100%' },
                  }}
                >
                  <Grid container>
                    <Grid item xs={6}>
                      <Typography className={classes.inputLabel} gutterBottom>
                        About Me
                      </Typography>
                    </Grid>
                    <Grid item xs={6} className="text-right">
                      <Typography
                        variant="body1"
                        sx={{
                          fontFamily: 'Graphik Regular',
                          fontSize: '12px',
                          fontWeight: 500,
                          color: '#8D94A3',
                        }}
                      >
                        {150 - bioValue.length}
                      </Typography>
                    </Grid>
                  </Grid>

                  <Controller
                    render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                      <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                        <TextField
                          placeholder="Example: I am a fitness enthusiast from sunny Florida ☀️"
                          variant="outlined"
                          multiline
                          inputProps={{ maxLength: 150 }}
                          rows={5}
                          error={Boolean(error)}
                          className={classes.inputFields}
                          helperText={error?.message}
                          value={value}
                          onChange={(e) => {
                            onChange(e.target.value)
                            trigger('bio') // manually trigger validation after change
                          }}
                          onBlur={onBlur}
                          data-cy="profile-about-me-text-field"
                        />
                      </FormControl>
                    )}
                    control={control}
                    name="bio"
                  />
                </Grid>
                <Grid
                  item
                  xs={12}
                  className={classes.inputGrid}
                  sx={{
                    '& .makeStyles-inputFields-58 .MuiInputBase-input': {
                      height: 'unset',
                    },
                  }}
                >
                  <Typography className={classes.inputLabel} gutterBottom>
                    Personality Type
                  </Typography>
                  <Controller
                    render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                      <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                        <Select
                          placeholder=""
                          variant="outlined"
                          error={Boolean(error)}
                          className={classes.inputFields}
                          value={value}
                          onChange={(e) => {
                            onChange(e.target.value)
                            trigger('personality_type') // manually trigger validation after change
                          }}
                          onBlur={onBlur}
                          data-cy="profile-personality-type-select-field"
                          MenuProps={{
                            PaperProps: {
                              sx: {
                                borderRadius: '12px',
                                boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
                                border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
                                backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
                                backgroundImage: 'unset',
                                padding: '6px',
                                '& li.Mui-selected': {
                                  backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
                                  backgroundImage: 'unset',
                                  borderRadius: '12px',
                                },
                                '& .MuiList-root': {
                                  padding: '0px',
                                },
                              },
                            },
                          }}
                        >
                          {personalityOptions.map((option) => (
                            <MenuItem
                              key={option.value}
                              value={option.value}
                              sx={{
                                '&:hover': {
                                  backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
                                  borderRadius: '12px',
                                },
                              }}
                            >
                              <ListItem
                                style={{
                                  justifyContent: 'left',
                                  paddingLeft: '5px',
                                  paddingTop: '0px',
                                  fontFamily: 'unset',
                                }}
                              >
                                {option.label}
                              </ListItem>
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    )}
                    control={control}
                    name="personality_type"
                  />
                </Grid>
                <Grid item xs={12} className={classes.inputGrid}>
                  <Typography className={classes.inputLabel} gutterBottom>
                    Where are you from
                  </Typography>
                  <Controller
                    render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                      <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                        <TextField
                          placeholder="New York, NY"
                          variant="outlined"
                          error={Boolean(error)}
                          className={classes.inputFields}
                          helperText={error?.message}
                          value={value}
                          onChange={(e) => {
                            onChange(e.target.value)
                            trigger('location') // manually trigger validation after change
                          }}
                          onBlur={onBlur}
                          data-cy="profile-about-me-text-field"
                        />
                      </FormControl>
                    )}
                    control={control}
                    name="location"
                  />
                </Grid>
              </Box>
              <Box className={classes.sectionContainer}>
                <Box>
                  <Typography className={classes.sectionTitle}>Social</Typography>
                  <Typography className={classes.sectionSubTitle}>Add your website and social links</Typography>
                </Box>
                {socialMediaInputs.map((input) => {
                  return (
                    <Grid item xs={12} className={classes.inputGrid}>
                      <Typography className={classes.inputLabel} gutterBottom>
                        {input.label}
                      </Typography>
                      <Controller
                        render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                          <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                            <TextField
                              placeholder={input.placeholder}
                              variant="outlined"
                              error={Boolean(error)}
                              className={classes.inputFields}
                              helperText={error?.message}
                              InputProps={input.inputProps}
                              value={value}
                              onChange={(e) => {
                                onChange(e.target.value)
                                trigger(input.name) // manually trigger validation after change
                              }}
                              onBlur={onBlur}
                              data-cy="profile-about-me-text-field"
                              sx={{
                                '& .MuiFormHelperText-root': {
                                  textAlign: 'end',
                                },
                              }}
                            />
                          </FormControl>
                        )}
                        control={control}
                        name={input.name}
                      />
                    </Grid>
                  )
                })}
              </Box>
              <Grid className="text-right" item xs={12}>
                <Button
                  className="app-button round-small"
                  variant="outlined"
                  color="primary"
                  disabled={requestUpdateProfile}
                  onClick={onClose}
                >
                  Cancel
                </Button>
                <Button
                  className="app-button round-small"
                  sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
                  variant="contained"
                  color="primary"
                  type="submit"
                  disabled={!formState?.isDirty || !formState?.isValid || requestUpdateProfile}
                  data-cy="profile-submit-button"
                >
                  {requestUpdateProfile ? <CircularProgress size={16} /> : 'Save Changes'}
                </Button>
              </Grid>
            </Grid>
          </form>
        </div>
        {Boolean(openImageCropper?.url) && (
          <AppImageCropper
            title="Crop Image"
            url={openImageCropper.url}
            cropSize={{ width: 300, height: 300 }}
            initialFile={openImageCropper.file}
            initialCroppedAreaPercentages={openImageCropper.crop_area}
            open={true}
            applyButtonText="Apply"
            style={{
              containerStyle: {
                overflow: 'visible',
              },
              cropAreaStyle: {
                borderRadius: '42%',
              },
            }}
            onApply={(e) => {
              setValue('image', e.url, { shouldDirty: true, shouldValidate: true })
              setValue('image_crop_area', e.crop_area, { shouldDirty: true, shouldValidate: true })
              setFile(e.file)
              setOpenImageCropper(null)
            }}
            onClose={() => setOpenImageCropper(null)}
          />
        )}
      </DialogContent>
    </Dialog>
  )
}

export default EditUserProfile
