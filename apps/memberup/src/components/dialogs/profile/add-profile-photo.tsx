// TODO: Can we organize these files so that we know they are apart of new user onboarding?
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import CloseIcon from '@mui/icons-material/Close'
import PersonOutlineIcon from '@mui/icons-material/PersonOutline'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import LinearProgress from '@mui/material/LinearProgress'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import React from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { MediaPicker } from '@memberup/shared/src/components/common/pickers/media-picker'
import { IUserProfile } from '@memberup/shared/src/types/interfaces'
import {
  selectRequestUpdateProfile,
  selectUser,
  selectUserProfile,
  setCompleteUserProfileStep,
  updateUserProfile,
} from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
      backgroundColor: theme.palette.text.primary,
    },
    '& .MuiInputBase-root': {
      color: theme.palette.background.default,
    },
    '& .MuiOutlinedInput-root': {
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: theme.palette.action.disabledBackground,
      },
      '&.Mui-focused': {
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: theme.palette.action.disabledBackground,
        },
      },
    },
  },
  dialogTitle: {
    borderBottom: 'none',
  },
  dialogContent: {
    minHeight: 320,
    color: theme.palette.primary.light,
    lineHeight: 1,
    padding: '0 24px 24px 24px',
    '& img': {
      borderRadius: 16,
    },
    textAlign: 'center',
  },
  title: {
    fontSize: 20,
  },
  backButton: {
    fontSize: 14,
    position: 'absolute',
    left: 8,
    top: 8,
  },
}))

const AddProfilePhoto: React.FC<{
  open: boolean
  onGoBack: () => void
  onClose: () => void
}> = ({ open, onGoBack, onClose }) => {
  const classes = useStyles()
  const dispatch = useAppDispatch()
  const user = useAppSelector((state) => selectUser(state))
  const profile = useAppSelector((state) => selectUserProfile(state))
  const requestUpdateProfile = useAppSelector((state) => selectRequestUpdateProfile(state))

  const handleContinue = (payload: Partial<IUserProfile>) => {
    dispatch(updateUserProfile({ data: payload }))
  }

  const handleDoThisLater = () => {
    dispatch(setCompleteUserProfileStep(2))
    dispatch(updateUserProfile({ data: { completed_image: true } }))
  }

  return (
    <Dialog
      maxWidth="sm"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      aria-labelledby="complete-profile-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="complete-profile-dialog-title">
        <Button
          variant="text"
          className={clsx(classes.backButton, 'color03')}
          startIcon={<ArrowBackIcon color="inherit" />}
          onClick={onGoBack}
        >
          Back
        </Button>
        <IconButton size="small" aria-label="close" className="close large color03" onClick={onClose}>
          <CloseIcon fontSize="inherit" />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography className={classes.title} variant="h4" color="secondary" gutterBottom>
              Add a profile photo
            </Typography>
            <Typography className="text-center" variant="body1" color="inherit">
              Connect with members message to get to know members,
              <br />
              share thoughts or discuss ideas.
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Box
              className="app-image-wrapper"
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
                m: 'auto',
                width: 200,
                height: 200,
                overflow: 'hidden',
                backgroundColor: '#EBE2DD',
                borderRadius: '42%',
              }}
            >
              {profile?.image ? (
                <AppImg
                  src={profile.image || user.image}
                  width={200}
                  height={200}
                  style={{ maxWidth: 200, maxHeight: 200 }}
                  alt="Profile Picture"
                />
              ) : (
                <PersonOutlineIcon
                  sx={{
                    fontSize: 96,
                    opacity: 0.2,
                  }}
                />
              )}
            </Box>
          </Grid>
          <Grid item xs={12}>
            <MediaPicker
              className="small"
              buttonColor="primary"
              buttonStr="Change Photo"
              buttonVariant="contained"
              onChange={(e) =>
                handleContinue({
                  completed_image: true,
                  image: e?.secure_url || '',
                  image_crop_area: null,
                })
              }
            />
          </Grid>
          {!profile?.completed_image && (
            <Grid item xs={12} className="text-center">
              <Button variant="text" color="inherit" fullWidth onClick={handleDoThisLater}>
                I&#8217;ll do this later
              </Button>
            </Grid>
          )}
          <Grid item xs={12}>
            <LinearProgress variant="determinate" value={100} />
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default AddProfilePhoto
