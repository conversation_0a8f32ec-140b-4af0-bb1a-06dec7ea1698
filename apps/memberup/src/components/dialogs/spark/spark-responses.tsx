//@ts-nocheck
import { Modal } from '@mui/material'
import Backdrop from '@mui/material/Backdrop'
import Box from '@mui/material/Box'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import Slide from '@mui/material/Slide'
import Stack from '@mui/material/Stack'
import useTheme from '@mui/material/styles/useTheme'
import { TransitionProps } from '@mui/material/transitions'
import Typography from '@mui/material/Typography'
import useMediaQuery from '@mui/material/useMediaQuery'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import React, { useMemo, useRef, useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import { MessageResponse } from 'stream-chat'
import {
  Channel as ReactStreamChatChannel,
  StreamMessage,
  useChannelStateContext,
  useChatContext,
} from 'stream-chat-react'

import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { CHANNEL_TYPE_ENUM, FEED_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { ISparkQuestion } from '@memberup/shared/src/types/interfaces'
import { SparkExpirationCounter } from '@/components/spark/spark-expiration-counter'
import { SparkStatusIndicator } from '@/components/spark/spark-status-indicator'
import AppLinkify from '@/memberup/components/common/app-linkify'
import LoadingSpinner from '@/memberup/components/common/loaders/loading-spinner'
import useAppStreamMessageListener from '@/memberup/components/hooks/use-app-stream-message-listener'
import useAppStreamReactionListener from '@/memberup/components/hooks/use-app-stream-reaction-listener'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import NewCommentSpark from '@/memberup/components/stream-chat/new-comment-spark'
import ReplyReaction from '@/memberup/components/stream-chat/reply-reaction'
import SVGClock from '@/memberup/components/svgs/clock'
import SVGCloseNew from '@/memberup/components/svgs/close-new'
import SVGVerified from '@/memberup/components/svgs/verified'
import { selectUser, selectUserSparkStreak } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="up" ref={ref} {...props} />
})

const useStyles = makeStyles((theme) => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-around',
    borderRadius: 20,
    maxWidth: '512px',
    height: 'auto',
    width: 'auto',
    margin: 'auto',
    overflowX: 'hidden',
    '@media (max-width:540px)': {
      maxWidth: '90%',
    },
    '& .MuiDivider-root': {
      borderColor: theme.palette.action.hover,
    },
    '& .MuiOutlinedInput-root': {
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none',
      },
    },
    '& .MuiOutlinedInput-multiline': {
      padding: 0,
    },
    '&.mobile': {
      '& .MuiDialog-container': {
        alignItems: 'flex-end',
      },
      '& .MuiDialog-paper': {
        margin: 0,
        width: '100%',
      },
    },
    '& .str-chat': {
      height: '100%',
    },
    '& .str-chat.messaging, & .str-chat.commerce': {
      backgroundColor: '#FFFFFF',
      color: '#000000',
    },
  },
  dialogTitleBox: (props) => ({
    background: props.newGradient,
    borderRadius: 16,
    paddingLeft: '16px',
    paddingRight: '16px',
    height: 'auto',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    boxSizing: 'border-box',
    maxWidth: '500px',
    width: '100%',
    '@media (max-width:540px)': {
      paddingBottom: 8,
      fontSize: 10,
    },
  }),
  dialogTitle: {
    borderBottom: 'none',
    fontSize: 20,
    lineHeight: '24px',
    padding: 24,
    paddingTop: 32,
    paddingBottom: 16,
    textAlign: 'center',
    fontFamily: 'Graphik SemiBold',
    '@media (max-width:967px)': {
      fontSize: 16,
      paddingLeft: 12,
      paddingRight: 12,
      marginBottom: 16,
    },
  },
  dialogContent: {
    position: 'relative',
    borderRadius: 16,
    display: 'flex',
    fontFamily: 'Graphik Bold',
    flexDirection: 'column',
    justifyContent: 'space-between',
    minHeight: '50vh',
    maxHeight: '50vh',
    height: '50vh',
    overflow: 'auto',
    flex: 1,
    padding: 0,
    '@media (max-width:540px)': {
      fontSize: 16,
    },
  },
  disabledFont: {
    opacity: 0.87,
  },
}))

const Reply: React.FC<{
  reply: MessageResponse
}> = ({ reply: originalReply }) => {
  const { channel: streamChatChannel } = useChannelStateContext()
  const { requestGetReactions, reactions, updatedMessage } = useAppStreamReactionListener(
    streamChatChannel,
    originalReply as StreamMessage,
  )
  const [visibleNewComment, setVisibleNewComment] = useState(false)
  const reply = updatedMessage || originalReply

  const renderComments = useMemo(() => {
    const temp = reactions.filter((item) => item.type.indexOf('reply') >= 0)
    if (!temp.length) return null
    return (
      <Grid item xs={12} sx={{ marginTop: '16px' }}>
        <Box sx={{ pl: 3 }}>
          <Stack flexDirection="column-reverse">
            {temp.map((item: any, index) => {
              const commentUser = item.user
              return (
                <Box key={`comment-${index}`} padding={1} sx={{ mb: 2 }}>
                  <Grid container spacing={3}>
                    <Grid item>
                      <Grid container>
                        <Grid item>
                          <AppProfileImage
                            imageUrl={commentUser?.image}
                            cropArea={commentUser?.image_crop_area}
                            name={commentUser?.name || ''}
                            size={30}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid item xs>
                      <Grid item xs={12}>
                        <Grid container alignItems="center">
                          <Typography className="font-family-graphik-semibold" variant="body1">
                            {commentUser?.name || 'No Name'}
                          </Typography>
                          {commentUser?.role === 'global_admin' && (
                            <Box ml={1}>
                              <SVGVerified width={16} height={16} />
                            </Box>
                          )}
                        </Grid>
                        {Boolean(item.text) && (
                          <Grid item xs={12} sx={{ maxWidth: '100%', overflow: 'hidden', wordBreak: 'break-word' }}>
                            <AppLinkify content={item.text} visibleLinks={false} />
                          </Grid>
                        )}
                      </Grid>
                    </Grid>
                  </Grid>
                </Box>
              )
            })}
          </Stack>
        </Box>
      </Grid>
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reactions])

  return (
    <Stack direction={'column'}>
      <Stack direction={'row'} gap={3}>
        <Stack direction={'column'} gap={2}>
          <AppProfileImage
            imageUrl={reply.user?.image as string}
            cropArea={reply.user?.image_crop_area as any}
            name={reply.user?.name || 'No Name'}
            size={40}
          />
          <Typography variant="caption" sx={{ opacity: '50%', lineHeight: '12px', fontSize: '10px' }}>
            {formatDate({ date: reply.created_at, format: 'hh:mmaaa' })}
          </Typography>
        </Stack>
        <Stack sx={{ flexGrow: 1 }}>
          <Stack direction={'row'}>
            <Typography
              className="font-family-graphik-semibold"
              variant="body1"
              sx={{
                pb: '4px',
              }}
            >
              {reply.user?.name || 'No Name'}
            </Typography>
            {reply.user?.role === 'global_admin' && (
              <SVGVerified styles={{ marginLeft: '4px' }} width={16} height={16} />
            )}
          </Stack>
          {Boolean(reply.text) && <AppLinkify content={reply.text} visibleLinks={false} />}
          <ReplyReaction onNewCommentClick={() => setVisibleNewComment(!visibleNewComment)} message={reply as any} />
        </Stack>
      </Stack>
      {visibleNewComment && (
        <NewCommentSpark
          message={reply as StreamMessage}
          isReaction={true}
          enableCancel={true}
          onlyUpdateStreamChat={true}
          style={{
            borderRadius: '8px',
            paddingLeft: '8px',
            paddingRight: '8px',
            paddingBottom: '36px',
            paddingTop: '8px',
          }}
          onSave={() => setVisibleNewComment(false)}
          handleCancel={() => setVisibleNewComment(false)}
        />
      )}
      {renderComments}
    </Stack>
  )
}

const NoResponses = () => {
  const { loading, messages } = useChannelStateContext()
  if (!loading && messages.length === 0) {
    return (
      <Box sx={{ padding: 3 }}>
        <Typography align="center" color="text.disabled" variant="body1">
          No Responses
        </Typography>
      </Box>
    )
  }
  return null
}

const ResponseList: React.FC<{ membershipQuestionId: string }> = ({ membershipQuestionId }) => {
  const mountedRef = useMounted(true)
  const user = useAppSelector((state) => selectUser(state))
  const { channel: streamChatChannel } = useChannelStateContext()
  const { requestFetchMessages, messages, fetchMessages } = useAppStreamMessageListener(
    true,
    'unpinned',
    streamChatChannel,
    null,
    {
      deleted_at: { $exists: false },
      parent_id: { $exists: false },
      pinned: { $eq: false },
      feed_status: { $nin: [FEED_STATUS_ENUM.rejected, FEED_STATUS_ENUM.reported] },
      m_question_id: membershipQuestionId,
    },
    {
      limit: 8,
      sort: { created_at: 1 },
    },
  )

  const handleFetchMore = () => {
    if (!mountedRef.current) return
    fetchMessages()
  }

  return (
    <Box className="d-flex direction-column h-100" style={{ padding: 0 }} data-cy={'test'}>
      <Box
        id="spark-response-list"
        sx={{
          flex: 1,
          mt: 2,
          overflowY: 'auto',
        }}
      >
        {!requestFetchMessages && !messages?.results.length && <NoResponses />}
        <InfiniteScroll
          dataLength={messages.results.length}
          next={handleFetchMore}
          hasMore={messages.hasMore}
          loader={<LoadingSpinner size={24} />}
          scrollableTarget="spark-response-list"
          style={{ overflow: 'unset' }}
        >
          {messages.results.map((item, index) => (
            <Box key={item.id} sx={{ pt: 3, width: '100%' }}>
              {index > 0 && <Divider sx={{ color: 'RGBA(141, 148, 163, 0.12)', mb: 3 }} />}
              <Reply reply={item} />
            </Box>
          ))}
        </InfiniteScroll>
      </Box>
    </Box>
  )
}

const CustomBackdrop: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  return (
    <Backdrop sx={{ zIndex: '-10 !important' }} open={true} onClick={onClose}>
      <IconButton
        color="inherit"
        size="medium"
        className="close large"
        onClick={onClose}
        aria-label="close"
        sx={{
          background: theme.palette.mode === 'dark' ? '#17171a' : '#ffffff',
          color: '#8D94A3',
          p: '14px',
          m: '6px',
          width: '40px',
          height: '40px',
          position: 'absolute',
          right: '8px',
          top: '8px',
          '&.MuiIconButton-root': {
            position: isMobile ? '' : '',
          },
          '&.hover': {
            backgroundColor: theme.palette.mode === 'dark' ? '#000000' : '#ffffff',
          },
        }}
      >
        <SVGCloseNew fontSize={16} />
      </IconButton>
    </Backdrop>
  )
}

const SparkResponsesDialog: React.FC<{
  open: boolean
  question: ISparkQuestion
  onClose: () => void
}> = ({ open, question, onClose }) => {
  const userStreak = useAppSelector((state) => selectUserSparkStreak(state))
  const { isMobile, theme, newGradient } = useAppTheme()
  const classes = useStyles({ newGradient })
  const { client } = useChatContext()
  const [loading, setLoading] = useState(true)
  const [message, setMessage] = useState<StreamMessage>(null)

  const streamChatChannel = useMemo(() => {
    const mCategoryId = question?.m_category_id
    return client?.user && mCategoryId ? client.channel(CHANNEL_TYPE_ENUM.team, mCategoryId) : undefined
  }, [question, client])

  const content = question.spark_m_questions?.[0]?.content || question.content || ''

  const disabledTextColor = theme.palette.mode === 'light' ? 'RGBA(255, 255, 255, 0.8)' : theme.palette.action.disabled

  const dialogContentRef = useRef(null)
  const [showMoreAnswersMessage, setShowMoreAnswersMessage] = useState(false)

  const membershipQuestionId = question.id

  const responseList = useMemo(() => {
    return <ResponseList membershipQuestionId={membershipQuestionId} />
  }, [membershipQuestionId])

  return (
    <Modal
      hideBackdrop
      maxWidth="xs"
      fullWidth={true}
      className={clsx(classes.root, { mobile: isMobile })}
      open={open}
      onClose={onClose}
      TransitionComponent={isMobile ? Transition : undefined}
      keepMounted
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      aria-labelledby="spark-responses"
    >
      <Box
        sx={{
          padding: '24px',
          backgroundColor: theme.palette.background.paper,
          borderRadius: '20px',
        }}
      >
        <CustomBackdrop onClose={onClose} />
        <Box className={classes.dialogTitleBox}>
          <DialogTitle className={classes.dialogTitle} id="spark-share-answer">
            {content}
            {!isMobile && (
              <div className="pb-5 pt-9">
                <SparkStatusIndicator streak={userStreak} />
              </div>
            )}
          </DialogTitle>
        </Box>
        <DialogContent className={classes.dialogContent} ref={dialogContentRef}>
          <Box
            sx={{
              height: '100%',
              position: 'relative',
              fontFamily: 'Graphik Bold',
            }}
          >
            <ReactStreamChatChannel channel={streamChatChannel}>
              <Box
                className="d-flex direction-column h-100"
                sx={{ pt: '8px', backgroundColor: theme.palette.background.paper }}
              >
                <Box className="w-100" sx={{ flex: 1, overflow: 'hidden' }}>
                  {responseList}
                </Box>
              </Box>
            </ReactStreamChatChannel>
            {showMoreAnswersMessage && (
              <Box sx={{ display: 'flex', alignItems: 'center', my: 3 }}>
                <Box flexGrow={1}>
                  <Divider />
                </Box>
                <Typography
                  sx={{
                    mx: 4,
                    fontFamily: 'Graphik Medium',
                    color: 'RGB(141, 148, 163)',
                    fontSize: '14px',
                  }}
                  align="center"
                >
                  Come back for more answers
                </Typography>
                <Box flexGrow={1}>
                  <Divider />
                </Box>
              </Box>
            )}
          </Box>
        </DialogContent>
      </Box>
    </Modal>
  )
}

export default SparkResponsesDialog
