// TODO: Are we using this?
import Box from '@mui/material/Box'
import React from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { ILive } from '@memberup/shared/src/types/interfaces'
import { selectMembershipAssetsPath } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const itemSx = {
  width: '100%',
  maxWidth: 220,
  minWidth: 220,
  borderRadius: '12px',
  borderColor: 'rgba(255, 255, 255, 0.08)',
  borderWidth: 1,
  borderStyle: 'solid',
  m: '8px',
  overflow: 'hidden',
  '& img': {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
  },
}

const GiveawayItem: React.FC<{ space: ILive }> = ({ space }) => {
  const membershipAssetsPath = useAppSelector((state) => selectMembershipAssetsPath(state))

  return (
    <Box
      sx={{
        display: 'flex',
        width: '100%',
        fontSize: 12,
        overflow: 'hidden',
      }}
    >
      <Box
        sx={{
          ...itemSx,
          mr: 0,
        }}
      >
        <Box
          className="app-image-wrapper"
          sx={{
            width: '100%',
            maxWidth: 220,
            height: 220,
            backgroundColor: '#000000',
            borderRadius: '12px',
            overflow: 'hidden',
          }}
        >
          <AppImg src={`${membershipAssetsPath}/images/space.png`} height={100} width={100} alt="Space Thumbnail" />
        </Box>
        <Box className="color02" sx={{ p: '12px', lineHeight: '14px' }}>
          Survival is one of the most demanding and challenging issues that we face as humans! Survival challenges us
          through many different issues such as: child abuse, sexual abuse, birth, death, job loss, health problems, low
          sel…
        </Box>
      </Box>
      <Box className="app-image-wrapper" sx={{ itemSx }}>
        <AppImg
          style={{ height: '100%' }}
          height={100}
          width={100}
          src={`${membershipAssetsPath}/images/space.png`}
          alt="Space Thumbnail"
        />
      </Box>
    </Box>
  )
}

export default GiveawayItem
