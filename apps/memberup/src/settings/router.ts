import { LAYOUT_ENUM } from '@/shared-types/enum'

const isMaintenanceMode = process.env.NEXT_PUBLIC_MAINTENANCE_MODE === 'true'

let ROUTES = {
  '/account-settings/profile': {
    title: 'Profile settings',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.main_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/auth/change-password': {
    title: 'Change Password',
    authRequired: false,
    isCreatorRoute: false,
  },
  '/auth/reset-password': {
    title: 'Reset Password',
    authRequired: false,
    isCreatorRoute: false,
  },
  '/auth/login': {
    title: 'Sign In',
  },
  '/auth/signup': {
    title: 'Sign Up',
  },
  '/auth/signup/payment': {
    title: 'Setup Payment',
    authRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.auth_layout,
      attributes: {},
    },
  },
  '/create-community/[plan]/checkout': {
    title: 'Create your community',
    authRequired: false,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.auth_layout2,
      attributes: {},
    },
  },
  '/signup': {
    title: 'Sign Up',
    authRequired: false,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.auth_layout2,
      attributes: {},
    },
  },
  '/verify': {
    title: 'Email verification',
    authRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.auth_layout2,
      attributes: {},
    },
  },
  '/benefits': {
    title: 'Benefits',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/billing-confirmed': {
    title: 'Billing Confirmed',
    authRequired: true,
    isCreatorRoute: true,
  },
  '/[slug]/course/[id]': {
    title: 'Course',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/course/builder/[courseId]': {
    title: 'Course Builder',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
    },
  },
  '/[slug]/events': {
    title: 'Events',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/[slug]/events/[id]': {
    title: 'Event',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
  },
  '/getting-started': {
    title: 'Getting Started',
    authRequired: true,
    chatRequired: false,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/goals': {
    title: 'Goals',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]': {
    title: 'Community',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/about': {
    title: 'About',
    authRequired: false,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/join': {
    title: 'Join',
    authRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {},
    },
  },
  '/[slug]/join/payment': {
    title: 'Join',
    authRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {},
    },
  },
  '/[slug]/space/[spaceSlug]': {
    title: 'Space',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/library': {
    title: 'Content',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeaderMobile: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/inbox': {
    title: 'Messages',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/intro': {
    title: 'Intro',
    authRequired: false,
    isCreatorRoute: false,
  },
  // '/live': {
  //   title: 'Live',
  //   authRequired: true,
  //   chatRequired: true,
  //   isCreatorRoute: false,
  //   layout: {
  //     layout: LAYOUT_ENUM.page_layout,
  //     attributes: {
  //       visibleSideMenu: true,
  //       visibleHeader: true,
  //     },
  //   },
  // },
  // '/live/cameo': {
  //   title: 'Live',
  //   authRequired: true,
  //   chatRequired: true,
  //   isCreatorRoute: false,
  //   layout: {
  //     layout: LAYOUT_ENUM.live_layout,
  //     attributes: {},
  //   },
  // },
  // '/live/cameo/past/[stream_id]': {
  //   title: 'Live',
  //   authRequired: true,
  //   chatRequired: true,
  //   isCreatorRoute: false,
  //   layout: {
  //     layout: LAYOUT_ENUM.live_layout,
  //     attributes: {},
  //   },
  // },
  '/library': {
    title: 'Content',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        fullWidth: true,
        visibleHeaderMobile: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/members/[status]': {
    title: 'Members',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/[slug]/members': {
    title: 'Members',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/new-profile': {
    title: 'Profile',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/notifications': {
    title: 'Events ',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
  },
  '/plan': {
    title: 'Pricing Plans',
    authRequired: true,
    isCreatorRoute: true,
  },
  '/plan/upgrade/[plan]': {
    title: 'Billing Details',
    authRequired: true,
    isCreatorRoute: true,
  },
  '/post/[id]': {
    title: 'Post',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/redirect': {
    title: 'Home',
    authRequired: false,
    chatRequired: false,
    isCreatorRoute: false,
  },
  // '/settings/calendar': {
  //   title: 'Calendar',
  //   authRequired: true,
  //   isCreatorRoute: true,
  //   layout: {
  //     layout: LAYOUT_ENUM.page_layout,
  //     desktopOnly: true,
  //     attributes: {
  //       visibleSideMenu: true,
  //       visibleHeader: true,
  //     },
  //   },
  // },

  '/[slug]/settings/analytics': {
    title: 'Analytics',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/settings/community-pricing': {
    title: 'Community Pricing Plans',
    authRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        visibleSideMenu: true,
        visibleHeader: false,
      },
    },
  },
  '/[slug]/settings/stripe-connect-success': {
    title: 'Stripe Connect Success',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/settings/community-settings': {
    title: 'Community Settings',
    authRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/[slug]/settings/membership-questions': {
    title: 'Membership Questions',
    authRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/[slug]/settings/add-membership-question': {
    title: 'Membership Questions',
    authRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/[slug]/settings/members-requests': {
    title: 'Member Requests',
    authRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/[slug]/settings/deactivate-account': {
    title: 'Deactivate Account',
    authRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/settings/emails': {
    title: 'Emails',
    authRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/settings/home': {
    title: 'Manage Home',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  // '/[slug]/settings/getting-started': {
  //   title: 'Getting Started',
  //   authRequired: true,
  //   chatRequired: true,
  //   isCreatorRoute: true,
  //   layout: {
  //     layout: LAYOUT_ENUM.page_layout,
  //     attributes: {
  //       visibleHeader: true,
  //       visibleSideMenu: true,
  //       visibleTitle: true,
  //     },
  //   },
  // },
  // '/settings/integrations': {
  //   title: 'Integrations',
  //   authRequired: true,
  //   isCreatorRoute: true,
  //   layout: {
  //     layout: LAYOUT_ENUM.page_layout,
  //     desktopOnly: true,
  //     attributes: {
  //       visibleHeader: true,
  //       visibleSideMenu: true,
  //       visibleTitle: true,
  //     },
  //   },
  // },
  '/[slug]/settings/invite-links': {
    title: 'Invite Links',
    authRequired: true,
    chatRequired: false,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        visibleSideMenu: true,
        visibleHeader: false,
      },
    },
  },
  '/[slug]/settings/invite-members': {
    title: 'Invite Members',
    authRequired: true,
    chatRequired: false,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        visibleSideMenu: true,
        visibleHeader: false,
      },
    },
  },
  '/[slug]/settings/library': {
    title: 'Manage Content',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/settings/members': {
    title: 'Member Management',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        visibleHeader: false,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/[slug]/settings/admin-roles': {
    title: 'Admin Roles',
    authRequired: true,
    chatRequired: false,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        visibleSideMenu: true,
        visibleHeader: false,
      },
    },
  },
  '/[slug]/settings/memberup-billing': {
    title: 'Billing',
    subtitle: 'Setup custom pricing for a customer group or run pricing experiments.',
    authRequired: true,
    chatRequired: false,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        visibleSideMenu: true,
        visibleHeader: false,
      },
    },
  },
  '/[slug]/settings/moderation': {
    title: 'Moderation',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/settings/onboarding': {
    title: 'Onboarding',
    authRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/settings/organize-spaces': {
    title: 'Organize Spaces',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        visibleHeader: false,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/settings/settings-roles': {
    title: 'Creator Roles',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/settings/upgrade': {
    title: 'Pricing Plans',
    authRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        visibleSideMenu: true,
        visibleHeader: false,
      },
    },
  },
  // '/settings/giveaways': {
  //   title: 'Giveaways',
  //   authRequired: true,
  //   isCreatorRoute: true,
  //   layout: {
  //     layout: LAYOUT_ENUM.page_layout,
  //     desktopOnly: true,
  //     attributes: {
  //       visibleSideMenu: true,
  //       visibleHeader: true,
  //     },
  //   },
  // },
  // '/settings/shop': {
  //   title: 'Shop',
  //   authRequired: true,
  //   isCreatorRoute: true,
  //   layout: {
  //     layout: LAYOUT_ENUM.page_layout,
  //     desktopOnly: true,
  //     attributes: {
  //       visibleSideMenu: true,
  //       visibleHeader: true,
  //     },
  //   },
  // },

  // '/settings/notifications': {
  //   title: 'Notifications',
  //   authRequired: true,
  //   isCreatorRoute: true,
  //   layout: {
  //     layout: LAYOUT_ENUM.page_layout,
  //     desktopOnly: true,
  //     attributes: {
  //       visibleHeader: true,
  //       visibleSideMenu: true,
  //       visibleTitle: true,
  //     },
  //   },
  // },
  '/[slug]/settings/signup-pages': {
    title: 'Signup & Login',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/settings/spark': {
    title: 'Spark',
    authRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/[slug]/settings/spark/[category]': {
    title: 'Spark',
    authRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        simpleHeader: true,
        visibleSideMenu: true,
        visibleHeader: false,
      },
    },
  },
  '/[slug]/settings/themes': {
    title: 'Themes',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: true,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      desktopOnly: true,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/settings/account/account': {
    title: 'Account settings',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout_legacy,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/settings/account/account/a': {
    title: 'Account settings',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/settings/account/notifications': {
    title: 'Notifications settings',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/settings/account/password': {
    title: 'Password settings',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout_legacy,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/settings/account/password/a': {
    title: 'Password settings',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/settings/account/payment-history': {
    title: 'Payment history',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout_legacy,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/settings/account/payment-methods': {
    title: 'Payment methods',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout_legacy,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/settings/account/referrals-wip': {
    title: 'Payment methods',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout_legacy,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/settings/account/profile': {
    title: 'Profile settings',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout_legacy,
      attributes: {
        simpleHeader: true,
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: false,
      },
    },
  },
  '/space/[slug]': {
    title: 'Space',
    authRequired: true,
    chatRequired: true,
    isCreatorRoute: false,
    layout: {
      layout: LAYOUT_ENUM.page_layout,
      attributes: {
        visibleHeader: true,
        visibleSideMenu: true,
        visibleTitle: true,
      },
    },
  },
  '/server-error': {
    title: 'Not Found',
    authRequired: false,
    isCreatorRoute: false,
  },
  '/@/[username]': {
    title: 'User Profile',
    authRequired: false,
    isCreatorRoute: false,
  },
  '/404': {
    title: 'Not Found',
    authRequired: false,
    isCreatorRoute: false,
  },
  '/maintenance': {
    title: 'Coming Soon',
    authRequired: false,
    isCreatorRoute: false,
  },

  // TODO UA: Fix this for the user profile, we need to integrate with the communities
  // '/[slug]': {
  //   title: 'Profile',
  //   authRequired: true,
  //   chatRequired: true,
  //   isCreatorRoute: false,
  //   layout: {
  //     layout: LAYOUT_ENUM.page_layout,
  //     attributes: {
  //       visibleHeader: true,
  //       visibleSideMenu: true,
  //       visibleTitle: false,
  //     },
  //   },
  // },
}

ROUTES[isMaintenanceMode ? '/*' : '/'] = {
  title: 'Home',
  authRequired: false,
  chatRequired: false,
  //redirect: isMaintenanceMode ? '/maintenance' : '/community',
  isCreatorRoute: false,
}

export { ROUTES }

export const AUTH_ROUTES = ['/auth/signin', '/auth/login', '/auth/signup']
export const GLOBAL_ROUTES = ['/404', '/redirect', '/redirect-new']
export const NO_REDIRECT_ROUTES = ['/getting-started', '/course/[id]', '/course/builder/[courseId]']
