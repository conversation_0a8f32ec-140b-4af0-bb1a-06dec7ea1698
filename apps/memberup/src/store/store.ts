import { configureStore, createSlice, current, PayloadAction } from '@reduxjs/toolkit'
import { TypedUseSelectorHook, useDispatch, useSelector, useStore } from 'react-redux'
import { combineReducers } from 'redux'
import createSagaMiddleware from 'redux-saga'

import contentLibraryReducer from './features/contentLibrarySlice'
import eventReducer from './features/eventSlice'
import feedAggregationReducer from './features/feedAggregationSlice'
import feedReducer from './features/feedSlice'
import feedTrackReducer from './features/feedTrackSlice'
import headerReducer from './features/headerSlice'
import inboxReducer from './features/inboxSlice'
import likesReducer from './features/likesSlice'
import liveReducer from './features/liveSlice'
import membershipReducer from './features/membershipSlice'
import memberReducer from './features/memberSlice'
import spaceReducer from './features/spaceSlice'
import sparkReducer from './features/sparkSlice'
import uiReducer from './features/uiSlice'
import userReducer from './features/userSlice'
import saga from './saga'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { IUser, IUserProfile } from '@memberup/shared/src/types/interfaces'
import { TError, TStartMembershipPayload } from '@memberup/shared/src/types/types'
import { generateSpacesData } from '@/lib/communities'
import { InitialStoreState } from '@/store'

export interface UserState {
  canceledSignUp: boolean
  completeProfileStep: number
  user: IUser | null
  profile: IUserProfile | null
  knockUserToken: string
  streamChatUserToken: string
  sparkStreak: number
  requestGetActiveUser: boolean
  requestStartMembership: boolean
  requestUpdateProfile: boolean
  requestConnectStripeAccount: boolean
  requestDisconnectStripeAccount: boolean
  error: TError
  streamChatConnected: boolean
  isReady: boolean
}

const initialState: UserState = {
  canceledSignUp: false,
  completeProfileStep: 0,
  user: null,
  profile: null,
  knockUserToken: '',
  streamChatUserToken: '',
  sparkStreak: 0,
  requestGetActiveUser: false,
  requestStartMembership: false,
  requestUpdateProfile: false,
  requestConnectStripeAccount: false,
  requestDisconnectStripeAccount: false,
  error: null,
  streamChatConnected: false,
  isReady: false,
}

export const userSlice = createSlice({
  name: 'userSlice',
  initialState,
  // The `reducers` field lets us define reducers and generate associated actions
  reducers: {
    // Use the PayloadAction type to declare the contents of `action.payload`
    setUser(state, action: PayloadAction<IUser>) {
      console.log('PAYLOAD', action.payload)
      state.user = action.payload
      state.profile = action.payload.profile
    },
    setUserProfile(state, action: PayloadAction<IUserProfile>) {
      state.profile = action.payload
    },
    updateUser: (state, action: PayloadAction<Partial<IUser>>) => {
      console.log('updateUser', action.payload)
      state.user = {
        ...state.user,
        ...action.payload,
      }
    },
    updateUserState: (state, action: PayloadAction<Partial<IUser>>) => {
      console.log('updateUser', action.payload)
      state = {
        ...state,
        ...action.payload,
      }
    },
    // TODO: Rename to resetUserState or something else.
    resetUser: (state, action: PayloadAction<Partial<UserState>>) => {
      let keys = Object.keys(action.payload)
      let data

      if (keys.length) {
        data = action.payload
      } else {
        keys = Object.keys(initialState)
        data = initialState
      }
      for (const key of keys) {
        state[key] = data[key]
      }
    },
    setStreamChatConnected: (state, action: PayloadAction<boolean>) => {
      state.streamChatConnected = action.payload
    },
    setCanceledSignUp: (state, action: PayloadAction<boolean>) => {
      state.error = null
      state.canceledSignUp = action.payload
    },
    setCompleteUserProfileStep: (state, action: PayloadAction<number>) => {
      state.error = null
      state.completeProfileStep = action.payload
    },
    getActiveUser: (state, action: PayloadAction<{ time_zone?: string }>) => {
      state.error = null
      state.requestGetActiveUser = true
    },
    getActiveUserSuccess: (
      state,
      action: PayloadAction<{
        user: IUser
        profile?: IUserProfile
        streamChatUserToken?: string
        knockToken?: string
      }>,
    ) => {
      //const before = current(state)
      const { user, profile, streamChatUserToken, knockToken } = action.payload
      state.profile = profile
      state.knockUserToken = knockToken
      state.streamChatUserToken = streamChatUserToken
      state.user = user
      state.error = null
      state.isReady = true
      state.requestGetActiveUser = false
    },
    getActiveUserFailure: (state, action: PayloadAction<TError>) => {
      state.error = action.payload
      state.user = null
      state.profile = null
      state.requestGetActiveUser = false
    },
    // TODO 3023: The profile should always come with the user
    getActiveUserProfileSuccess: (state, action: PayloadAction<{ profile: IUserProfile }>) => {
      state.error = null
      state.profile = action.payload.profile
    },
    startMembership: (state, action: PayloadAction<TStartMembershipPayload>) => {
      state.requestStartMembership = true
    },
    updateUserProfile: (
      state,
      action: PayloadAction<{
        data: Partial<IUserProfile>
        successMessage?: string
        errorMessage?: string
      }>,
    ) => {
      state.requestUpdateProfile = true
      // const before = current(state.profile) || {}
      // const payload = action.payload as any
      // state.profile = {
      //   ...before,
      //   ...payload,
      // }
    },
    mergeUserProfile: (state, action: PayloadAction<{ spark_hidden_at?: any; pinned_posts_hidden?: any }>) => {
      state.profile = { ...state.profile, ...action.payload }
    },
    updateUserMembership: (state, action) => {
      let userMemberships = [...state.user.user_memberships]
      const index = userMemberships.findIndex((item) => item.id === action.payload.id)
      if (index !== -1) {
        // Replace the existing object
        userMemberships[index] = action.payload
      } else {
        // Add the new object to the end of the array
        userMemberships.push(action.payload)
      }
      state.user.user_memberships = userMemberships
    },
    updateUserProfileSuccess: (state, action: PayloadAction<IUserProfile>) => {
      const before = current(state)
      const { user, ...rest } = (action.payload || {}) as any
      let completedProfileStep = rest?.completed_profile ? 2 : 0
      completedProfileStep = completedProfileStep === 2 && rest?.completed_image ? 3 : completedProfileStep
      state.completeProfileStep = completedProfileStep
      if (user) {
        state.user = {
          ...(before.user || {}),
          ...user,
        }
      }
      state.profile = {
        ...(before.profile || {}),
        ...(rest || {}),
      }

      state.requestUpdateProfile = false
    },
    updateUserProfileFailure: (state, action: PayloadAction<TError>) => {
      state.requestUpdateProfile = false
    },
    setUserError: (state, action: PayloadAction<TError>) => {
      state.profile = null
      state.error = action.payload
      state.requestGetActiveUser = false
      state.requestUpdateProfile = false
    },
  },
})

export const {
  startMembership,
  getActiveUser,
  getActiveUserSuccess,
  getActiveUserFailure,
  getActiveUserProfileSuccess,
  updateUserProfile,
  mergeUserProfile,
  updateUserProfileSuccess,
  updateUserProfileFailure,
  resetUser,
  setCanceledSignUp,
  setCompleteUserProfileStep,
  setUserError,
  updateUserMembership,
  setStreamChatConnected,
  setUser,
  setUserProfile,
  updateUser,
} = userSlice.actions

export const selectStreamChatConnected = (state: RootState) => state.user.streamChatConnected

export const selectCanceledSignUp = (state: RootState) => state.user.canceledSignUp
export const selectCompleteUserProfileStep = (state: RootState) => state.user.completeProfileStep
export const selectUser = (state: RootState) => state.user.user
export const selectUserIsReady = (state: RootState) => state.user.isReady
export const selectUserFullName = (state: RootState) =>
  getFullName(state.user.user?.first_name, state.user.user?.last_name, '')
export const selectUserProfile = (state: RootState) => state.user.profile
export const selectLibraryLayout = (state: RootState) => state.user.profile.library_layout
export const selectKnockToken = (state: RootState) => state.user.knockUserToken
export const selectStreamChatUserToken = (state: RootState) => state.user.streamChatUserToken
export const selectUserSparkStreak = (state: RootState) => state.user.sparkStreak
export const selectRequestConnectStripeAccount = (state: RootState) => state.user.requestConnectStripeAccount
export const selectRequestGetActiveUser = (state: RootState) => state.user.requestGetActiveUser
export const selectRequestUpdateProfile = (state: RootState) => state.user.requestUpdateProfile
export const selectRequestStartMembership = (state: RootState) => state.user.requestStartMembership
export const selectUserError = (state: RootState) => state.user.error
export default userSlice.reducer

export const makeStore = (initialData: Partial<InitialStoreState>) => {
  const reduxInitialData = {
    membership: {
      membership: initialData?.community?.membership,
      membershipSetting: initialData?.community?.membership?.membership_setting,
      requestGetMembership: false,
      requestUpdateMembershipSetting: false,
      membershipError: null,
      membershipSettingError: null,
      stripeError: null,
      updateStatus: '',
      owner: initialData?.community?.owner,
    },
    user: {
      canceledSignUp: false,
      completeProfileStep: 0,
      sparkStreak: 0,
      requestGetActiveUser: false,
      requestStartMembership: false,
      requestUpdateProfile: false,
      requestConnectStripeAccount: false,
      requestDisconnectStripeAccount: false,
      error: null,
      streamChatConnected: false,
      isReady: true,
      streamChatUserToken: initialData?.auth?.streamChatUserToken,
      user: initialData?.auth?.user
        ? {
            ...initialData?.auth?.user,
          }
        : null,
      profile: initialData?.auth?.profile
        ? {
            ...initialData?.auth?.profile,
          }
        : null,
    },
  }

  const reducers = combineReducers({
    contentLibrary: contentLibraryReducer,
    event: eventReducer,
    feed: feedReducer,
    feedTrack: feedTrackReducer,
    header: headerReducer,
    live: liveReducer,
    membership: membershipReducer,
    member: memberReducer,
    space: spaceReducer,
    feedAggregation: feedAggregationReducer,
    ui: uiReducer,
    user: userReducer,
    likes: likesReducer,
    inbox: inboxReducer,
    spark: sparkReducer,
  })

  const sagaMiddleware = createSagaMiddleware()

  const store = configureStore({
    reducer: reducers,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({ serializableCheck: false, thunk: false }).concat(sagaMiddleware),
    preloadedState: reduxInitialData,
  })

  sagaMiddleware.run(saga)

  return store
}

export type AppStore = ReturnType<typeof makeStore>
export type AppDispatch = AppStore['dispatch']
export type RootState = ReturnType<AppStore['getState']>

export const useAppDispatch: () => AppDispatch = useDispatch
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector
export const useAppStore: () => AppStore = useStore
