import { createSlice, current, PayloadAction } from '@reduxjs/toolkit'

import { RootState } from '../store'
import { IUser, IUserProfile } from '@memberup/shared/src/types/interfaces'
import { TError } from '@memberup/shared/src/types/types'
import { getFullName } from '@/shared-libs/profile'

export interface UserState {
  activeMember: Partial<IUser>
  members: {
    total: number
    docs: Partial<IUser>[]
  }
  requestGetMember: boolean
  requestGetMembers: boolean
  error: TError | null
  spaceMembers: {
    [key: string]: IUser
  }
}

const initialState: UserState = {
  activeMember: null,
  members: {
    total: 0,
    docs: [],
  },
  requestGetMember: false,
  requestGetMembers: false,
  error: null,
  spaceMembers: {},
}

const TIME_THRESHOLD_SECONDS = 90

export const memberSlice = createSlice({
  name: 'memberSlice',
  initialState,
  // The `reducers` field lets us define reducers and generate associated actions
  reducers: {
    // Use the PayloadAction type to declare the contents of `action.payload`
    setActiveMember: (state, action: PayloadAction<Partial<IUser>>) => {
      state.error = null
      state.activeMember = action.payload
    },
    getMemberSuccess: (state, action: PayloadAction<IUser>) => {
      state.activeMember = action.payload
      state.requestGetMember = false
    },
    getMembers: (state, action: PayloadAction<{ membershipId: string; where: string; take: number; skip: number }>) => {
      state.error = null
      state.requestGetMembers = true
    },
    getMembersSuccess: (
      state,
      action: PayloadAction<{
        total: number
        docs: IUser[]
      }>,
    ) => {
      state.members = action.payload
      const currentTime = Date.now()
      const membersMap = action.payload.docs.reduce((accMap, member) => {
        const lastActiveTime = new Date(member.profile.last_activity_at).getTime()
        const isOnline = (currentTime - lastActiveTime) / 1000 < TIME_THRESHOLD_SECONDS

        /* we don't want to override the online and last_active fields from the other update */
        accMap[member.id] = {
          ...accMap[member.id],
          ...member,
          name: getFullName(member.first_name, member.last_name),
          isOnline: isOnline,
        }
        return accMap
      }, {})

      state.spaceMembers = membersMap
    },
    setMembersStatuses: (state, action: PayloadAction<IUser[]>) => {
      action.payload.map((member) => {
        const lastActiveTime = new Date(member.last_active).getTime()
        const currentTime = Date.now()
        const isOnline = (currentTime - lastActiveTime) / 1000 < 90

        if (state.spaceMembers[member.id]) {
          state.spaceMembers[member.id] = {
            ...state.spaceMembers[member.id],
            last_active: member.last_active,
            online: isOnline,
          }
        }
      })
    },
    updateMember: (state, action: PayloadAction<Partial<IUser>>) => {
      const { id, profile, ...rest } = action.payload
      const before = current(state)
      const index = before.members.docs.findIndex((item) => item.id === id)
      if (index >= 0) {
        const oldMember = before.members.docs[index]
        state.members = {
          docs: [
            ...before.members.docs.slice(0, index),
            {
              ...oldMember,
              ...(rest || {}),
              profile: {
                ...(oldMember?.profile || {}),
                ...(profile || {}),
              },
            } as any,
            ...before.members.docs.slice(index + 1),
          ],
          total: before.members.total,
        }
      }

      state.spaceMembers[id] = {
        ...state.spaceMembers[id],
        ...(rest || {}),
        profile: {
          ...((state.spaceMembers?.[id]?.profile || {}) as IUserProfile),
          ...(profile || {}),
        },
      }
    },
    updateMemberProfile: (state, action: PayloadAction<Partial<any>>) => {
      const {
        bio,
        cover_image,
        cover_image_crop_area,
        getting_started,
        image,
        image_crop_area,
        location,
        social,
        user: { id, first_name, last_name },
      } = action.payload

      if (state.spaceMembers[id]) {
        state.spaceMembers[id] = {
          ...state.spaceMembers[id],
          profile: {
            ...state.spaceMembers[id].profile,
            bio,
            cover_image,
            cover_image_crop_area,
            getting_started,
            image,
            image_crop_area,
            location,
            social,
          },
          first_name,
          last_name,
          name: getFullName(first_name, last_name),
        }
      }
    },
    deleteMember: (state, action: PayloadAction<string>) => {
      const before = current(state)
      state.members = {
        docs: before.members.docs.filter((item) => item.id !== action.payload),
        total: before.members.total,
      }

      delete state.spaceMembers[action.payload]
    },
    setMemberError: (state, action: PayloadAction<TError>) => {
      state.error = action.payload
      state.requestGetMembers = false
    },
  },
})

export const {
  setActiveMember,
  getMemberSuccess,
  getMembers,
  getMembersSuccess,
  deleteMember,
  updateMember,
  setMemberError,
  setMembersStatuses,
  updateMemberProfile,
} = memberSlice.actions
export const selectActiveMember = (state: RootState) => state.member.activeMember
export const selectMembers = (state: RootState) => state.member.members
export const selectRequestGetMember = (state: RootState) => state.member.requestGetMember
export const selectRequestGetMembers = (state: RootState) => state.member.requestGetMembers
export const selectMemberError = (state: RootState) => state.member.error
export const selectMembersMap = (state: RootState) => state.member.spaceMembers
// export const selectMembersAggregatedData = (state: RootState) => {
//   let totalMembers = 0
//   let totalAdmins = 0
//   let totalOnline = 0
// }
export default memberSlice.reducer
