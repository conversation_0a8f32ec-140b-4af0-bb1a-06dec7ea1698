import { createSlice, current, PayloadAction } from '@reduxjs/toolkit'

import { RootState } from '../store'
import { IChannel } from '@memberup/shared/src/types/interfaces'
import { TError } from '@memberup/shared/src/types/types'

export interface SpaceState {
  channels: {
    total: number
    docs: IChannel[]
  }
  activeChannel: IChannel
  requestGetChannels: boolean
  requestUpsertChannel: boolean
  requestDeleteChannel: boolean
  updateStatus: string
  error: TError
}

const initialState: SpaceState = {
  channels: {
    total: 0,
    docs: [],
  },
  activeChannel: null,
  requestGetChannels: false,
  requestUpsertChannel: false,
  requestDeleteChannel: false,
  updateStatus: '',
  error: null,
}

export const spaceSlice = createSlice({
  name: 'spaceSlice',
  initialState,
  // The `reducers` field lets us define reducers and generate associated actions
  reducers: {
    // Use the PayloadAction type to declare the contents of `action.payload`
    resetSpaceState: () => initialState,
    setActiveChannel: (state, action: PayloadAction<IChannel>) => {
      state.activeChannel = action.payload
    },
    getChannels: (
      state,
      action: PayloadAction<{ init: boolean; membershipId: string; take?: number; skip?: number }>,
    ) => {
      state.error = null
      state.requestGetChannels = true
    },
    getChannelsSuccess: (
      state,
      action: PayloadAction<{ data: { total: number; docs: IChannel[] }; init: boolean }>,
    ) => {
      state.error = null
      state.requestGetChannels = false
      if (action.payload.init) {
        state.channels = action.payload.data
      } else {
        if (action.payload.data.docs.length) {
          const before = current(state)
          state.channels = {
            total: action.payload.data.total,
            docs: before.channels.docs.concat(action.payload.data.docs),
          }
        }
      }
    },
    getChannelsFailure: (state, action: PayloadAction<{ init: boolean; take?: number; skip?: number }>) => {
      state.error = null
      state.requestGetChannels = true
    },
    upsertChannel: (state, action: PayloadAction<Partial<IChannel>>) => {
      state.error = null
      state.requestUpsertChannel = true
      state.updateStatus = ''
    },
    upsertChannelSuccess: (state, action: PayloadAction<IChannel>) => {
      const before = current(state)
      const tempIndex = before.channels.docs.findIndex((c) => c.id === action.payload.id)
      if (tempIndex >= 0) {
        state.channels = {
          ...before.channels,
          docs: before.channels.docs
            .slice(0, tempIndex)
            .concat(action.payload, before.channels.docs.slice(tempIndex + 1)),
        }
      } else {
        state.channels = {
          total: before.channels.total + 1,
          docs: [...before.channels.docs, action.payload],
        }
      }
      state.activeChannel = action.payload
      state.updateStatus = 'updated'
      state.error = null
      state.requestUpsertChannel = false
    },
    upsertChannelFailure: (state, action: PayloadAction<TError>) => {
      state.error = action.payload
      state.requestUpsertChannel = false
    },
    deleteChannel: (state, action: PayloadAction<string>) => {
      const before = current(state)
      state.channels = {
        total: before.channels.total - 1,
        docs: before.channels.docs.filter((c) => c.id !== action.payload),
      }
      state.error = null
      state.requestDeleteChannel = true
      state.updateStatus = ''
    },
    deleteChannelSuccess: (state, action: PayloadAction<string>) => {
      state.error = null
      state.requestDeleteChannel = false
      state.updateStatus = 'updated'
    },
    deleteChannelFailure: (state, action: PayloadAction<{ error: TError; channel: IChannel; index: number }>) => {
      const before = current(state)
      if (action.payload.index >= 0 && action.payload.channel) {
        state.channels = {
          total: before.channels.total + 1,
          docs: before.channels.docs
            .slice(0, action.payload.index)
            .concat(action.payload.channel, before.channels.docs.slice(action.payload.index + 1)),
        }
      }
      state.error = action.payload.error
      state.requestDeleteChannel = false
    },
    setSpaceError: (state, action: PayloadAction<TError>) => {
      state.error = action.payload
    },
  },
})

export const {
  resetSpaceState,
  getChannels,
  getChannelsSuccess,
  getChannelsFailure,
  upsertChannel,
  upsertChannelSuccess,
  upsertChannelFailure,
  deleteChannel,
  deleteChannelSuccess,
  deleteChannelFailure,
  setActiveChannel,
  setSpaceError,
} = spaceSlice.actions

export const selectRequestGetChannels = (state: RootState) => state.space.requestGetChannels
export const selectChannels = (state: RootState) => state.space.channels
export const selectActiveChannels = (state: RootState) => state.space.channels.docs.filter((c) => c.active)

export const selectActiveChannel = (state: RootState) => state.space.activeChannel
export const selectRequestUpsertChannel = (state: RootState) => state.space.requestUpsertChannel
export const selectRequestDeleteChannel = (state: RootState) => state.space.requestDeleteChannel
export const selectSpaceUpdateStatus = (state: RootState) => state.space.updateStatus
export const selectSpaceError = (state: RootState) => state.space.error

export default spaceSlice.reducer
