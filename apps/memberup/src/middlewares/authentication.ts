import { auth } from '@/auth'
import { findUser } from '@/shared-libs/prisma/user'

const authenticationMiddleware = async (req: any, res: any, next: any) => {
  try {
    const session = await auth(req, res)

    if (!session?.user?.id) return res.status(401).end('Failed Authentication.')
    const user = await findUser({
      where: {
        id: session.user.id,
      },
      include: {
        profile: true,
        user_memberships: true,
      },
    })

    if (!user) {
      return res.status(401).end('Failed Authentication.')
    }

    req['user'] = user

    return next()
  } catch (err: any) {
    console.error('err', err)

    if (typeof err === 'string') {
      return res.status(401).end(err)
    } else if (err.message === 'jwt expired') {
      return res.status(401).end('Token Expired. Please login again.')
    } else {
      return res.status(401).end('Invalid Token. Please login again.')
    }
  }
}

export default authenticationMiddleware
