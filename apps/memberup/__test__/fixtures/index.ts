import { data } from './data'
import prisma from '@memberup/shared/src/libs/prisma/prisma'

export const loadFixtures = async () => {
  const keys = Object.keys(data)
  for (const key of keys) {
    await prisma[key].createMany({
      data: data[key],
    })
  }
}

export const clearFixtures = async () => {
  const keys = Object.keys(data)
  for (const key of keys) {
    await prisma[key].deleteMany({})
  }
}
