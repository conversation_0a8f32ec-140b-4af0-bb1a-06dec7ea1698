import { USER_MEMBERSHIP_STATUS_ENUM, USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'

export const data = {
  membership: [
    {
      id: '1',
      name: 'Discoverable Community 1',
      slug: 'discoverable-community-1',
      domain: 'discoverable1.test.com',
      active: true,
      brand: 'Test Brand 1',
      deactive_reason: null,
    },
    {
      id: '2',
      name: 'Discoverable Community 2',
      slug: 'discoverable-community-2',
      domain: 'discoverable2.test.com',
      active: true,
      brand: 'Test Brand 2',
      deactive_reason: null,
    },
    {
      id: '3',
      name: 'Non-Discoverable Community',
      slug: 'non-discoverable-community',
      domain: 'nondiscoverable.test.com',
      active: true,
      brand: 'Test Brand 3',
      deactive_reason: null,
    },
  ],

  membershipSetting: [
    {
      id: '1',
      membership_id: '1',
      discoverable: true,
      visibility: 'public',
    },
    {
      id: '2',
      membership_id: '2',
      discoverable: true,
      visibility: 'public',
    },
    {
      id: '3',
      membership_id: '3',
      discoverable: false,
      visibility: 'public',
    },
  ],

  user: [
    {
      id: '1',
      username: 'admin1',
      email: '<EMAIL>',
      password: 'password',
      role: USER_ROLE_ENUM.admin,
      first_name: 'Admin',
      last_name: 'Admin',
    },
    {
      id: '2',
      username: 'member2',
      email: '<EMAIL>',
      password: 'password',
      role: USER_ROLE_ENUM.member,
      first_name: 'Member',
      last_name: 'Member',
    },
  ],

  userMembership: [
    {
      id: '1',
      user_id: '2',
      membership_id: '1',
      status: USER_MEMBERSHIP_STATUS_ENUM.accepted,
    },
  ],
}
