import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

export default async function middleware(req: NextRequest) {
  const { host, hostname, pathname, searchParams } = req.nextUrl
  // console.log('test ======', req.nextUrl)
  // if (pathname === '/redirect') {
  //   console.log('test ======', req.nextUrl)
  //   const state = searchParams.get('state')
  //   const code = searchParams.get('code')
  //   if (state) {
  //     return NextResponse.redirect(`${state}${code ? `?stripe_code=${code}` : ''}`)
  //   }
  // }

  /* if (process.env.NEXT_PUBLIC_MAINTENANCE_MODE === 'true') {
    const protocol = host.includes('localhost') ? 'http' : 'https'
    return NextResponse.redirect(`${protocol}://${host}/maintenance`)
  } */

  if (!hostname) {
    const tempUrl = new URL('/404', req.url)
    tempUrl.searchParams.set('from', req.nextUrl.pathname)
    return NextResponse.redirect(tempUrl)
  }

  // const nextAuthValue = req.cookies.get('next-auth.session-token')
  // const secureNextAuthValue = req.cookies.get('__Secure-next-auth.session-token')

  // if (
  //   !nextAuthValue &&
  //   !secureNextAuthValue &&
  //   ROUTES[pathname].authRequired &&
  //   !NO_REDIRECT_ROUTES.includes(pathname)
  // ) {
  //   const tempUrl = new URL('/auth/signin', req.url)
  //   return NextResponse.redirect(tempUrl)
  // }
}

export const config = {
  matcher: [
    '/',
    '/achievements/:path*',
    '/auth/:path*',
    '/benefits/:path*',
    '/settings/:path*',
    '/events/:path*',
    '/giveaways/:path*',
    '/goals/:path*',
    '/inbox/:path*',
    // '/live/:path*',
    '/members/:path*',
    '/notifications/:path*',
    '/plan/:path*',
    '/space/:path*',
    '/billing-confirmed',
    '/getting-started',
    '/community',
    '/redirect',
    '/server-error',
  ],
}
