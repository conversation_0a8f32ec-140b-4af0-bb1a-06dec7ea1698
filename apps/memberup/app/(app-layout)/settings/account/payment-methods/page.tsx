'use client'

import { PaymentMethod } from '@stripe/stripe-js'
import Link from 'next/link'
import { useEffect, useRef, useState } from 'react'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { DynamicCardIcon, MoreHorizontal20Icon } from '@/components/icons'
import { LoadingErrorMessage } from '@/components/layout/loading-error-message'
import { AddPaymentMethodDialog } from '@/components/payments/add-payment-method-dialog'
import { Button, SkeletonBox } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { toast } from '@/components/ui/sonner'
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table'
import { unexpectedError } from '@/lib/error-messages'
import {
  detachStripePaymentMethod<PERSON><PERSON>,
  getStripePaymentMethodsApi,
  setStripeDefaultPaymentMethodApi,
} from '@/shared-services/apis/stripe.api'
import useCheckUserRole from '@/src/components/hooks/use-check-user-role'
import { mergeUserProfile, selectUserProfile } from '@/src/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/src/store/hooks'

export default function PaymentMethodsPage() {
  const mounted = useMounted()
  const dispatch = useAppDispatch()
  const { isCurrentUserAdmin } = useCheckUserRole()
  const [loading, setLoading] = useState(true)
  const [loadingError, setLoadingError] = useState(false)
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [addingPaymentMethod, setAddingPaymentMethod] = useState(false)
  const [confirmingPaymentMethodRemovalId, setConfirmingPaymentMethodRemovalId] = useState<string | null>(null)
  const [detachingPaymentMethod, setDetachingPaymentMethod] = useState<string | null>(null)
  const [changingDefault, setChangingDefault] = useState(false)
  const originalPaymentMethodId = useRef(null)
  const profile = useAppSelector((state) => selectUserProfile(state))
  const initialized = useRef(false)

  const getPaymentMethods = async () => {
    if (!loading) setLoading(true)

    try {
      const response = await getStripePaymentMethodsApi(false, { type: 'card' })

      if (mounted.current) {
        setPaymentMethods(response.data.data.data)
        setLoading(false)
      }
    } catch {
      setLoadingError(true)
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!initialized.current) {
      initialized.current = true
      getPaymentMethods()
    }
  }, [])

  const onRemove = (id: string) => {
    if (id === profile.stripe_payment_method_id) {
      toast.info('Please set another card as default before removing this card.')
      return
    }

    setConfirmingPaymentMethodRemovalId(id)
  }

  const detachPaymentMethod = async () => {
    const paymentMethodId = confirmingPaymentMethodRemovalId
    setConfirmingPaymentMethodRemovalId(null)
    setDetachingPaymentMethod(paymentMethodId)

    try {
      await detachStripePaymentMethodApi(false, paymentMethodId)

      if (mounted.current) {
        setPaymentMethods(paymentMethods.filter((paymentMethod: PaymentMethod) => paymentMethod.id !== paymentMethodId))
        setDetachingPaymentMethod(null)
      }
    } catch {
      if (mounted.current) {
        setDetachingPaymentMethod(null)
        toast.error('Failed to remove payment method')
      }
    }
  }

  const setDefaultPaymentMethod = async (paymentMethod: PaymentMethod) => {
    setChangingDefault(true)
    originalPaymentMethodId.current = profile.stripe_payment_method_id

    try {
      dispatch(mergeUserProfile({ stripe_payment_method_id: paymentMethod.id }))

      await setStripeDefaultPaymentMethodApi(false, paymentMethod.id)
      setChangingDefault(false)
    } catch (err) {
      if (mounted.current) {
        dispatch(mergeUserProfile({ stripe_payment_method_id: originalPaymentMethodId.current }))
      }
      toast.error(err.response?.message || unexpectedError)
    } finally {
      if (mounted.current) setChangingDefault(false)
    }
  }

  const onAddPaymentMethod = (paymentMethod: PaymentMethod) => {
    setPaymentMethods([paymentMethod, ...paymentMethods])
  }

  const retryRequest = () => {
    setLoadingError(false)
    getPaymentMethods()
  }

  if (loadingError) {
    return <LoadingErrorMessage retryRequest={retryRequest} />
  }

  const paymentMethodsAvailable = paymentMethods && paymentMethods.length > 0

  return (
    <div className="page-inner-pb">
      <div className="tailwind-component rounded-box max-w-screen-md p-5">
        <h2 className="-mt-1 mb-1 text-lg font-semibold">Payment Methods</h2>
        {loading ? (
          <SkeletonBox />
        ) : (
          <div>
            <div className="mb-4 text-sm text-black-600 dark:text-black-100">
              <p>
                Cards for communities that you own.
                {isCurrentUserAdmin && (
                  <span>
                    &nbsp;Cards for communities you own are&nbsp;
                    <Link className="text-primary-100 hover:text-primary-200" href="/settings/memberup-billing">
                      here
                    </Link>
                    .
                  </span>
                )}
              </p>
              {!paymentMethods ||
                (paymentMethods.length === 0 && <p className="mb-1">You do not have any cards on file.</p>)}
            </div>
            {paymentMethodsAvailable && (
              <>
                <Table className="mb-8">
                  <TableBody>
                    {paymentMethods?.map((paymentMethod: PaymentMethod) => {
                      const isDefault = profile.stripe_payment_method_id === paymentMethod.id

                      if (detachingPaymentMethod !== paymentMethod.id) {
                        return (
                          <TableRow
                            key={paymentMethod.id}
                            className="max-h-0 overflow-hidden border-b border-b-grey-200 text-black-200 dark:border-b-grey-900 dark:text-black-100 [&_td]:py-4"
                            variant="condensed"
                          >
                            <TableCell className="select-none pr-2">
                              <DynamicCardIcon card={paymentMethod.card.brand} className="h-6 w-8" />
                            </TableCell>
                            <TableCell className="w-full text-sm">
                              <span className="pr-4 capitalize">{paymentMethod.card.brand} Card</span>
                              <span className="font-semibold">**{paymentMethod.card.last4}</span>
                              {isDefault && <span className="ml-2">(Default)</span>}
                            </TableCell>
                            <TableCell className="text-sm md:px-5">
                              {`${paymentMethod.card.exp_month.toString().padStart(2, '0')}/${paymentMethod.card.exp_year.toString().slice(2)}`}
                            </TableCell>
                            <TableCell className="md:pl-5 md:pr-4">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="inline"
                                    className="flex h-5 w-5 cursor-pointer items-center justify-center overflow-hidden"
                                  >
                                    <MoreHorizontal20Icon className="relative left-0.5 cursor-pointer" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent className="w-56">
                                  <DropdownMenuItem
                                    className="cursor-pointer"
                                    onClick={
                                      // Added delay to allow for pointer-events: none to be removed from the body
                                      // so it can be added back by the Modal component
                                      () => {
                                        setTimeout(() => {
                                          onRemove(paymentMethod.id)
                                        }, 100)
                                      }
                                    }
                                  >
                                    Remove
                                  </DropdownMenuItem>
                                  {!isDefault && (
                                    <DropdownMenuItem
                                      className="cursor-pointer"
                                      onClick={() => !changingDefault && setDefaultPaymentMethod(paymentMethod)}
                                    >
                                      Make default
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        )
                      }
                    })}
                  </TableBody>
                </Table>
                <ConfirmModal
                  title="Are you sure you want to remove this card?"
                  onConfirm={detachPaymentMethod}
                  open={!!confirmingPaymentMethodRemovalId}
                  onCancel={() => setConfirmingPaymentMethodRemovalId(null)}
                />
              </>
            )}
            <div className="flex justify-end">
              <Button className={paymentMethodsAvailable ? '' : 'w-full'} onClick={() => setAddingPaymentMethod(true)}>
                Add Card
              </Button>
            </div>
          </div>
        )}
        <AddPaymentMethodDialog
          open={addingPaymentMethod}
          setOpen={(value) => setAddingPaymentMethod(value)}
          onAddPaymentMethod={onAddPaymentMethod}
          paymentMethods={paymentMethods}
        />
      </div>
    </div>
  )
}
