'use client'

import { saveAs } from 'file-saver'
import { useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { numberToCurrency } from '@memberup/shared/src/libs/numeric-utils'
import { Download20Icon } from '@/components/icons'
import { LoadingErrorMessage } from '@/components/layout/loading-error-message'
import { Button, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SkeletonBox } from '@/components/ui'
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useStore } from '@/hooks/useStore'
import { getPaymentHistoryApi } from '@/shared-services/apis/stripe.api'

export default function PaymentHistoryPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const membershipId = searchParams.get('membershipId')
  const [selectedMembershipId, setSelectedMembershipId] = useState(membershipId)
  const currentPage = parseInt(searchParams.get('page') || '1', 10)
  const pageSize = 10
  const mountedRef = useMounted(true)

  const [loading, setLoading] = useState(false)
  const [payments, setPayments] = useState([])
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize,
    totalCount: 0,
    totalPages: 1,
  })
  const [loadingError, setLoadingError] = useState(false)
  const user = useStore((state) => state.auth.user)

  const fetchPaymentHistory = async (page = 1) => {
    setLoading(true)

    try {
      const params = {
        page,
        pageSize,
        membershipId: selectedMembershipId || undefined,
      }

      const result = await getPaymentHistoryApi(params)

      if (mountedRef.current) {
        setPayments(result.data.data.data)
        setPagination(result.data.data.pagination)
        setLoading(false)
      }
    } catch (e) {
      if (mountedRef.current) {
        setLoading(false)
        setLoadingError(true)
        console.error('Error fetching payment history:', e)
      }
    }
  }

  // Update state when URL parameter changes
  useEffect(() => {
    setSelectedMembershipId(membershipId)
  }, [membershipId])

  // Fetch payment history when state changes
  useEffect(() => {
    fetchPaymentHistory(currentPage)
  }, [currentPage, selectedMembershipId])

  const handleDownloadInvoice = (receiptUrl: string) => {
    if (!receiptUrl) return

    const urlObj = new URL(receiptUrl)
    urlObj.pathname = urlObj.pathname + '/pdf'
    saveAs(urlObj.href)
  }

  const retryRequest = () => {
    setLoadingError(false)
    fetchPaymentHistory(currentPage)
  }

  const handlePageChange = (page: number) => {
    // Update URL with the new page number
    const params = new URLSearchParams(searchParams.toString())
    params.set('page', page.toString())
    router.push(`/settings/account/payment-history?${params.toString()}`)
  }

  if (loadingError) {
    return <LoadingErrorMessage retryRequest={retryRequest} />
  }

  // Generate pagination items
  const renderPaginationItems = () => {
    const items = []
    const maxVisiblePages = 5

    // Always show first page
    items.push(
      <PaginationItem key="page-1">
        <PaginationLink page={1} isActive={pagination.page === 1} onClick={handlePageChange} />
      </PaginationItem>,
    )

    // Show ellipsis if needed
    if (pagination.page > 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>,
      )
    }

    // Show pages around current page
    const startPage = Math.max(2, pagination.page - 1)
    const endPage = Math.min(pagination.totalPages - 1, pagination.page + 1)

    for (let i = startPage; i <= endPage; i++) {
      if (i > 1 && i < pagination.totalPages) {
        items.push(
          <PaginationItem key={`page-${i}`}>
            <PaginationLink page={i} isActive={pagination.page === i} onClick={handlePageChange} />
          </PaginationItem>,
        )
      }
    }

    // Show ellipsis if needed
    if (pagination.page < pagination.totalPages - 2) {
      items.push(
        <PaginationItem key="ellipsis-2">
          <PaginationEllipsis />
        </PaginationItem>,
      )
    }

    // Always show last page if there is more than one page
    if (pagination.totalPages > 1) {
      items.push(
        <PaginationItem key={`page-${pagination.totalPages}`}>
          <PaginationLink
            page={pagination.totalPages}
            isActive={pagination.page === pagination.totalPages}
            onClick={handlePageChange}
          />
        </PaginationItem>,
      )
    }

    return items
  }

  return (
    <div>
      <div className="account-settings tailwind-component page-inner-pb">
        <div className="rounded-box w-full max-w-screen-md p-5">
          <div className={'mb-4 flex items-center justify-between'}>
            <h2 className="-mt-1 grow text-lg font-semibold">Payment History</h2>
            {user?.user_memberships?.length > 0 && (
              <div className="w-64">
                <Select
                  value={selectedMembershipId || 'all'}
                  onValueChange={(value) => {
                    // Update the state
                    setSelectedMembershipId(value !== 'all' ? value : null)

                    // Update the URL
                    const params = new URLSearchParams(searchParams.toString())
                    if (value && value !== 'all') {
                      params.set('membershipId', value)
                    } else {
                      params.delete('membershipId')
                    }
                    params.set('page', '1')
                    router.push(`/settings/account/payment-history?${params.toString()}`)
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by community" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All payments</SelectItem>
                    {user.user_memberships.map((um) => (
                      <SelectItem key={um.membership.id} value={um.membership.id}>
                        {um.membership.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Description</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Receipt</TableHead>
              </TableRow>
            </TableHeader>
            {!loading && payments.length > 0 ? (
              <TableBody>
                {payments.map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell className="text-black-700 dark:text-white-500">
                      {payment.payment_type === 'membership' && payment.membership ? (
                        <>
                          Payment for membership <span className="font-bold">{payment.membership.name}</span>
                        </>
                      ) : payment.payment_type === 'memberup' ? (
                        <>
                          Payment for <span className="font-bold">MemberUp</span> / {payment.membership?.name}{' '}
                          {payment.invoice?.discount?.coupon?.name && (
                            <span className="font-bold">({payment.invoice?.discount?.coupon?.name})</span>
                          )}
                        </>
                      ) : (
                        payment.description
                      )}
                    </TableCell>
                    <TableCell className="text-black-200 dark:text-black-100">
                      {payment.payment_date
                        ? formatDate({
                            date: new Date(payment.payment_date),
                            format: 'LLLL dd, yyyy',
                          })
                        : 'N/A'}
                    </TableCell>
                    <TableCell className="font-semibold text-black-700 dark:text-white-500">
                      {numberToCurrency(payment.amount)} {payment.currency.toUpperCase()}
                    </TableCell>
                    <TableCell className="text-center sm:text-left">
                      {payment.stripe_receipt_url ? (
                        <Button
                          variant="inline"
                          className="text-primary-100"
                          onClick={() => handleDownloadInvoice(payment.stripe_receipt_url)}
                        >
                          <Download20Icon className="sm:hidden" />
                          <span className="hidden sm:inline">Download</span>
                        </Button>
                      ) : (
                        <span className="text-black-200 dark:text-black-100">N/A</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            ) : null}
          </Table>
          {!loading && payments.length === 0 && (
            <p className="pl-6 pt-6 text-sm text-black-600 dark:text-black-100">You haven't made any payments yet.</p>
          )}
          {loading && (
            <div className="mt-4 space-y-2">
              <SkeletonBox className="h-10" />
              <SkeletonBox className="h-10" />
              <SkeletonBox className="h-10" />
            </div>
          )}
          {pagination.totalPages > 1 && (
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    disabled={loading || pagination.page === 1}
                    onClick={() => handlePageChange(pagination.page - 1)}
                  />
                </PaginationItem>

                {renderPaginationItems()}

                <PaginationItem>
                  <PaginationNext
                    disabled={loading || pagination.page >= pagination.totalPages}
                    onClick={() => handlePageChange(pagination.page + 1)}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
        </div>
      </div>
    </div>
  )
}
