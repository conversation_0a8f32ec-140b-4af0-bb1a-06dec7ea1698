'use server'

import { headers } from 'next/headers'
import { notFound, redirect } from 'next/navigation'

import { auth } from '@/auth'
import AppLayout from '@/components/layout/AppLayout'
import { StreamChatProvider } from '@/components/providers/StreamChatProvider'
import { canAccessCommunityPath } from '@/lib/authorization'
import { nonCommunityPathnames } from '@/lib/constants'
import { getCachedAuthenticatedUserData } from '@/lib/server-components/users'
import { getCachedCommunityData } from '@/lib/server/communities'

export default async function AppLayoutWrapper({ children }: { children: React.ReactNode }) {
  const requestHeaders = await headers()
  const communitySlug = requestHeaders.get('x-community-slug')
  const pathname = requestHeaders.get('x-pathname')

  const pathnameParts = pathname.split('/')

  if (!pathname.startsWith('/@') && !nonCommunityPathnames.includes(`/${pathnameParts[1]}`)) {
    const membershipData = await getCachedCommunityData(communitySlug)
    if (!membershipData) {
      notFound()
    }

    const session = await auth()
    let userData = null

    if (session?.user?.id) {
      userData = await getCachedAuthenticatedUserData(session.user.id)
    }

    if (!canAccessCommunityPath(userData?.user, membershipData, pathname)) {
      redirect(`/${communitySlug}/about`)
    }
  }

  return (
    <StreamChatProvider>
      <AppLayout>{children}</AppLayout>
    </StreamChatProvider>
  )
}
