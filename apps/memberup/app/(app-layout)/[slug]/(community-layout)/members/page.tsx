'use client'

import { useSearchParams } from 'next/navigation'
import React, { useEffect, useState } from 'react'

import { USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { CommunityDetails } from '@/components/community/community-details'
import { InviteSettings } from '@/components/settings/member-settings/invite-settings'
import { MembershipSettings } from '@/components/settings/member-settings/membership-settings'
import { NotificationsSettings } from '@/components/settings/member-settings/notification-settings'
import { Button, SkeletonBox } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { toast } from '@/components/ui/sonner'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useMemberRequests } from '@/hooks/useMemberRequests'
import { useStore } from '@/hooks/useStore'
import { unexpectedError } from '@/lib/error-messages'
import { formatThousands } from '@/lib/formatting'
import { checkAdminOrCreatorRole } from '@/shared-libs/profile'
import { approveAllMembersRequestsApi } from '@/shared-services/apis/membership.api'
import { deleteUserApi } from '@/shared-services/apis/user.api'
import useCheckUserRole from '@/src/components/hooks/use-check-user-role'
import MembersListing from '@/src/components/member/members-listing-new'
import MembersRequests from '@/src/components/member/members-requests'
import { getMembers, selectMembersMap } from '@/src/store/features/memberSlice'
import { openDialog } from '@/src/store/features/uiSlice'
import { useAppDispatch, useAppSelector } from '@/src/store/hooks'

const DEFAULT_TAB = 'members'

export default function MembersPage() {
  const searchParams = useSearchParams()
  const tab = searchParams.get('tab')
  const dispatch = useAppDispatch()
  const membersMap = useAppSelector((state) => selectMembersMap(state))
  const { isCurrentUserAdmin } = useCheckUserRole()
  const [currentTab, setCurrentTab] = useState(DEFAULT_TAB)
  const [openConfirm, setOpenConfirm] = useState(false)
  const membership = useStore((state) => state.community.membership)
  const { memberRequests, loading: loadingMembersRequests, fetchMemberRequests } = useMemberRequests()

  const [selectedUserId, setSelectedUserId] = useState(null)
  const [requestDeleteMember, setRequestDeleteMember] = useState(false)

  useEffect(() => {
    if (!membership) {
      return
    }

    if (tab && typeof tab === 'string') {
      setCurrentTab(tab)
    } else {
      setCurrentTab(DEFAULT_TAB)
    }
    dispatch(
      getMembers({
        membershipId: membership?.id,
        where: JSON.stringify({
          status: USER_STATUS_ENUM.active,
        }),
        take: 10000,
        skip: 0,
      }),
    )
  }, [membership])

  const allUsers = Object.values(membersMap)
  const admins = allUsers.filter((m: any) => checkAdminOrCreatorRole(m?.role))
  const members = allUsers.filter((m: any) => !checkAdminOrCreatorRole(m?.role))
  const onlineMembers = allUsers.filter((m: any) => m.isOnline)

  const membersCount = members.length
  const adminsCount = admins.length
  const onlineCount = onlineMembers.length
  const requestsCount = memberRequests.length

  const showInviteButton = currentTab !== 'requests' && isCurrentUserAdmin
  const showApproveAll = currentTab === 'requests' && isCurrentUserAdmin
  const isApproveAllDisabled = requestsCount === 0

  const handleInviteMember = () => {
    const config = {
      sections: [
        {
          name: 'membership',
          title: 'Membership',
          component: MembershipSettings,
        },
        {
          name: 'notifications',
          title: 'Notifications',
          component: NotificationsSettings,
        },
        {
          name: 'invite',
          title: 'Invite',
          component: InviteSettings,
        },
      ],
    }
    dispatch(openDialog({ dialog: 'MemberSettings', open: true, props: { defaultSection: 'invite' } }))
  }

  const handleApproveAll = async () => {
    setOpenConfirm(true)
  }

  const handleApproveAllConfirm = async () => {
    try {
      await approveAllMembersRequestsApi(membership.id)
      await fetchMemberRequests()
      setOpenConfirm(false)
    } catch (error) {
      toast.error(unexpectedError)
    }
  }

  const handleApproveAllClose = async () => {
    setOpenConfirm(false)
  }

  const handleOnDeleteMemberClick = (id) => {
    setSelectedUserId(id)
  }

  const handleOnDeleteConfirm = async () => {
    setRequestDeleteMember(true)
    try {
      const result = await deleteUserApi(membership.id, selectedUserId, false)
      if (result.data.success) {
        toast.success('Member removed successfully.')
        dispatch(
          getMembers({
            membershipId: membership.id,
            where: JSON.stringify({
              status: USER_STATUS_ENUM.active,
            }),
            take: 10000,
            skip: 0,
          }),
        )
      }
    } catch (error) {
      toast.error(unexpectedError)
    } finally {
      setRequestDeleteMember(false)
      setSelectedUserId(null)
    }
  }

  const tabs = [
    {
      value: 'members',
      label: `Members (${formatThousands(membersCount)})`,
      showAlways: true,
      content: <MembersListing members={members} onDeleteMemberClick={handleOnDeleteMemberClick} />,
    },
    {
      value: 'admins',
      label: `Admins (${formatThousands(adminsCount)})`,
      showAlways: true,
      content: <MembersListing members={admins} onDeleteMemberClick={handleOnDeleteMemberClick} />,
    },
    {
      value: 'online',
      label: `Online (${formatThousands(onlineCount)})`,
      showAlways: true,
      content: <MembersListing members={onlineMembers} onDeleteMemberClick={handleOnDeleteMemberClick} />,
    },
    {
      value: 'requests',
      label: `Requests (${formatThousands(requestsCount)})`,
      showAlways: false,
      content: <MembersRequests />,
    },
  ]

  return (
    <div className="space-y:6 page-inner-pb mobile-padded-content-container flex flex-col-reverse items-start md:flex-row md:space-x-6 md:space-y-0">
      <ConfirmModal
        title="Are you sure you want to remove the member?"
        onConfirm={handleOnDeleteConfirm}
        loading={requestDeleteMember}
        open={selectedUserId}
        onCancel={() => setSelectedUserId(null)}
      />
      <div className="flex-grow">
        <div>
          <div className="text-white font-['Graphik'] text-lg font-semibold leading-normal">Members</div>
          <div className="mt-5">
            <div className="flex-grow">
              <Tabs defaultValue={DEFAULT_TAB} value={currentTab} onValueChange={(value) => setCurrentTab(value)}>
                <div className="flex items-center">
                  <div className="grow">
                    <TabsList>
                      {tabs.map((tab) =>
                        tab.showAlways || isCurrentUserAdmin ? (
                          <TabsTrigger key={tab.value} value={tab.value}>
                            {tab.label}
                          </TabsTrigger>
                        ) : null,
                      )}
                    </TabsList>
                  </div>
                  <div>
                    {showInviteButton && (
                      <Button
                        className="w-[69px]"
                        type="submit"
                        variant="default"
                        size="sm"
                        onClick={handleInviteMember}
                        data-cy="invite-button"
                      >
                        Invite
                      </Button>
                    )}
                    {showApproveAll && (
                      <Button
                        type="submit"
                        size="sm"
                        variant="default"
                        disabled={isApproveAllDisabled || loadingMembersRequests}
                        onClick={handleApproveAll}
                        data-cy="approval-all-button"
                      >
                        Approve All
                      </Button>
                    )}
                  </div>
                </div>
                {tabs.map((tab) => (
                  <TabsContent key={tab.value} value={tab.value}>
                    {tab.content}
                  </TabsContent>
                ))}
              </Tabs>
            </div>
          </div>
        </div>
      </div>
      <CommunityDetails />

      <ConfirmModal
        title="Please confirm"
        open={openConfirm}
        onCancel={() => handleApproveAllClose()}
        onConfirm={() => handleApproveAllConfirm()}
      >
        Are you sure you want to approve all members requests?
      </ConfirmModal>
    </div>
  )
}
