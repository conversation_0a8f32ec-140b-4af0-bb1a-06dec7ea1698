'use client'

import AddIcon from '@mui/icons-material/Add'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import _groupBy from 'lodash/groupBy'
import { useRouter } from 'next/navigation'
import { useEffect, useMemo, useState } from 'react'

import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { EVENT_STATUS_ENUM, EVENT_VIEW_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { IEvent } from '@memberup/shared/src/types/interfaces'
import { CommunityDetails } from '@/components/community/community-details'
import { Button } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import LoadingSpinner from '@/memberup/components/common/loaders/loading-spinner'
import EditEvent from '@/memberup/components/dialogs/events/edit-event'
import EventCard from '@/memberup/components/events/event-card'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import { getCommunityBaseURL } from '@/memberup/libs/utils'
import { getEventsApi } from '@/shared-services/apis/event.api'

export const selectEvents = (events, type: string, nowDate: Date) => {
  const now = Math.floor(nowDate.getTime() / 1000)
  if (type === 'now') {
    return events.filter((item) => {
      if (now < item.start_time || now > item.end_time) return false
      const eventDate = new Date(item.start_time * 1000)
      return (
        nowDate.getFullYear() === eventDate.getFullYear() &&
        nowDate.getMonth() === eventDate.getMonth() &&
        nowDate.getDate() === eventDate.getDate()
      )
    })
  }

  let docs = events.map((item) => {
    const eventDate = new Date(item.start_time * 1000)
    return {
      ...item,
      year: eventDate.getFullYear(),
      month: eventDate.getMonth() + 1,
    }
  })

  docs = type === EVENT_VIEW_TYPE_ENUM.upcoming ? docs.filter((item) => item.start_time > now) : docs
  const groupedDocs = _groupBy(docs, (item) => `${item.year}+${item.month < 10 ? '0' : ''}${item.month}`)
  return groupedDocs
}

export default function EventsPage() {
  const [requestEvents, setRequestEvents] = useState(false)
  const router = useRouter()
  const { isCurrentUserAdmin } = useCheckUserRole()
  const [date, setDate] = useState<Date | null>(new Date())
  const membership = useStore((state) => state.community.membership)
  const [eventViewType, setEventViewType] = useState<EVENT_VIEW_TYPE_ENUM>(EVENT_VIEW_TYPE_ENUM.upcoming)
  const [allEvents, setAllEvents] = useState([])
  const [selectedEvent, setSelectedEvent] = useState(null)
  const [createOrEditEvent, setCreateOrEditEvent] = useState(false)

  async function loadEvents() {
    try {
      setRequestEvents(true)
      let where = {}
      const now = Math.floor(new Date().getTime() / 1000)

      if (eventViewType === EVENT_VIEW_TYPE_ENUM.past) {
        where = {
          start_time: { lt: now },
          end_time: { lt: now },
          status: EVENT_STATUS_ENUM.published,
        }
      } else if (eventViewType === EVENT_VIEW_TYPE_ENUM.upcoming) {
        where = {
          OR: [{ start_time: { gte: now } }, { end_time: { gte: now } }],
          status: EVENT_STATUS_ENUM.published,
        }
      } else if (eventViewType === EVENT_VIEW_TYPE_ENUM.draft) {
        where = {
          status: EVENT_STATUS_ENUM.drafts,
        }
      }
      const res = await getEventsApi(membership.id, {
        where: JSON.stringify(where),
        orderBy: JSON.stringify([{ start_time: 'asc' }, { end_time: 'asc' }]),
      })
      console.log('RES', res.data.data.docs)
      setAllEvents(res.data.data.docs)
    } catch (e) {
      toast.error(e.message)
    } finally {
      setRequestEvents(false)
    }
  }

  const nowEvents = selectEvents(allEvents, 'now', date) || {}
  const events = selectEvents(allEvents, eventViewType, date) || {}

  const handleCreateEventOnClose = () => {
    setCreateOrEditEvent(false)
    setSelectedEvent(null)
    loadEvents()
  }

  const handleOnEditEventClick = (event: IEvent) => {
    setSelectedEvent(event)
    setCreateOrEditEvent(true)
  }

  const handleOnDeleteSuccess = () => {
    loadEvents()
  }

  const eventTypes = isCurrentUserAdmin
    ? [EVENT_VIEW_TYPE_ENUM.upcoming, EVENT_VIEW_TYPE_ENUM.draft, EVENT_VIEW_TYPE_ENUM.past]
    : [EVENT_VIEW_TYPE_ENUM.upcoming, EVENT_VIEW_TYPE_ENUM.past]

  useEffect(() => {
    if (!membership) return
    loadEvents()
  }, [eventViewType, membership])

  const renderHappeningNow = useMemo(() => {
    const temp = new Date()
    temp.setHours(0, 0, 0)
    const startTime = temp.getTime()
    temp.setHours(23, 59, 59)
    const endTime = temp.getTime()
    const time = date.getTime()
    if (time >= startTime && time <= endTime) {
      return 'Happening Now'
    }
    return `Happening on ${formatDate({ date: time, format: 'MMM dd' })}`
  }, [date])

  const renderEvents = useMemo(() => {
    if (!Object.keys(events).length) return null
    const keys = Object.keys(events).sort()
    return (
      <>
        {keys.map((key) => (
          <div key={key} className="events-month">
            <div className={'space-y-4 text-xl font-semibold'}>
              {formatDate({
                date: events[key][0].start_time * 1000,
                format: 'MMMM yyyy',
              })}
            </div>

            <div className={'flex-col space-y-2'}>
              {events[key].map((item) => (
                <EventCard
                  appEvent={item}
                  editable={true}
                  eventViewType={eventViewType}
                  onClick={() => router.push(`${getCommunityBaseURL(membership)}/events/${item.id}`)}
                  onEditEventClick={handleOnEditEventClick}
                  onDeleteSuccess={handleOnDeleteSuccess}
                  useLocalTimeZone={true}
                />
              ))}
            </div>
          </div>
        ))}
      </>
    )
  }, [events])

  return (
    <div className="space-y:6 page-inner-pb flex flex-col-reverse items-start md:flex-row md:space-x-6 md:space-y-0">
      {requestEvents && <LoadingSpinner />}
      <div>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          {/* Page header */}
          <div className={'mb-4 flex flex-row'}>
            <div className={'flex flex-grow items-center gap-4'}>
              Events{' '}
              {isCurrentUserAdmin && (
                <Button variant="community-primary" disabled={requestEvents} onClick={() => setCreateOrEditEvent(true)}>
                  {' '}
                  <AddIcon />
                </Button>
              )}
            </div>
            <div className={'flex flex-row gap-1'}>
              {eventTypes.map((et) => (
                <Button key={et} onClick={() => setEventViewType(et)}>
                  {et}
                </Button>
              ))}
            </div>
            {!requestEvents && Object.keys(nowEvents).length === 0 && Object.keys(events).length === 0 && (
              <div>There isn&apos;t any event.</div>
            )}
          </div>
          <div className={'flex flex-col gap-4'}>
            {false && renderHappeningNow}
            {(nowEvents as IEvent[]).map((event) => (
              <EventCard
                key={event.id}
                appEvent={event}
                eventViewType="now"
                editable={true}
                onEditEventClick={handleOnEditEventClick}
                onClick={() => router.push(`${getCommunityBaseURL(membership)}/events/${event.id}`)}
                useLocalTimeZone={true}
              />
            ))}
            {renderEvents}
          </div>
        </LocalizationProvider>

        <EditEvent data={selectedEvent} open={createOrEditEvent} onClose={() => handleCreateEventOnClose()} />
      </div>
      <CommunityDetails />
    </div>
  )
}
