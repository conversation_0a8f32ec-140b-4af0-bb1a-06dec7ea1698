'use client'

import AccessTimeIcon from '@mui/icons-material/AccessTime'
import ArrowB<PERSON>OutlinedIcon from '@mui/icons-material/ArrowBackOutlined'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp'
import PersonIcon from '@mui/icons-material/Person'
import VideocamIcon from '@mui/icons-material/Videocam'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import ListItemText from '@mui/material/ListItemText'
import Menu from '@mui/material/Menu'
import MenuItem from '@mui/material/MenuItem'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import { GoogleMap } from '@react-google-maps/api'
import { saveAs } from 'file-saver'
import moment from 'moment-timezone'
import { useParams, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { formatDate, getDateTime } from '@memberup/shared/src/libs/date-utils'
import { deleteEventAttendApi, getEventApi } from '@memberup/shared/src/services/apis/event.api'
import { EVENT_LOCATION_TYPES } from '@memberup/shared/src/types/consts'
import { EVENT_LOCATION_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { IEvent } from '@memberup/shared/src/types/interfaces'
import { useStore } from '@/hooks/useStore'
import LoadingSpinner from '@/memberup/components/common/loaders/loading-spinner'
import AttendEventDialog from '@/memberup/components/dialogs/events/attend-event'
import useGoogleMapApi from '@/memberup/components/hooks/use-googlemap-api'
import NormalLayout from '@/memberup/layouts/normal-layout'
import { getCommunityBaseURL } from '@/memberup/libs/utils'
import { selectMembersMap } from '@/memberup/store/features/memberSlice'
import { selectUser } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const ics = require('ics')

export default function EventPage() {
  const theme = useTheme()
  const mountedRef = useMounted(true)
  const { isLoaded: isLoadedGoogleMap } = useGoogleMapApi()
  const user = useAppSelector((state) => selectUser(state))
  const membership = useStore((state) => state.community.membership)
  const router = useRouter()
  const params = useParams()
  const id = params.id
  const [appEvent, setAppEvent] = useState<IEvent>(null)
  const [requestEvent, setRequestEvent] = useState(true)
  const [requestAttend, setRequestAttend] = useState(false)
  const [openAttendEvent, setOpenAttendEvent] = useState(false)
  const [attendingAnchorEl, setAttendingAnchorEl] = useState(null)
  const [addCalendarAnchorEl, setAddCalendarAnchorEl] = useState(null)
  const localTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone
  const eventTimeZone = appEvent ? appEvent?.time_zone : 'America/Los_Angeles'
  const eventStartDateTime = (appEvent ? new Date(appEvent.start_time * 1000) : new Date()).getTime()
  const eventEndDateTime = (appEvent ? new Date(appEvent.end_time * 1000) : new Date()).getTime()
  const isEventCreator = appEvent?.created_by === user.id
  const attended = appEvent?.attendees?.find((a) => a.user_id === user.id)
  const eventLocationType =
    EVENT_LOCATION_TYPES.find((l) => l.name === appEvent?.location_type) || EVENT_LOCATION_TYPES[0]
  const noIconEvent =
    eventLocationType.name === EVENT_LOCATION_TYPE_ENUM.tbd ||
    eventLocationType.name === EVENT_LOCATION_TYPE_ENUM.content_release
  const members = useAppSelector((state) => selectMembersMap(state))

  useEffect(() => {
    if (!mountedRef.current) return
    getEventApi(id as string)
      .then((res) => {
        if (!mountedRef.current) return
        res.data.data.attendees = res.data.data.attendees.filter((item) => item.user?.status === 'active')
        setAppEvent(res.data.data)
      })
      .catch((err) => {})
      .finally(() => {
        if (!mountedRef.current) return
        setRequestEvent(false)
      })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id])
  console.log('membersMap', members)
  const handleNotAttend = () => {
    if (!attended) return
    setRequestAttend(true)
    deleteEventAttendApi(attended.id as string)
      .then((res) => {
        if (!mountedRef.current) return
        setAppEvent({
          ...appEvent,
          attendees: appEvent.attendees.filter((a) => a.id !== attended.id),
        })
      })
      .catch((err) => {})
      .finally(() => {
        if (!mountedRef.current) return
        setRequestAttend(false)
      })
  }

  const handleClickCalendarItem = (e, item: string) => {
    e.stopPropagation()
    e.preventDefault()
    setAddCalendarAnchorEl(null)

    if (item === 'Google') {
      const startTime = new Date(appEvent.start_time * 1000).toUTCString()
      const endTime = new Date(appEvent.end_time * 1000).toUTCString()
      let calendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${
        appEvent.title
      }&details=${appEvent.description}&dates=${formatDate({
        date: startTime,
        format: 'yyyyMMdd',
      })}T${formatDate({
        date: startTime,
        format: 'HHmmss',
      })}/${formatDate({
        date: endTime,
        format: 'yyyyMMdd',
      })}T${formatDate({
        date: endTime,
        format: 'HHmmss',
      })}`

      if (!noIconEvent) {
        const location = appEvent.location_address || appEvent.location_url
        if (location) {
          calendarUrl += `&location=${location}`
        }
      }

      // if (appEvent.time_zone) {
      //   calendarUrl += `&ctz=${appEvent.time_zone}`
      // }
      calendarUrl += `&ctz=${localTimeZone}`
      window.open(calendarUrl, '_blank')
      return
    } else if (item === 'Outlook') {
      const startTime = new Date(appEvent.start_time * 1000).toUTCString()
      const endTime = new Date(appEvent.end_time * 1000).toUTCString()
      let calendarUrl = `https://outlook.live.com/calendar/0/action/compose?subject=${
        appEvent.title
      }&body=${appEvent.description}&startdt=${formatDate({
        date: startTime,
        format: 'yyyy-MM-dd',
      })}T${formatDate({
        date: startTime,
        format: 'HH:mm:ss',
      })}&enddt=${formatDate({
        date: endTime,
        format: 'yyyy-MM-dd',
      })}T${formatDate({
        date: endTime,
        format: 'HH:mm:ss',
      })}&path=%2Fcalendar%2Faction%2Fcompose&rru=addevent`

      if (!noIconEvent) {
        const location = appEvent.location_address || appEvent.location_url
        if (location) {
          calendarUrl += `&location=${location}`
        }
      }

      window.open(calendarUrl, '_blank')
      return
    }

    const localDateTime = getDateTime({
      timeZone: localTimeZone,
      dateTime: appEvent.start_time * 1000,
    }) as Date
    const localYear = localDateTime.getFullYear()
    const localMonth = localDateTime.getMonth() + 1
    const localDate = localDateTime.getDate()
    const localHours = localDateTime.getHours()
    const localMinutes = localDateTime.getMinutes()
    const durationSeconds = appEvent.end_time - appEvent.start_time
    const durationDays = Math.trunc(durationSeconds / 3600 / 24)
    const durationHours = Math.trunc((durationSeconds - durationDays * 3600 * 24) / 3600)
    const durationMinutes = Math.trunc((durationSeconds - durationDays * 3600 * 24 - durationHours * 3600) / 60)

    const tempEvent = {
      start: [localYear, localMonth, localDate, localHours, localMinutes],
      duration: {},
      title: appEvent.title,
      description: appEvent.description,
      // categories: ['10k races', 'Memorial Day Weekend', 'Boulder CO'],
      status: 'CONFIRMED',
      // busyStatus: 'BUSY',
      // organizer: { name: 'Admin', email: '<EMAIL>' },
      // attendees: (appEvent.attendees || []).map((item) => ({
      //   name: getFullName(item.user?.first_name, item.user?.last_name),
      //   email: item.user?.email,
      // })),
    }

    if (durationDays) {
      tempEvent.duration['days'] = durationDays
    }

    if (durationHours) {
      tempEvent.duration['hours'] = durationHours
    }

    if (durationMinutes) {
      tempEvent.duration['minutes'] = durationMinutes
    }

    if (appEvent.location_map_center?.lat) {
      tempEvent['geo'] = {
        lat: appEvent.location_map_center.lat,
        lon: appEvent.location_map_center.lng,
      }
    }

    // if (appEvent.location_url) {
    //   tempEvent['url'] = appEvent.location_url
    // }

    if (appEvent.location_address || appEvent.location_url) {
      tempEvent['location'] = appEvent.location_address || appEvent.location_url
    }

    ics.createEvent(tempEvent, (error, value) => {
      if (error) {
        console.log('event error ======', error)
        return
      } else {
        const blob = new Blob([value], { type: 'text/plain;charset=utf-8' })
        saveAs(blob, `${appEvent.title}.ics`)
      }
    })
  }

  const outputTimeDay = moment(eventStartDateTime).tz(localTimeZone).format('ddd, DD MMMM')
  const eventStartDateTimeStr = moment(eventStartDateTime).tz(localTimeZone).format('h:mm a')
  const eventEndDateTimeStr = moment(eventEndDateTime).tz(localTimeZone).format('h:mm A [GMT]Z z')
  let formattedEventEndDateTimeStr = eventEndDateTimeStr.replace(/GMT([+-]\d+):00/g, 'GMT$1')
  formattedEventEndDateTimeStr = formattedEventEndDateTimeStr.replace(
    /(GMT[+-]\d{1,2}) ([+-]\d{1,2})$/,
    (match, p1, p2) => {
      // Check if p2 is a repetition of the offset in p1 without the GMT part, it shouldn't affer CDT or CST known timezone letter abbreviations
      if (p1.includes(p2)) {
        return p1 // If it's a repeat, use just the first part
      }
      return match // Otherwise, return the original match
    },
  )
  return (
    <NormalLayout
      maxWidth={1416}
      visibleHeader={true}
      visibleProfileMenu={true}
      leftHeader={
        <Button
          variant="text"
          className="no-padding"
          color="inherit"
          startIcon={<ArrowBackOutlinedIcon />}
          onClick={() => router.replace(`${getCommunityBaseURL(membership)}/events`)}
        >
          Back to Events
        </Button>
      }
    >
      {requestEvent ? (
        <Box sx={{ minHeight: '640px' }}>
          <LoadingSpinner />
        </Box>
      ) : (
        <Box className="background-color08" sx={{ maxWidth: '756px', width: '100%', margin: 'auto' }}>
          {Boolean(appEvent) ? (
            <Grid container>
              {Boolean(appEvent.header_image) && (
                <Grid item xs={12}>
                  <Box
                    className="app-image-wrapper background-gradient01"
                    sx={{
                      borderTopLeftRadius: '12px',
                      borderTopRightRadius: '12px',
                      height: { xs: 250, sm: 420 },
                      overflow: 'hidden',
                    }}
                  >
                    <AppImg src={appEvent.header_image} alt="Event Header Image" fill style={{ objectFit: 'cover' }} />
                  </Box>
                </Grid>
              )}
              <Grid item xs={12}>
                <Box sx={{ padding: '48px 32px' }}>
                  <Grid container spacing={4}>
                    <Grid item xs={12}>
                      <Grid container spacing={4}>
                        <Grid item xs={12}>
                          <Box
                            sx={{
                              display: 'inline-flex',
                              paddingLeft: '6px',
                              paddingRight: '8px',
                              borderRadius: 4,
                              backgroundColor: theme.palette.divider,
                              lineHeight: 1,
                            }}
                          >
                            <Grid
                              container
                              spacing={1}
                              alignItems="center"
                              sx={{ color: theme.palette.text.disabled, minHeight: 24 }}
                            >
                              {eventLocationType.name === EVENT_LOCATION_TYPE_ENUM.in_person && (
                                <Grid item>
                                  <PersonIcon color="inherit" />
                                </Grid>
                              )}
                              {eventLocationType.name === EVENT_LOCATION_TYPE_ENUM.url && (
                                <Grid item>
                                  <VideocamIcon color="inherit" />
                                </Grid>
                              )}
                              <Grid item xs>
                                <Typography
                                  color="inherit"
                                  variant="body2"
                                  sx={{
                                    ml: noIconEvent ? '2px' : 0,
                                  }}
                                >
                                  {eventLocationType.displayName}
                                </Typography>
                              </Grid>
                            </Grid>
                          </Box>
                        </Grid>
                        <Grid item xs={12}>
                          <Typography variant="h4" gutterBottom data-testid="event-title">
                            {appEvent.title || ''}
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <Box>
                            <Grid container alignItems="center" spacing={2} sx={{ color: theme.palette.text.disabled }}>
                              <Grid className="d-flex algin-center width-auto" item>
                                <AccessTimeIcon color="inherit" />
                              </Grid>
                              <Grid item>
                                <Typography variant="h6" data-testid="event-date-time">
                                  {outputTimeDay}
                                  {/* {formatInTimeZone(eventStartDateTime, localTimeZone, 'E, d LLLL')} */}
                                  &nbsp;&nbsp;&nbsp;•&nbsp;&nbsp;&nbsp;
                                  {/*  {formatInTimeZone(eventStartDateTime, localTimeZone, 'h:mm a')} */}
                                  {eventStartDateTimeStr}
                                  &nbsp;To&nbsp;
                                  {/* {formatInTimeZone(eventEndDateTime, localTimeZone, 'h:mm a z')} */}
                                  {formattedEventEndDateTimeStr}
                                </Typography>
                              </Grid>
                            </Grid>
                          </Box>
                        </Grid>
                        <Grid item xs={6}>
                          <Grid container alignItems="center" spacing={3}>
                            <Grid item>
                              {!attended ? (
                                <Button
                                  className="round-small"
                                  disabled={Date.now() > eventEndDateTime}
                                  variant="contained"
                                  sx={{
                                    width: 98,
                                    backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
                                  }}
                                  onClick={() => setOpenAttendEvent(true)}
                                >
                                  RSVP
                                </Button>
                              ) : (
                                <Button
                                  sx={{
                                    backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
                                  }}
                                  className="round-small"
                                  variant="contained"
                                  endIcon={attendingAnchorEl ? <ArrowDropUpIcon /> : <ArrowDropDownIcon />}
                                  onClick={(e) => setAttendingAnchorEl(e.currentTarget)}
                                >
                                  Attending
                                </Button>
                              )}
                            </Grid>
                            <Grid item xs>
                              <Button
                                className="round-small no-padding"
                                onClick={(e) => {
                                  setAddCalendarAnchorEl(e.currentTarget)
                                }}
                              >
                                Add to Calendar
                              </Button>
                            </Grid>
                          </Grid>
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid item xs={12}>
                      <Divider />
                    </Grid>
                    <Grid item xs={12}>
                      <Grid container spacing={4}>
                        <Grid item xs={12}>
                          <Typography variant="h6">Event Details</Typography>
                          <Typography
                            color="text.disabled"
                            variant="body1"
                            gutterBottom
                            sx={{ lineHeight: '20px', marginTop: 3 }}
                            data-testid="event-description"
                          >
                            {appEvent.description}
                          </Typography>
                          {Boolean(appEvent?.location_type) && (
                            <Box sx={{ mt: 3 }}>
                              <Typography variant="subtitle1" gutterBottom>
                                Location
                              </Typography>
                              {appEvent.location_type === EVENT_LOCATION_TYPE_ENUM.in_person && (
                                <>
                                  <Typography color="text.disabled" variant="body1">
                                    {appEvent.location_address}
                                  </Typography>
                                  {appEvent.location_map_center && isLoadedGoogleMap && (
                                    <GoogleMap
                                      mapContainerStyle={{
                                        width: '100%',
                                        height: 400,
                                        marginTop: 10,
                                      }}
                                      center={appEvent.location_map_center}
                                      zoom={appEvent?.location_zoom}
                                    >
                                      {/* Child components, such as markers, info windows, etc. */}
                                      <></>
                                    </GoogleMap>
                                  )}
                                </>
                              )}
                              {appEvent.location_type === EVENT_LOCATION_TYPE_ENUM.url &&
                                Boolean(appEvent.location_url) && (
                                  <Typography variant="body1">
                                    <a
                                      style={{
                                        textDecoration: 'underline',
                                        wordBreak: 'break-all',
                                      }}
                                      href={appEvent.location_url}
                                    >
                                      {appEvent.location_url}
                                    </a>
                                  </Typography>
                                )}
                              {appEvent.location_type === EVENT_LOCATION_TYPE_ENUM.content_release && (
                                <Typography color="text.disabled" variant="body1">
                                  Content Release
                                </Typography>
                              )}
                              {appEvent.location_type === EVENT_LOCATION_TYPE_ENUM.tbd && (
                                <Typography color="text.disabled" variant="body1">
                                  TBD
                                </Typography>
                              )}
                            </Box>
                          )}
                        </Grid>
                        <Grid item xs={12}>
                          <Typography variant="h6">Attendees</Typography>
                          <Box sx={{ mt: 3 }}>
                            {Boolean(appEvent.attendees?.length) ? (
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', overflow: 'hidden' }}>
                                {appEvent.attendees.map((attendee, index) => {
                                  const atendeeUser = members[attendee.user_id]
                                  return (
                                    <AppProfileImage
                                      key={index}
                                      imageUrl={atendeeUser?.profile?.image || atendeeUser?.image}
                                      cropArea={atendeeUser?.profile?.image_crop_area || atendeeUser?.image_crop_area}
                                      name={atendeeUser?.first_name || ''}
                                      size={48}
                                      style={{ borderRadius: 20, marginRight: 3, marginBottom: 3 }}
                                    />
                                  )
                                })}
                              </Box>
                            ) : (
                              <Typography color="text.disabled" variant="body1">
                                No Attendees
                              </Typography>
                            )}
                          </Box>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Box>
              </Grid>
            </Grid>
          ) : (
            <></>
          )}
          {openAttendEvent && (
            <AttendEventDialog
              appEvent={appEvent}
              open={openAttendEvent}
              onClose={(e) => {
                if (e) {
                  setAppEvent({
                    ...appEvent,
                    attendees: [...(appEvent.attendees || []), e],
                  })
                }
                setOpenAttendEvent(false)
              }}
            />
          )}
          <Menu
            anchorEl={addCalendarAnchorEl}
            open={Boolean(addCalendarAnchorEl)}
            onClose={() => setAddCalendarAnchorEl(null)}
            PaperProps={{
              sx: {
                borderRadius: 2,
                mt: '6px',
                '& .MuiList-root.MuiMenu-list': {
                  paddingTop: 0,
                  paddingBottom: 0,
                },
                '& .MuiMenuItem-root': {
                  p: '8px 16px',
                },
                '& .MuiListItemIcon-root': {
                  minWidth: 0,
                  marginTop: '-2px',
                },
              },
            }}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
          >
            <MenuItem onClick={(e) => handleClickCalendarItem(e, 'Apple Calendar')}>
              <ListItemText primary={<Typography variant="body2">Apple Calendar</Typography>} />
            </MenuItem>
            <MenuItem onClick={(e) => handleClickCalendarItem(e, 'Google')}>
              <ListItemText primary={<Typography variant="body2">Google</Typography>} />
            </MenuItem>
            <MenuItem onClick={(e) => handleClickCalendarItem(e, 'Outlook')}>
              <ListItemText primary={<Typography variant="body2">Outlook</Typography>} />
            </MenuItem>
          </Menu>

          <Menu
            anchorEl={attendingAnchorEl}
            open={Boolean(attendingAnchorEl)}
            onClose={() => setAttendingAnchorEl(null)}
            PaperProps={{
              sx: {
                borderRadius: 2,
                mt: '4px',
                width: 120,
                '& .MuiList-root.MuiMenu-list': {
                  paddingTop: 0,
                  paddingBottom: 0,
                },
                '& .MuiMenuItem-root': {
                  p: '8px 16px',
                },
                '& .MuiListItemIcon-root': {
                  minWidth: 0,
                  marginTop: '-2px',
                },
              },
            }}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'center',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'center',
            }}
          >
            <MenuItem selected={true}>
              <ListItemText primary={<Typography variant="body2">Going</Typography>} />
            </MenuItem>
            <MenuItem
              onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
                setAttendingAnchorEl(null)
                handleNotAttend()
              }}
            >
              <ListItemText primary={<Typography variant="body2">Not Going</Typography>} />
            </MenuItem>
          </Menu>
        </Box>
      )}
    </NormalLayout>
  )
}
