'use client'

import Image from 'next/image'
import Link from 'next/link'
import { useState } from 'react'

import { ChangePasswordForm } from '@/components/auth/change-password-form'
import { buttonVariants } from '@/components/ui'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'
import memberupLogo from '@/public/assets/default/logos/memberup-logo.png'

const STATUS_MESSAGES = {
  passwordUpdated: {
    title: 'Password updated',
    message: 'Please log in with your new password.',
  },
  tokenExpired: {
    title: 'Link Expired',
    message: 'Your reset password link has expired. Please request a new one.',
  },
  wrongUser: {
    title: 'Wrong User',
    message: 'This password reset link was requested by another user.',
  },
  invalidToken: {
    title: 'Invalid Link',
    message: 'This password reset link is invalid. Please request a new one.',
  },
} as const

export default function ChangePasswordPage() {
  const [passwordUpdated, setPasswordUpdated] = useState(false)
  const [tokenExpired, setTokenExpired] = useState(false)
  const [wrongUser, setWrongUser] = useState(false)
  const [invalidToken, setInvalidToken] = useState(false)

  const currentState = passwordUpdated
    ? 'passwordUpdated'
    : tokenExpired
      ? 'tokenExpired'
      : wrongUser
        ? 'wrongUser'
        : invalidToken
          ? 'invalidToken'
          : null

  return (
    <ScrollArea className="absolute h-full w-full bg-grey-100 dark:bg-grey-700">
      <div className="flex h-full min-h-svh w-full items-center justify-center overflow-hidden">
        <div className="relative mx-4 w-full max-w-[498px] rounded-base bg-white-500 px-5 py-8 dark:bg-black-500 md:px-8">
          <div className="flex flex-col items-center">
            <Image className="mb-5" src={memberupLogo} width={170} height={25} alt="MemberUp" />
            <div className="mb-2 text-center text-lg font-semibold text-black-700 dark:text-white-500">
              {currentState ? STATUS_MESSAGES[currentState].title : 'Create a new password'}
            </div>

            {currentState && (
              <p className="mb-6 text-center text-sm font-normal text-black-200 dark:text-black-100">
                {STATUS_MESSAGES[currentState].message}
              </p>
            )}
          </div>
          {currentState ? (
            <Link className={cn(buttonVariants({ variant: 'default' }), 'w-full')} href="/login">
              {tokenExpired || wrongUser || invalidToken ? 'Back to Login' : 'Log in'}
            </Link>
          ) : (
            <ChangePasswordForm
              className="mt-4"
              onUpdatedPassword={() => setPasswordUpdated(true)}
              onTokenExpired={() => setTokenExpired(true)}
              onWrongUser={() => setWrongUser(true)}
              onInvalidToken={() => setInvalidToken(true)}
            />
          )}
        </div>
      </div>
    </ScrollArea>
  )
}
