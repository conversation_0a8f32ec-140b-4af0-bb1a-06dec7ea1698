'use client'

import DashboardIcon from '@mui/icons-material/Dashboard'
import But<PERSON> from '@mui/material/Button'
import { useRouter } from 'next/navigation'
import React from 'react'

import { BillingConfirmed } from '@memberup/shared/src/components/stripe/billing-confirmed'
import NormalLayout from '@/memberup/layouts/normal-layout'
import { selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const MemberupBillingConfirmed: React.FC = () => {
  const router = useRouter()
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))

  if (!membershipSetting) return null

  return (
    <NormalLayout
      visibleHeader={true}
      visibleProfileMenu={true}
      leftHeader={
        <Button
          variant="text"
          className="no-padding"
          color="inherit"
          startIcon={<DashboardIcon />}
          onClick={() => router.push('/')}
        >
          Dashboard
        </Button>
      }
    >
      <BillingConfirmed
        planName={membershipSetting.plan}
        isAnnual={membershipSetting.stripe_enable_annual}
        stripeSubscriptionStatus={membershipSetting.stripe_subscription_status}
      />
    </NormalLayout>
  )
}

export default MemberupBillingConfirmed
