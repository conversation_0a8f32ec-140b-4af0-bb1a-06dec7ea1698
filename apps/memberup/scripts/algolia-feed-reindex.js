const algoliasearch = require('algoliasearch')
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()
const algoliaClient = algoliasearch(
  process.env.NEXT_PUBLIC_ALGOLIA_APP_ID,
  process.env.NEXT_PUBLIC_ALGOLIA_ADMIN_API_KEY,
)
const algoliaIndex = algoliaClient.initIndex('feed')

const stripHtml = (htmlString) => {
  return htmlString.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ')
}

const isUUIDv4 = (uuid) => {
  const uuidv4Pattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidv4Pattern.test(uuid)
}

async function reindexFeeds() {
  try {
    console.log('Starting reindexing process...')
    await algoliaIndex.clearObjects().wait()
    console.log('Cleared existing objects from Algolia index.')

    console.log('Fetching feeds from database...')
    const feeds = await prisma.feed.findMany({
      where: { feed_type: 'default' },
      include: { user: true, channel: true },
    })
    console.log(`Fetched ${feeds.length} feeds from database.`)

    console.log('Fetching users from database...')
    const users = await prisma.user.findMany()
    const userMap = users.reduce((map, user) => {
      map[user.id] = user
      return map
    }, {})
    console.log(`Fetched ${users.length} users from database.`)

    console.log('Creating Algolia objects...')
    const algoliaObjects = feeds.map((feed) => {
      let text = stripHtml(feed.text || '').trim()

      text = text.replace(/{{mention:all}}/g, '@everyone').trim()

      const mentionPattern = /{{mention:user:(\w+)}}/g
      let match
      while ((match = mentionPattern.exec(text)) !== null) {
        const userId = match[1]
        const user = userMap[userId]
        if (user) {
          text = text.replace(match[0], `@${user.first_name.trim()} ${user.last_name.trim()}`).trim()
        }
      }

      let featuredThumbnail = undefined

      if (feed.attachments?.length > 0) {
        const featuredAttachmentVideo = feed.attachments.filter((attachment) => attachment.mimetype === 'video')
        if (featuredAttachmentVideo?.length > 0) {
          if (featuredAttachmentVideo?.[0]?.thumbnail?.startsWith('http')) {
            featuredThumbnail = featuredAttachmentVideo[0].thumbnail
          }
        }

        if (!featuredThumbnail) {
          const featuredAttachmentImage = feed.attachments.filter((attachment) => attachment.mimetype === 'image')
          if (featuredAttachmentImage?.length > 0) {
            if (featuredAttachmentImage?.[0]?.url?.startsWith('http')) {
              featuredThumbnail = featuredAttachmentImage[0].url
            }
          }
        }

        if (!featuredThumbnail) {
          const featuredAttachmentGif = feed.attachments.filter((attachment) => attachment.mimetype === 'gif')
          if (featuredAttachmentGif?.length > 0) {
            if (featuredAttachmentGif?.[0]?.url?.startsWith('http')) {
              /* convert non embed "https://giphy.com/gifs/doodles-cute-party-birthday-S7jE9UN8zNGOeqstLv" into "https://giphy.com/embed/S7jE9UN8zNGOeqstLv" */
              featuredThumbnail = featuredAttachmentGif[0].url.replace(
                /https:\/\/giphy\.com\/gifs\/.*-(.*)/,
                'https://giphy.com/embed/$1',
              )
              console.log('featuredThumbnail', featuredThumbnail)
            }
          }
        }
      }

      feed.title = feed.title

      const algoliaFeedObject = {
        title: feed.title ? (isUUIDv4(feed.title) ? undefined : feed.title.trim()) : undefined,
        text: text,
        author_full_name: `${feed.user.first_name.trim()} ${feed.user.last_name.trim()}`,
        author_id: feed.user.id,
        createdAt: feed.createdAt,
        updatedAt: feed.updatedAt,
        objectID: feed.id,
        permalink: feed.permalink?.trim(),
        channel: feed.channel.name.trim(),
        post_pic_url: featuredThumbnail,
        feed_status: feed.feed_status.trim(),
        viewable_by: feed.user.membership_id,
      }

      return algoliaFeedObject
    })
    console.log(`Created ${algoliaObjects.length} Algolia objects.`)

    console.log('Uploading Algolia objects...')
    await algoliaIndex.saveObjects(algoliaObjects).wait()
    console.log(`Uploaded ${algoliaObjects.length} feeds to Algolia.`)
  } catch (error) {
    console.error('Error reindexing feeds:', error)
  } finally {
    await prisma.$disconnect()
    console.log('Disconnected from Prisma.')
  }
}

reindexFeeds()

/* 

doppler run -- node apps/memberup/scripts/algolia-feed-reindex.js

*/
