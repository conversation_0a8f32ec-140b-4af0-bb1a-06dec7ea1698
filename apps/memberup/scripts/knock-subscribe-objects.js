// Creates subscriptions in Knock for all communities or a specific community
// Add --yes parameter for unattended operation
//
// Ensure Doppler is set up to run on the staging environment
// doppler run -- node  apps/memberup/scripts/knock-subscribe-objects.js [Membership ID]
require('dotenv').config()

const { askQuestion, getKnockApps } = require('./common')
const { Knock } = require('@knocklabs/node')
const { PrismaClient } = require('@prisma/client')
const yargs = require('yargs/yargs')
const { hideBin } = require('yargs/helpers')
const prisma = new PrismaClient()

const KNOCK_API_KEY = process.env.KNOCK_SECRET_API_KEY
const knockClient = new Knock(KNOCK_API_KEY)

const DELAY_TIME = 1000
const BATCH_SIZE = 50

const argv = yargs(hideBin(process.argv))
  .option('community-slugs', {
    type: 'string',
    description: 'Comma-separated list of community slugs',
    coerce: (arg) => (arg ? arg.split(',') : []),
  })
  .option('yes', {
    type: 'boolean',
    description: 'Unattended operation',
  }).argv

const delay = (ms) => new Promise((res) => setTimeout(res, ms))

const getMembershipsBySlugs = async (communitySlugs) => {
  console.log('Fetching membership from database...', communitySlugs)
  const memberships = await prisma.membership.findMany({
    where: {
      slug: {
        in: communitySlugs?.length ? communitySlugs : undefined,
      },
      active: true,
    },
    select: {
      id: true,
      slug: true,
      name: true,
    },
  })
  return memberships
}

const deleteUsers = async (users) => {
  for (const user of users) {
    try {
      await knockClient.users.delete(user.id)
      console.log(`Deleted user: ${user.id}`)
      await delay(DELAY_TIME)
    } catch (error) {
      console.error(`Failed to delete user ${user.id}: ${error.message}`)
      // Continue to the next user without stopping the script
    }
  }
}

const bulkAddSubscriptions = async (membershipId, slug, objects, recipients) => {
  const recipientsObjects = recipients.map((u) => ({
    id: u.id,
    name: u.first_name && u.last_name ? `${u.first_name} ${u.last_name}` : `${u.first_name}` || `${u.last_name}`,
    email: u.email,
  }))

  const subscriptions = objects.map((o) => ({
    id: o,
    recipients: recipientsObjects,
  }))
  await knockClient.objects.bulkAddSubscriptions(slug, subscriptions)
  await delay(DELAY_TIME)
}

const addKnockSubscriptions = async (membershipId, slug, objects, users) => {
  console.log(`Deleting existing users for ${membershipId}:${slug}`)
  await deleteUsers(users)

  console.log(`Adding subscriptions to ${membershipId}:${slug} collection`)
  for (let i = 0; i < users.length; i += BATCH_SIZE) {
    const recipients = users.slice(i, i + BATCH_SIZE)
    await bulkAddSubscriptions(membershipId, slug, objects, recipients)
  }
  console.log('Done')
}

const runAddKnockSubscriptions = async (memberships) => {
  console.log(`Processing ${memberships.length} memberships`)

  for (const membership of memberships) {
    try {
      const usersList = await prisma.user.findMany({
        where: {
          membership_id: membership.id,
          status: 'active',
          role: {
            in: ['member', 'owner', 'creator'],
          },
        },
      })
      const objects = ['new-content', 'new-event', 'new-spark', 'new-everyone-mention']
      await addKnockSubscriptions(membership.id, membership.slug, objects, usersList)
    } catch (error) {
      console.error(`Failed processing membership ${membership.id}: ${error.message}`)
      // Continue to the next membership without stopping the script
    }
  }
}

async function main() {
  try {
    const communitySlugs = argv['community-slugs']
    const yes = argv['yes']

    const memberships = await getMembershipsBySlugs(communitySlugs)
    console.log(
      `Running object subscription process for ${
        memberships.length > 0 ? memberships.map((m) => m.slug).join(', ') : 'all communities'
      }`,
    )

    console.log(`Using KNOCK_API_KEY key=${KNOCK_API_KEY}, name=${getKnockApps[KNOCK_API_KEY].name}`)

    if (!yes) {
      const answer = await askQuestion('Do you want to proceed? (y/n): ')
      if (answer.toLowerCase() !== 'y') {
        console.log('Good bye.')
        return
      }
    }

    await runAddKnockSubscriptions(memberships)
  } catch (err) {
    console.error(`An error occurred: ${err.message}`)
  }
}

main()

// doppler run -- node  apps/memberup/scripts/knock-subscribe-objects.js --yes
