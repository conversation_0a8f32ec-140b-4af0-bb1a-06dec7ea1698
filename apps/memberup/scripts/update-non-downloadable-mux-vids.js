// doppler run -- node ./scripts/update-non-downloadable-mux-vids.js --help
require('dotenv').config()
const { PrismaClient } = require('@prisma/client')
const Mux = require('@mux/mux-node')
const prisma = new PrismaClient()
const fs = require('fs')
const path = require('path')
const filePath = path.join(__dirname, 'lessons.json')
const errorLogPath = path.join(__dirname, 'downloadable-lessons-error.log')
const yargs = require('yargs/yargs')
const { hideBin } = require('yargs/helpers')

const { Video } = new Mux(process.env.NEXT_PUBLIC_MUX_ACCESS_TOKEN_ID, process.env.NEXT_PUBLIC_MUX_SECRET_KEY)
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

const argv = yargs(hideBin(process.argv))
  .option('community-slugs', {
    type: 'string',
    description: 'Comma-separated list of community slugs',
    coerce: (arg) => (arg ? arg.split(',') : []),
  })
  .option('lesson-ids', {
    type: 'string',
    description: 'Comma-separated list of lesson ids',
    coerce: (arg) => (arg ? arg.split(',') : []),
  }).argv

async function updateLessonsWithStaticRenditions(membershipId, lessonId) {
  try {
    // Fetch video lessons without static renditions
    let lessons
    if (membershipId) {
      lessons =
        await prisma.$queryRaw`SELECT * FROM content_library_course_lessons WHERE type = 'video' AND membership_id = ${membershipId} AND JSON_EXTRACT(media_file, '$.status') = 'ready' AND JSON_EXTRACT(media_file, '$.static_renditions') IS NULL`
    } else if (lessonId) {
      lessons =
        await prisma.$queryRaw`SELECT * FROM content_library_course_lessons WHERE type = 'video' AND id = ${lessonId} AND JSON_EXTRACT(media_file, '$.status') = 'ready' AND JSON_EXTRACT(media_file, '$.static_renditions') IS NULL`
    } else {
      lessons =
        await prisma.$queryRaw`SELECT * FROM content_library_course_lessons WHERE type = 'video' AND JSON_EXTRACT(media_file, '$.status') = 'ready' AND JSON_EXTRACT(media_file, '$.static_renditions') IS NULL`
    }

    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath)
    }
    fs.writeFileSync(filePath, JSON.stringify(lessons, null, 2))

    console.log('lessons saved to file')
    console.log(`Total lessons to process: ${lessons.length}`)

    let processedCount = 0
    for (const lesson of lessons) {
      try {
        console.log('lesson', lesson.id, lesson.title)
        const mediaFile = lesson.media_file || {}
        // Enable MP4 support for the asset in Mux
        let asset = await Video.Assets.get(mediaFile.id)
        if (!asset.static_renditions) {
          asset = await Video.Assets.updateMp4Support(mediaFile.id, { mp4_support: 'standard' })
        }
        // Update the lesson with the new static rendition data
        await prisma.contentLibraryCourseLesson.update({
          where: { id: lesson.id },
          data: {
            media_file: asset,
          },
        })
        processedCount++
        console.log(`Updated lesson ${lesson.id} with static renditions. Progress: ${processedCount}/${lessons.length}`)

        await delay(1000)
      } catch (error) {
        const errorMessage = `Failed to update lesson ${lesson.id} (${lesson.title}): ${error.message}\n`
        console.error(errorMessage)
        console.error(error)

        // Append error message to the error log file
        fs.appendFileSync(errorLogPath, errorMessage)

        await delay(1000)
      }
    }

    console.log('All eligible lessons updated successfully.')
  } catch (error) {
    console.error('Failed to update lessons with static renditions:', error)
  } finally {
    await prisma.$disconnect()
  }
}

const getMembershipsBySlugs = async (communitySlugs) => {
  console.log('Fetching membership from database...', communitySlugs)
  const memberships = await prisma.membership.findMany({
    where: {
      slug: {
        in: communitySlugs?.length ? communitySlugs : undefined,
      },
    },
    select: {
      id: true,
      slug: true,
      name: true,
    },
  })
  return memberships
}

async function main() {
  const communitySlugs = argv['community-slugs']
  if (communitySlugs && communitySlugs.length > 0) {
    const memberships = await getMembershipsBySlugs(communitySlugs)
    console.log(memberships)
    for (let membership of memberships) {
      await updateLessonsWithStaticRenditions(membership.id)
    }
  } else {
    const lessonIds = argv['lesson-ids']
    if (lessonIds && lessonIds.length > 0) {
      for (let lessonId of lessonIds) {
        await updateLessonsWithStaticRenditions(null, lessonId)
      }
    } else {
      await updateLessonsWithStaticRenditions()
    }
  }
}

main()
