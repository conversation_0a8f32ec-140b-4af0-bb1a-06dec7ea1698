const knock =  require('@knocklabs/node')
const { Knock } = knock
const API_KEY = process.env.KNOCK_SECRET_API_KEY


async function knockTriggerWorkflow(
    tenant,
    workflowKey,
    actor,
    recipients,
    data,
    cancellationKey,
    idempotencyRequestKey
  ) {
    try {
      const knockClient = new Knock(API_KEY)
      const result = await knockClient.workflows.trigger(
        workflowKey,
        {
          actor,
          recipients,
          tenant,
          data,
          cancellationKey,
        },
        idempotencyRequestKey
          ? {
              idempotencyKey: idempotencyRequestKey,
            }
          : undefined
      )
      return result
    } catch (err) {
      console.error(err)
    }
  }

async function main(){
    const idempotencyRequestKey = `new-member-notification-cada0a81-4f43-4cf8-b39f-82ddaa3888c1`
    const result = await knockTriggerWorkflow(
      'b2b0f062-a1e4-4243-8eac-d8dc91ccd76e',
      'new-member-notification',
      '11ef9be6-9ead-4e67-8ca9-94d719ca7f3b',
      ['11ef9be6-9ead-4e67-8ca9-94d719ca7f3b'],
      {
        "community_name": "Manual Testing t5svi7",
        "community_url": "https://manual-testing.memberup-staging.com"
      },
      null,
      idempotencyRequestKey
    )


    console.log('workflow run', result)
}

main()

// doppler run -- node apps/memberup/scripts/knock-test.js