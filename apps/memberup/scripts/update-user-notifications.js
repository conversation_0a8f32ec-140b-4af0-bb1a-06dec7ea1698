require('dotenv').config()

const { Knock } = require('@knocklabs/node')
const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const API_KEY = process.env.KNOCK_SECRET_API_KEY

const NOTIFICATION_SETTINGS = [
  {
    id: '3',
    title: 'Event reminders',
    name: 'event',
    email: true,
    in_app_feed: true,
    email_disabled: false,
  },
  {
    id: '4',
    title: 'New spark reminder',
    name: 'spark-reminder',
    email: false,
    email_disabled: true,
    in_app_feed: true,
  },
  {
    id: '5',
    title: 'Likes on my posts',
    name: 'new-like-notification',
    email: false,
    email_disabled: true,
    in_app_feed: true,
  },
  {
    id: '6',
    title: 'Comments on my posts',
    name: 'new-comment-notification',
    email: false,
    in_app_feed: true,
    email_disabled: true,
  },
]

const updateUserNotifications = async () => {
  try {
    const knockClient = new Knock(API_KEY)
    const users = await prisma.user.findMany({
      select: {
        id: true,
        membership_id: true,
        profile: {
          select: {
            id: true,
            enable_notifications: true,
          },
        },
      },
    })

    let count = 0
    while (count < users.length) {
      try {
        const userProfile = users[count].profile
        const enableNotifications = userProfile.enable_notifications
        const preferenceSet = {
          workflows: {},
        }

        for (const notificationSetting of NOTIFICATION_SETTINGS) {
          if (enableNotifications) {
            if (
              ['new-like-notification', 'new-comment-notification', 'new-message-notification'].includes(
                notificationSetting.name,
              )
            ) {
              enableNotifications[notificationSetting.name] = {
                email: notificationSetting.email,
                in_app_feed: notificationSetting.in_app_feed,
              }
            }
          }

          preferenceSet.workflows[notificationSetting.name] = {
            channel_types: {
              email: enableNotifications?.[notificationSetting.name]?.email ?? notificationSetting.email,
              in_app_feed:
                enableNotifications?.[notificationSetting.name]?.in_app_feed ?? notificationSetting.in_app_feed,
            },
          }
        }

        if (enableNotifications) {
          await prisma.userProfile.update({
            where: {
              id: userProfile.id,
            },
            data: {
              enable_notifications: enableNotifications,
            },
          })
        }

        await knockClient.users.setPreferences(users[count].id, preferenceSet, {
          preferenceSet: users[count].membership_id,
        })
        count++
      } catch (err) {
        console.log('err ====', err)
        count++
      }
    }
  } catch (e) {
    console.error(e)
    process.exit(1)
  }
}

updateUserNotifications()
