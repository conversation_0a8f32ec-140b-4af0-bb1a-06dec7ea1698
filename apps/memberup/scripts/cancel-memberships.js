// TODO: this script is probably not working because of changes to Spark models
require('dotenv').config()

const { StreamChat } = require('stream-chat')
const { PrismaClient } = require('@prisma/client')
const algoliasearch = require('algoliasearch')
const Stripe = require('stripe')
const Mux = require('@mux/mux-node')
const { Knock } = require('@knocklabs/node')
const axios = require('axios')

const ALGOLIA_APP_ID = process.env.NEXT_PUBLIC_ALGOLIA_APP_ID
const ALGOLIA_ADMIN_API_KEY = process.env.NEXT_PUBLIC_ALGOLIA_ADMIN_API_KEY
const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET
const MU_ID = process.env.NEXT_PUBLIC_MU_ID
const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY
const STRIPE_SECRET_KEY_TEST = process.env.STRIPE_SECRET_KEY_TEST
const NEXT_PUBLIC_STRIPE_LIVE_MODE = process.env.NEXT_PUBLIC_STRIPE_LIVE_MODE
const MUX_ACCESS_TOKEN_ID = process.env.NEXT_PUBLIC_MUX_ACCESS_TOKEN_ID
const MUX_SECRET_KEY = process.env.NEXT_PUBLIC_MUX_SECRET_KEY
const SAMPLE_MUX_ASSET_ID = process.env.NEXT_PUBLIC_SAMPLE_MUX_ASSET_ID
const KNOCK_SECRET_API_KEY = process.env.KNOCK_SECRET_API_KEY

const MEMBERSHIPS_WHILELIST = ['qa', 'demo', 'test-123', 'gabriel-avellaneda-rocmgc']

const deleteStripeCustomers = async (stripe, customerIds) => {
  try {
    let count = 0
    while (count < customerIds.length) {
      await stripe.customers.del(customerIds[count])
      count++
    }
  } catch (err) {
    console.log('Stripe customer delete err =====', err)
  }
}

const deleteAlgoliaObjects = async (algoliaIndex, ids) => {
  try {
    return algoliaIndex.deleteObjects(ids).wait()
  } catch (err) {
    console.log('err =====', err)
  }
}

const cancelMemberships = async () => {
  try {
    const prisma = new PrismaClient()
    const streamChatClient = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
      timeout: 30000,
    })
    const stripeMemberup = new Stripe(
      NEXT_PUBLIC_STRIPE_LIVE_MODE === 'true' ? STRIPE_SECRET_KEY : STRIPE_SECRET_KEY_TEST,
      {
        apiVersion: '2023-10-16',
        maxNetworkRetries: 2,
      },
    )
    const { Video } = new Mux(MUX_ACCESS_TOKEN_ID, MUX_SECRET_KEY)
    const searchAdminClient = algoliasearch(ALGOLIA_APP_ID, ALGOLIA_ADMIN_API_KEY)
    const algoliaMembersIndex = searchAdminClient.initIndex('member')
    const algoliaFeedsIndex = searchAdminClient.initIndex('feed')
    const algoliaLibrariesIndex = searchAdminClient.initIndex('library')
    const knockClient = new Knock(KNOCK_SECRET_API_KEY)

    const memberships = await prisma.membership.findMany({
      where: {
        slug: { notIn: MEMBERSHIPS_WHILELIST },
      },
      include: {
        users: {
          include: {
            profile: true,
          },
        },
        membership_setting: true,
      },
    })

    let membershipCount = 0
    while (membershipCount < memberships.length) {
      try {
        const membership = memberships[membershipCount]
        console.log('membership ====', membership.id)

        const channels = await prisma.channel.findMany({
          where: {
            membership_id: membership.id,
          },
          include: {
            feeds: true,
          },
        })

        if (channels.length) {
          try {
            console.log('Deleting feed stream channels')
            await streamChatClient.deleteChannels(
              channels.map((item) => `team:${item.id}`),
              {
                hard_delete: true,
              },
            )
            console.log('Done')
          } catch (err) {
            console.log('Feed stream channels delete err =====', err)
          }

          let channelCount = 0
          while (channelCount < channels.length) {
            try {
              const channel = channels[channelCount]
              console.log('Deleting algolia feeds =====', channel.id)
              const postIds = channel.feeds.filter((feed) => !feed.parent_id).map((feed) => feed.id)
              await deleteAlgoliaObjects(algoliaFeedsIndex, postIds)
              channelCount++
              console.log('Done')
            } catch (err) {
              console.log('Algolia feeds delete err =====', err)
            }
          }
        }

        const libraries = await prisma.library.findMany({
          where: {
            membership_id: membership.id,
          },
        })

        if (libraries.length) {
          try {
            console.log('Deleting library stream channels')
            await streamChatClient.deleteChannels(
              libraries.map((item) => `team:${item.id}`),
              {
                hard_delete: true,
              },
            )
            console.log('Done')
          } catch (err) {
            console.log('Library stream channels delete err =====', err)
          }

          console.log('Deleting algolia libraries')
          await deleteAlgoliaObjects(
            algoliaLibrariesIndex,
            libraries.map((item) => item.id),
          )
          console.log('Done')

          let libraryCount = 0
          while (libraryCount < libraries.length) {
            try {
              const library = libraries[libraryCount]
              if (library.mux_asset?.id && library.mux_asset.id !== SAMPLE_MUX_ASSET_ID) {
                console.log('Deleting mux asset =======', library.mux_asset.id)
                await Video.Assets.del(library.mux_asset.id)
                console.log('Done')
              }
              libraryCount++
            } catch (err) {
              console.log('Mux asset delete err =====', err)
            }
          }
        }

        const users = membership.users
        const userIds = users.map((item) => item.id)
        const owner = membership.users.find((item) => item.role === 'owner')
        const members = membership.users.filter((item) => item.role !== 'owner')

        console.log('Deleting algolia members')
        await deleteAlgoliaObjects(algoliaMembersIndex, userIds)
        console.log('Done')

        try {
          console.log('Deleting stream users')
          await streamChatClient.deleteUsers(userIds, {
            user: 'hard',
            conversations: 'hard',
            messages: 'hard',
          })
          console.log('Done')
        } catch (err) {
          console.log('Stream users delete err =====', err)
        }

        const memberStripeCustomerIds = members
          .filter((member) => Boolean(member.profile?.stripe_customer_id))
          .map((member) => member.profile.stripe_customer_id)
        if (memberStripeCustomerIds.length && membership?.membership_setting?.stripe_connect_account?.access_token) {
          console.log('Deleting stripe members')
          const stripeMembership = new Stripe(membership.membership_setting.stripe_connect_account.access_token, {
            apiVersion: '2023-10-16',
            maxNetworkRetries: 2,
          })
          await deleteStripeCustomers(stripeMembership, memberStripeCustomerIds)
          console.log('Done')
        }

        if (owner?.profile?.stripe_customer_id) {
          console.log('Deleting stripe customer')
          await deleteStripeCustomers(stripeMemberup, [owner.profile.stripe_customer_id])
          console.log('Done')
        }

        try {
          console.log('Deleting knock users')
          await knockClient.users.bulkDelete(userIds)
          console.log('Done')
        } catch (err) {
          console.log('Knock user delete err ====', err)
        }

        try {
          console.log('Deleting knock objects')
          await knockClient.objects.bulkDelete(membership.slug, [
            'new-content',
            'new-event',
            'new-spark',
            'new-everyone-mention',
          ])
          console.log('Done')

          // TODO: Delete Knock Tenant?
        } catch (err) {
          console.log('Knock objects delete err ====', err)
        }

        await prisma.inviteLink.deleteMany({
          where: {
            membership_id: membership.id,
          },
        })

        await prisma.sparkQuestionSetting.deleteMany({
          where: {
            membership_id: membership.id,
          },
        })

        await prisma.sparkMembershipCategory.deleteMany({
          where: {
            membership_id: membership.id,
          },
        })

        await prisma.sparkMembershipQuestion.deleteMany({
          where: {
            membership_id: membership.id,
          },
        })

        await prisma.sparkResponse.deleteMany({
          where: {
            membership_id: membership.id,
          },
        })
        await prisma.channel.deleteMany({
          where: {
            membership_id: membership.id,
          },
        })
        await prisma.membership.delete({
          where: {
            id: membership.id,
          },
        })
        membershipCount++
      } catch (err) {
        membershipCount++
        console.log('err =======', err)
      }
    }
  } catch (e) {
    console.error(e)
    process.exit(1)
  }
}

cancelMemberships()
