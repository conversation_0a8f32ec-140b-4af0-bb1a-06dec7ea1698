require('dotenv').config()

const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const computeUserMembershipStatus = (userStatus) => {
  switch (userStatus) {
    case 'active':
      return 'accepted'
    case 'banned':
      return 'banned'
    case 'deleted':
      return 'rejected'
    case 'inactive':
      return 'pending'
    case 'invited':
      return 'pending'
  }
}

const main = async () => {
  try {
    const users = await prisma.user.findMany({
      where: {
        status: 'active',
      },
      include: {
        profile: true,
      },
    })

    const updates = users.map((user) => {
      return (async () => {
        let mappedUser = user
        if (!user.is_primary) {
          console.log('user', user)
          mappedUser = await prisma.user.findFirst({
            where: { email: user.email, is_primary: true },
          })
          if (!mappedUser) {
            console.error('mappeddUser is null')
            console.log('user', user)
            process.exit(-1)
          }
          console.log(mappedUser)
          await prisma.user.update({
            where: {
              id: user.id,
            },
            data: {
              related_users: [mappedUser.id],
            },
          })
        }
        const result = await prisma.userMembership.create({
          data: {
            user_id: mappedUser.id,
            membership_id: user.membership_id,
            user_role: user.role,
            status: computeUserMembershipStatus(user.status),
            stripe_customer_id: user.profile.stripe_customer_id,
            stripe_payment_method_id: user.profile.stripe_payment_method_id,
            stripe_subscription_canceled_at: user.profile.stripe_subscription_canceled_at,
            stripe_subscription_id: user.profile.stripe_subscription_id,
            stripe_subscription_intent_client_secret: user.profile.stripe_subscription_intent_client_secret,
            stripe_subscription_status: user.profile.stripe_subscription_status,
            stripe_subscription_period_start_at: user.profile.stripe_subscription_period_start_at,
            stripe_subscription_period_end_at: user.profile.stripe_subscription_period_end_at,
            stripe_subscription_cancel_at_period_end: user.profile_subcription_cancel_at_period_end,
          },
        })
        console.log(result)
      })()
    })

    await Promise.all(updates)
  } catch (e) {
    console.error(e)
    process.exit(1)
  } finally {
  }
}

main()
