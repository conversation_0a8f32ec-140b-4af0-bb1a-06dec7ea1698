require('dotenv').config()

const { StreamChat } = require('stream-chat')
const { PrismaClient } = require('@prisma/client')
const _uniq = require('lodash/uniq')
const prisma = new PrismaClient()

const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET
const MU_ID = process.env.NEXT_PUBLIC_MU_ID

const getFullName = (firstName, lastName) => {
  // Both names
  if (firstName && lastName) return `${firstName} ${lastName}`

  // Only first name
  if (firstName && !lastName) return `${firstName}`

  // Only last name
  if (!firstName && lastName) return `${lastName}`

  // No names provided
  if (!firstName && !lastName) return 'No Name'
  return ''
}

const updateUsersStatus = async () => {
  try {
    const client = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
      timeout: 30000,
    })

    const memberships = await prisma.membership.findMany({
      //where: {id: '90a7cc31-c8cd-4cce-b025-cf1be960d813'},
      where: {},
      include: {
        membership_setting: true,
        users: {
          include: {
            profile: true,
          },
        },
      },
    })

    for (const membership of memberships) {
      const isPaidMembership = membership.membership_setting?.stripe_prices?.some((price) => price.active)

      for (let user of membership.users) {
        const userProfile = user.profile
        const isNotMember = ['admin', 'creator', 'owner'].includes(user.role || 'member')
        if (isPaidMembership && user.status === 'inactive' && userProfile.stripe_subscription_status === 'active') {
          user = await prisma.user.update({
            where: { id: user.id },
            data: {
              status: 'active',
              profile: {
                update: {
                  active: true,
                },
              },
            },
          })
        } else if (user.status === 'active' && !userProfile.active) {
          await prisma.userProfile.update({
            where: { id: userProfile.id },
            data: { active: true },
          })
        }
        console.log('user id =====', user.id)
        await client.upsertUser({
          id: user.id,
          name: getFullName(user.first_name, user.last_name),
          image: userProfile.image || user.image,
          image_crop_area: userProfile.image_crop_area,
          role: isNotMember ? 'global_admin' : 'global_moderator',
          status: user.status,
          teams: isNotMember ? _uniq([MU_ID, user.membership_id]) : [user.membership_id],
        })
      }
    }
  } catch (e) {
    console.error(e)
    process.exit(1)
  } finally {
  }
}

updateUsersStatus()
