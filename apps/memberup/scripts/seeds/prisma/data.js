require('dotenv').config()

const MU_ID = process.env.NEXT_PUBLIC_MU_ID
const DEFAULT_DOMAIN = process.env.NEXT_PUBLIC_DEFAULT_DOMAIN

const university = {
  membership: {
    id: MU_ID,
    active: true,
    brand: 'Default',
    host: `university.${DEFAULT_DOMAIN}`,
    name: 'Memberup University',
    slug: 'university',
  },
}

const sparkCategories = [
  {
    name: 'Getting to know',
    slug: 'getting-to-know',
    icon: '👋',
    description:
      'This question pack will help your members break the ice and go beyond surface level conversations to create real connection in your community.',
    seed_id: '0',
    spark_questions: [
      {
        content: 'What sparked you to join this community?',
        membership_id: null,
        sequence: 0,
        seed_id: '0-0',
      },
      {
        content: 'What do you hope to achieve from joining this community?',
        membership_id: null,
        sequence: 1,
        seed_id: '0-1',
      },
      {
        content: 'Share one fun fact about where you live?',
        membership_id: null,
        sequence: 2,
        seed_id: '0-2',
      },
      {
        content: 'Who would you like to connect with in this community?',
        membership_id: null,
        sequence: 3,
        seed_id: '0-3',
      },
      {
        content: 'Describe yourself in one sentence:',
        membership_id: null,
        sequence: 4,
        seed_id: '0-4',
      },
      {
        content: 'Is there anything specific you are looking for support with?',
        membership_id: null,
        sequence: 5,
        seed_id: '0-5',
      },
      {
        content: 'What do you need most right now?',
        membership_id: null,
        sequence: 6,
        seed_id: '0-6',
      },
      {
        content: 'What are some of your personal rules you refuse to break?',
        membership_id: null,
        sequence: 7,
        seed_id: '0-7',
      },
      {
        content: 'What advice would you give to your younger self?',
        membership_id: null,
        sequence: 8,
        seed_id: '0-8',
      },
      {
        content: 'What are the top three things on your bucket list?',
        membership_id: null,
        sequence: 9,
        seed_id: '0-9',
      },
      {
        content: 'What were the three biggest turning points in your life?',
        membership_id: null,
        sequence: 10,
        seed_id: '0-10',
      },
      {
        content: 'Something that could stop me from getting the most out of this community is:',
        membership_id: null,
        sequence: 11,
        seed_id: '0-11',
      },
      {
        content: 'What is something you think everyone should do once in their lives?',
        membership_id: null,
        sequence: 12,
        seed_id: '0-12',
      },
      {
        content: 'What do you believe to be your greatest strength?',
        membership_id: null,
        sequence: 13,
        seed_id: '0-13',
      },
      {
        content: 'What do you believe to be your greatest weakness?',
        membership_id: null,
        sequence: 14,
        seed_id: '0-14',
      },
      {
        content: 'What is a mistake you made and learned from?',
        membership_id: null,
        sequence: 15,
        seed_id: '0-15',
      },
      {
        content: "What is the best advice you've ever recieved?",
        membership_id: null,
        sequence: 16,
        seed_id: '0-16',
      },
      {
        content: 'What is the best thing about this community?',
        membership_id: null,
        sequence: 17,
        seed_id: '0-17',
      },
      {
        content: 'One year from now, what are you proud to have achieved?',
        membership_id: null,
        sequence: 18,
        seed_id: '0-18',
      },
      {
        content: 'What is something not a lot of people know about you?',
        membership_id: null,
        sequence: 19,
        seed_id: '0-19',
      },
      {
        content: 'Who in your life inspires you to be better?',
        membership_id: null,
        sequence: 20,
        seed_id: '0-20',
      },
      {
        content: 'What are your three favorite books and why?',
        membership_id: null,
        sequence: 21,
        seed_id: '0-21',
      },
      {
        content: 'What do the first 30 minutes of your day look like?',
        membership_id: null,
        sequence: 22,
        seed_id: '0-22',
      },
      {
        content: 'What activity do you love so much that it makes you loose track of time?',
        membership_id: null,
        sequence: 23,
        seed_id: '0-23',
      },
      {
        content: "What's one accomplishment you are most proud of?",
        membership_id: null,
        sequence: 24,
        seed_id: '0-24',
      },
      {
        content: 'What book has made the biggest impact in your life?',
        membership_id: null,
        sequence: 25,
        seed_id: '0-25',
      },
      { content: 'What was your first job?', membership_id: null, sequence: 26, seed_id: '0-26' },
      {
        content: 'What is the biggest purchase you have ever made?',
        membership_id: null,
        sequence: 27,
        seed_id: '0-27',
      },
      {
        content: 'Who is someone you really admire?',
        membership_id: null,
        sequence: 28,
        seed_id: '0-28',
      },
      {
        content: 'What is something you believed earlier in your life but think differently now?',
        membership_id: null,
        sequence: 29,
        seed_id: '0-29',
      },
      {
        content: 'What did you want to be when you grew up?',
        membership_id: null,
        sequence: 30,
        seed_id: '0-30',
      },
      {
        content: 'What podcast would you recommend and why?',
        membership_id: null,
        sequence: 31,
        seed_id: '0-31',
      },
      {
        content: 'What is this chapter of your life called?',
        membership_id: null,
        sequence: 32,
        seed_id: '0-32',
      },
      {
        content: 'What show on Netflix did you binge watch embarrassingly fast?',
        membership_id: null,
        sequence: 33,
        seed_id: '0-33',
      },
      {
        content: 'What is the best gift you have ever received?',
        membership_id: null,
        sequence: 34,
        seed_id: '0-34',
      },
      {
        content: 'What important truth do very few people agree with you on?',
        membership_id: null,
        sequence: 35,
        seed_id: '0-35',
      },
      {
        content: 'What stereotype do you completely live up to?',
        membership_id: null,
        sequence: 36,
        seed_id: '0-36',
      },
      {
        content: 'If you could ask for help with one thing today, what would that be?',
        membership_id: null,
        sequence: 37,
        seed_id: '0-37',
      },
      {
        content: 'What was the last random thing that made you smile?',
        membership_id: null,
        sequence: 38,
        seed_id: '0-38',
      },
      {
        content: 'What is your favorite quote?',
        membership_id: null,
        sequence: 39,
        seed_id: '0-39',
      },
      {
        content: 'Have you had your fifteen minutes of fame yet? If so, what was it?',
        membership_id: null,
        sequence: 40,
        seed_id: '0-40',
      },
      {
        content: 'What is your guilty pleasure?',
        membership_id: null,
        sequence: 41,
        seed_id: '0-41',
      },
      {
        content: 'If you could have any fictional character as your friend, who would it be?',
        membership_id: null,
        sequence: 42,
        seed_id: '0-42',
      },
      {
        content: 'Is there something that you like that creeps other people out?',
        membership_id: null,
        sequence: 43,
        seed_id: '0-43',
      },
      {
        content: 'What is your dream job if money did not matter?',
        membership_id: null,
        sequence: 44,
        seed_id: '0-44',
      },
      {
        content: 'If you had to relive the same day of your life, which day would you choose and why?',
        membership_id: null,
        sequence: 45,
        seed_id: '0-45',
      },
      {
        content: 'What is your most used emoji?',
        membership_id: null,
        sequence: 46,
        seed_id: '0-46',
      },
      {
        content: 'If you could do anything in the next year, what would it be?',
        membership_id: null,
        sequence: 47,
        seed_id: '0-47',
      },
      {
        content: 'If you could change one thing about how you were raised, what would it be?',
        membership_id: null,
        sequence: 48,
        seed_id: '0-48',
      },
      {
        content: 'If you could solve one world problem, what would it be?',
        membership_id: null,
        sequence: 49,
        seed_id: '0-49',
      },
      {
        content: 'If you could go back in time, what is one thing you would tell your teenage self?',
        membership_id: null,
        sequence: 50,
        seed_id: '0-50',
      },
      {
        content: 'What is one skill you are committed to learning over the next year?',
        membership_id: null,
        sequence: 51,
        seed_id: '0-51',
      },
      {
        content: 'If you were to write a book, what would it be about?',
        membership_id: null,
        sequence: 52,
        seed_id: '0-52',
      },
      {
        content: 'If you could only have 5 apps on your phone, what would they be?',
        membership_id: null,
        sequence: 53,
        seed_id: '0-53',
      },
      {
        content: 'If you could be famous, what would you want to be famous for?',
        membership_id: null,
        sequence: 54,
        seed_id: '0-54',
      },
      {
        content: 'Would you rather save money or save time?',
        membership_id: null,
        sequence: 55,
        seed_id: '0-55',
      },
      {
        content: 'What mark do you hope to leave on this community?',
        membership_id: null,
        sequence: 56,
        seed_id: '0-56',
      },
      {
        content: 'Would you rather win the lottery or live twice as long?',
        membership_id: null,
        sequence: 57,
        seed_id: '0-57',
      },
      {
        content: 'What is a hidden super power of yours?',
        membership_id: null,
        sequence: 58,
        seed_id: '0-58',
      },
      {
        content: 'What is the best question someone can ask to get to know you more?',
        membership_id: null,
        sequence: 59,
        seed_id: '0-59',
      },
      {
        content: 'What would make this community the best investment you ever made?',
        membership_id: null,
        sequence: 60,
        seed_id: '0-60',
      },
      {
        content: 'What is an unexpected win you experienced this week?',
        membership_id: null,
        sequence: 61,
        seed_id: '0-61',
      },
      {
        content: 'When was the best year of your life so far and why?',
        membership_id: null,
        sequence: 62,
        seed_id: '0-62',
      },
      {
        content: 'What is the most difficult transition you have experienced in your life?',
        membership_id: null,
        sequence: 63,
        seed_id: '0-63',
      },
      {
        content: 'What one habit has had the biggest impact in your life?',
        membership_id: null,
        sequence: 64,
        seed_id: '0-64',
      },
      {
        content: 'What is one area of your life where you have room for growth?',
        membership_id: null,
        sequence: 65,
        seed_id: '0-65',
      },
      {
        content: 'What does it mean for you to be successful?',
        membership_id: null,
        sequence: 66,
        seed_id: '0-66',
      },
      {
        content: 'What is the best complement you have ever received?',
        membership_id: null,
        sequence: 67,
        seed_id: '0-67',
      },
      {
        content: 'Which three words best describe you?',
        membership_id: null,
        sequence: 68,
        seed_id: '0-68',
      },
      {
        content: 'What do you think is the biggest problem in the world right now?',
        membership_id: null,
        sequence: 69,
        seed_id: '0-69',
      },
      {
        content: 'What is one thing you are grateful for today?',
        membership_id: null,
        sequence: 70,
        seed_id: '0-70',
      },
      {
        content: "What is the craziest thing you've ever done?",
        membership_id: null,
        sequence: 71,
        seed_id: '0-71',
      },
      {
        content: 'What is your favorite YouTube channel and why?',
        membership_id: null,
        sequence: 72,
        seed_id: '0-72',
      },
      {
        content: 'What do you wish you had more time for?',
        membership_id: null,
        sequence: 73,
        seed_id: '0-73',
      },
      {
        content: 'What is the hardest thing you have ever done?',
        membership_id: null,
        sequence: 74,
        seed_id: '0-74',
      },
      {
        content: 'What is the best thing about being your age?',
        membership_id: null,
        sequence: 75,
        seed_id: '0-75',
      },
      {
        content: 'What do you worry about the most?',
        membership_id: null,
        sequence: 76,
        seed_id: '0-76',
      },
      {
        content: 'What helps you feel better when you are upset or stressed?',
        membership_id: null,
        sequence: 77,
        seed_id: '0-77',
      },
      {
        content: 'What is the most adventurous thing you have ever done?',
        membership_id: null,
        sequence: 78,
        seed_id: '0-78',
      },
      {
        content: 'What do you think the world will be like in 25 years?',
        membership_id: null,
        sequence: 79,
        seed_id: '0-79',
      },
      {
        content: 'If you won 10 million dollars what would you do?',
        membership_id: null,
        sequence: 80,
        seed_id: '0-80',
      },
      { content: 'What made you laugh today?', membership_id: null, sequence: 81, seed_id: '0-81' },
      {
        content: 'On a scale of 1-10 how would you rate your week?',
        membership_id: null,
        sequence: 82,
        seed_id: '0-82',
      },
      {
        content: 'What is a new skill you want to learn?',
        membership_id: null,
        sequence: 83,
        seed_id: '0-83',
      },
      {
        content: 'Who is your favorite person to follow on Instagram and why?',
        membership_id: null,
        sequence: 84,
        seed_id: '0-84',
      },
      {
        content: 'What limiting belief is holding you back the most?',
        membership_id: null,
        sequence: 85,
        seed_id: '0-85',
      },
      {
        content: 'Are you holding onto a secret that you want to come clean about?',
        membership_id: null,
        sequence: 86,
        seed_id: '0-86',
      },
      {
        content: 'What types of friends are you looking for in your life?',
        membership_id: null,
        sequence: 87,
        seed_id: '0-87',
      },
      {
        content: "What is something you overcame that you didn't think you could?",
        membership_id: null,
        sequence: 88,
        seed_id: '0-88',
      },
      {
        content: 'Do you know your personality type? If so, share below:',
        membership_id: null,
        sequence: 89,
        seed_id: '0-89',
      },
      {
        content: 'The most attractive quality a person can possess is:',
        membership_id: null,
        sequence: 90,
        seed_id: '0-90',
      },
      {
        content: 'What lesson do you want to teach your grandchildren?',
        membership_id: null,
        sequence: 91,
        seed_id: '0-91',
      },
      {
        content: 'Would you rather be rich and unknown or famous and poor?',
        membership_id: null,
        sequence: 92,
        seed_id: '0-92',
      },
      {
        content: 'Would you rather be loved or respected?',
        membership_id: null,
        sequence: 93,
        seed_id: '0-93',
      },
      {
        content: 'What is one habit that has made you feel better?',
        membership_id: null,
        sequence: 94,
        seed_id: '0-94',
      },
      {
        content: 'What is the best advice you have received for overcoming judgment?',
        membership_id: null,
        sequence: 95,
        seed_id: '0-95',
      },
      {
        content: 'Tell a story of the craziest night of your life:',
        membership_id: null,
        sequence: 96,
        seed_id: '0-96',
      },
      {
        content: 'When was the last time you second guessed yourself?',
        membership_id: null,
        sequence: 97,
        seed_id: '0-97',
      },
      {
        content: 'What is one thing you can do today to be a better person?',
        membership_id: null,
        sequence: 98,
        seed_id: '0-98',
      },
    ],
  },
  {
    name: 'Health/Fitness',
    slug: 'health-fitness',
    icon: '🏃‍♀️',
    description:
      'Engage your members around health and fitness. The questions within will encourage your members to share workout tips and bond over their commitment to wellness.',
    seed_id: '1',
    spark_questions: [
      {
        content: 'What sparked you to join this community?',
        membership_id: null,
        sequence: 0,
        seed_id: '1-0',
      },
      {
        content: 'What do you hope to achieve from joining this community?',
        membership_id: null,
        sequence: 1,
        seed_id: '1-1',
      },
      {
        content: 'Share one fun fact about where you live?',
        membership_id: null,
        sequence: 2,
        seed_id: '1-2',
      },
      {
        content: 'Who would you like to connect with in this community and why?',
        membership_id: null,
        sequence: 3,
        seed_id: '1-3',
      },
      {
        content: 'Describe yourself in one sentence:',
        membership_id: null,
        sequence: 4,
        seed_id: '1-4',
      },
      {
        content: 'Is there anything specific you are looking for support with?',
        membership_id: null,
        sequence: 5,
        seed_id: '1-5',
      },
      {
        content: 'What do you need most right now?',
        membership_id: null,
        sequence: 6,
        seed_id: '1-6',
      },
      {
        content: 'What stage of your health journey are you currently on?',
        membership_id: null,
        sequence: 7,
        seed_id: '1-7',
      },
      {
        content: 'What do you plan to achieve in the next 3 months with your health?',
        membership_id: null,
        sequence: 8,
        seed_id: '1-8',
      },
      {
        content: 'What aspect of health do you struggle with the most?',
        membership_id: null,
        sequence: 9,
        seed_id: '1-9',
      },
      {
        content: 'What is your favorite part of training?',
        membership_id: null,
        sequence: 10,
        seed_id: '1-10',
      },
      {
        content: 'What is your least favorite part of training?',
        membership_id: null,
        sequence: 11,
        seed_id: '1-11',
      },
      {
        content: 'Do you have a pre-workout ritual?',
        membership_id: null,
        sequence: 12,
        seed_id: '1-12',
      },
      {
        content: 'What is your favorite meal of the day and why?',
        membership_id: null,
        sequence: 13,
        seed_id: '1-13',
      },
      {
        content: 'What is your favorite exercise to perform?',
        membership_id: null,
        sequence: 14,
        seed_id: '1-14',
      },
      {
        content: 'What is your least favorite exercise to perform?',
        membership_id: null,
        sequence: 15,
        seed_id: '1-15',
      },
      { content: 'Leg day or arm day?', membership_id: null, sequence: 16, seed_id: '1-16' },
      {
        content: 'What unconventional health tip have you tried?',
        membership_id: null,
        sequence: 17,
        seed_id: '1-17',
      },
      {
        content: 'What is the hardest thing you have achieved in your health journey so far?',
        membership_id: null,
        sequence: 18,
        seed_id: '1-18',
      },
      {
        content: 'What does your morning routine look like?',
        membership_id: null,
        sequence: 19,
        seed_id: '1-19',
      },
      {
        content: 'Who is your favorite health and fitness YouTuber and why?',
        membership_id: null,
        sequence: 20,
        seed_id: '1-20',
      },
      {
        content: 'Who is your favorite person to follow on Instagram for health and fitness?',
        membership_id: null,
        sequence: 21,
        seed_id: '1-21',
      },
      {
        content: 'One year from now, what are you proud to of achieved?',
        membership_id: null,
        sequence: 22,
        seed_id: '1-22',
      },
      {
        content: 'What is your guilty pleasure?',
        membership_id: null,
        sequence: 23,
        seed_id: '1-23',
      },
      {
        content: 'If you could ask for help with one thing today, what would that be?',
        membership_id: null,
        sequence: 24,
        seed_id: '1-24',
      },
      {
        content: 'What is the best health and fitness podcast you would recommend and why?',
        membership_id: null,
        sequence: 25,
        seed_id: '1-25',
      },
      {
        content: 'How do you stay motivated when you feel like quitting?',
        membership_id: null,
        sequence: 26,
        seed_id: '1-26',
      },
      {
        content: 'What is one tip you would tell your younger self?',
        membership_id: null,
        sequence: 27,
        seed_id: '1-27',
      },
      {
        content: 'What is one habit that has made you feel better?',
        membership_id: null,
        sequence: 28,
        seed_id: '1-28',
      },
      {
        content: 'What is the best advice you have received for overcoming judgment?',
        membership_id: null,
        sequence: 29,
        seed_id: '1-29',
      },
      {
        content: 'What is one thing you wish more people knew about health and fitness?',
        membership_id: null,
        sequence: 30,
        seed_id: '1-30',
      },
      { content: 'Share a funny gym story:', membership_id: null, sequence: 31, seed_id: '1-31' },
      {
        content: 'Share an epic fitness fail:',
        membership_id: null,
        sequence: 32,
        seed_id: '1-32',
      },
      {
        content: 'Have you ever been hit on at the gym?',
        membership_id: null,
        sequence: 33,
        seed_id: '1-33',
      },
      {
        content: 'Do you prefer training in the morning or afternoon?',
        membership_id: null,
        sequence: 34,
        seed_id: '1-34',
      },
      {
        content: 'I am available to help others in this community with:',
        membership_id: null,
        sequence: 35,
        seed_id: '1-35',
      },
      {
        content: 'What is one biohack that has changed your life?',
        membership_id: null,
        sequence: 36,
        seed_id: '1-36',
      },
      {
        content: 'Who is someone that has inspired you in your health journey?',
        membership_id: null,
        sequence: 37,
        seed_id: '1-37',
      },
      {
        content: 'Do you have an evening ritual?',
        membership_id: null,
        sequence: 38,
        seed_id: '1-38',
      },
      {
        content: 'What is your favorite post workout meal?',
        membership_id: null,
        sequence: 39,
        seed_id: '1-39',
      },
      {
        content: 'What limiting belief is holding you back the most?',
        membership_id: null,
        sequence: 40,
        seed_id: '1-40',
      },
      {
        content: 'What is a new skill you want to learn?',
        membership_id: null,
        sequence: 41,
        seed_id: '1-41',
      },
      {
        content: 'On a scale of 1-10 how would you rate your week?',
        membership_id: null,
        sequence: 42,
        seed_id: '1-42',
      },
      {
        content: 'If you could train with anyone who would it be?',
        membership_id: null,
        sequence: 43,
        seed_id: '1-43',
      },
      {
        content: 'What gym do you want to visit and why?',
        membership_id: null,
        sequence: 44,
        seed_id: '1-44',
      },
      {
        content: 'What helps you feel better when you are upset or stressed?',
        membership_id: null,
        sequence: 45,
        seed_id: '1-45',
      },
      {
        content: 'What is the best thing about being your age?',
        membership_id: null,
        sequence: 46,
        seed_id: '1-46',
      },
      {
        content: 'What is the hardest workout you have ever done?',
        membership_id: null,
        sequence: 47,
        seed_id: '1-47',
      },
      {
        content: 'What mark do you hope to leave on this community?',
        membership_id: null,
        sequence: 48,
        seed_id: '1-48',
      },
      {
        content: 'If you could do anything in the next year, what would it be?',
        membership_id: null,
        sequence: 49,
        seed_id: '1-49',
      },
      {
        content: 'What is your favorite quote?',
        membership_id: null,
        sequence: 50,
        seed_id: '1-50',
      },
      {
        content: 'What is something you needed to hear today?',
        membership_id: null,
        sequence: 51,
        seed_id: '1-51',
      },
      {
        content: 'How motivated do you feel to work on your health this week?',
        membership_id: null,
        sequence: 52,
        seed_id: '1-52',
      },
      {
        content: 'What is your favorite cheat meal?',
        membership_id: null,
        sequence: 53,
        seed_id: '1-53',
      },
      {
        content: 'What is your favorite way to rest and relax?',
        membership_id: null,
        sequence: 54,
        seed_id: '1-54',
      },
      {
        content: 'What is something you think everyone should do once in their lives?',
        membership_id: null,
        sequence: 55,
        seed_id: '1-55',
      },
      {
        content: 'What do you believe to be your greatest strength?',
        membership_id: null,
        sequence: 56,
        seed_id: '1-56',
      },
      {
        content: 'What do you believe to be your greatest weakness?',
        membership_id: null,
        sequence: 57,
        seed_id: '1-57',
      },
      {
        content: 'What is a mistake you made and learned from?',
        membership_id: null,
        sequence: 58,
        seed_id: '1-58',
      },
      {
        content: 'What is the best advice you have ever received?',
        membership_id: null,
        sequence: 59,
        seed_id: '1-59',
      },
      {
        content: 'What is the best thing about this community?',
        membership_id: null,
        sequence: 60,
        seed_id: '1-60',
      },
      {
        content: 'What do you think is the most attractive body part?',
        membership_id: null,
        sequence: 61,
        seed_id: '1-61',
      },
      {
        content: 'What is one health tip you use when you get sick?',
        membership_id: null,
        sequence: 62,
        seed_id: '1-62',
      },
      {
        content: 'What is your relationship with unhealthy food?',
        membership_id: null,
        sequence: 63,
        seed_id: '1-63',
      },
      {
        content: 'If you could eat only one meal everyday for the rest of your life what would it be?',
        membership_id: null,
        sequence: 64,
        seed_id: '1-64',
      },
      {
        content: 'If you could instantly be an expert in a subject, what would it be?',
        membership_id: null,
        sequence: 65,
        seed_id: '1-65',
      },
      {
        content: 'If you could have any superpower what would it be?',
        membership_id: null,
        sequence: 66,
        seed_id: '1-66',
      },
      {
        content: 'If you could have one celebrity as your training partner who would it be?',
        membership_id: null,
        sequence: 67,
        seed_id: '1-67',
      },
      {
        content: 'If you could compete in the Olympics, which sport would it be for?',
        membership_id: null,
        sequence: 68,
        seed_id: '1-68',
      },
      {
        content: 'If you could arm wrestle a historical figure, who would it be?',
        membership_id: null,
        sequence: 69,
        seed_id: '1-69',
      },
      {
        content: 'If you could pick up a skill instantly, what would it be?',
        membership_id: null,
        sequence: 70,
        seed_id: '1-70',
      },
      {
        content: 'What is the best product you bought for less than $100',
        membership_id: null,
        sequence: 71,
        seed_id: '1-71',
      },
      {
        content: 'Who in your life inspires you to be better?',
        membership_id: null,
        sequence: 72,
        seed_id: '1-72',
      },
      {
        content: 'What one accomplishment are you most proud of?',
        membership_id: null,
        sequence: 73,
        seed_id: '1-73',
      },
      {
        content: 'What book has made the biggest impact on your health journey?',
        membership_id: null,
        sequence: 74,
        seed_id: '1-74',
      },
      {
        content: 'What is something about health you believed earlier but think about differently?',
        membership_id: null,
        sequence: 75,
        seed_id: '1-75',
      },
      {
        content: 'What important health truth do very few people agree on?',
        membership_id: null,
        sequence: 76,
        seed_id: '1-76',
      },
      {
        content: 'What is your favorite pump up song?',
        membership_id: null,
        sequence: 77,
        seed_id: '1-77',
      },
      {
        content: 'What is one of your gym pet peeves?',
        membership_id: null,
        sequence: 78,
        seed_id: '1-78',
      },
      {
        content: 'What is a health hack you tried but will never do again?',
        membership_id: null,
        sequence: 79,
        seed_id: '1-79',
      },
      {
        content: 'What is your worst habit currently?',
        membership_id: null,
        sequence: 80,
        seed_id: '1-80',
      },
      {
        content: 'What is the best advice you ever received?',
        membership_id: null,
        sequence: 81,
        seed_id: '1-81',
      },
      {
        content: 'Who are three people you want on your team during a zombie apocalypse?',
        membership_id: null,
        sequence: 82,
        seed_id: '1-82',
      },
      {
        content: 'What is one tip you have learned for staying healthy while traveling?',
        membership_id: null,
        sequence: 83,
        seed_id: '1-83',
      },
      {
        content: 'What is one tip you have learned for staying healthy when eating out at restaurants?',
        membership_id: null,
        sequence: 84,
        seed_id: '1-84',
      },
      {
        content: 'Share an embarrassing gym story?',
        membership_id: null,
        sequence: 85,
        seed_id: '1-85',
      },
      {
        content: 'What activity do you love so much that it makes you lose track of time?',
        membership_id: null,
        sequence: 86,
        seed_id: '1-86',
      },
      {
        content: 'What did you want to be when you grew up?',
        membership_id: null,
        sequence: 87,
        seed_id: '1-87',
      },
      {
        content: 'What stereotype do you completely live up to?',
        membership_id: null,
        sequence: 88,
        seed_id: '1-88',
      },
      {
        content: 'If you had to relive the same day of your life, which day would you choose?',
        membership_id: null,
        sequence: 89,
        seed_id: '1-89',
      },
      {
        content: 'What is your most used emoji?',
        membership_id: null,
        sequence: 90,
        seed_id: '1-90',
      },
      {
        content: 'What is an unexpected win you experienced this week?',
        membership_id: null,
        sequence: 91,
        seed_id: '1-91',
      },
      {
        content: 'What does it mean for you to be successful?',
        membership_id: null,
        sequence: 92,
        seed_id: '1-92',
      },
      {
        content: 'What is the best compliment you have ever received?',
        membership_id: null,
        sequence: 93,
        seed_id: '1-93',
      },
      {
        content: 'Which three words best describe you?',
        membership_id: null,
        sequence: 94,
        seed_id: '1-94',
      },
      {
        content: 'The most attractive quality a person can possess is:',
        membership_id: null,
        sequence: 95,
        seed_id: '1-95',
      },
      {
        content: 'What lesson do you want to teach your grandchildren?',
        membership_id: null,
        sequence: 96,
        seed_id: '1-96',
      },
      {
        content: 'What is something you think everyone should do once in their lives?',
        membership_id: null,
        sequence: 97,
        seed_id: '1-97',
      },
      {
        content: 'What are your three favorite books and why?',
        membership_id: null,
        sequence: 98,
        seed_id: '1-98',
      },
    ],
  },
  {
    name: 'Business',
    slug: 'business',
    icon: '💼',
    description:
      'This question pack will have your members spilling the tea on their business and personal development insights.',
    seed_id: '2',
    spark_questions: [
      {
        content: 'What sparked you to join this community?',
        membership_id: null,
        sequence: 0,
        seed_id: '2-0',
      },
      {
        content: 'What do you hope to achieve from joining this community?',
        membership_id: null,
        sequence: 1,
        seed_id: '2-1',
      },
      {
        content: 'Share one fun fact about where you live?',
        membership_id: null,
        sequence: 2,
        seed_id: '2-2',
      },
      {
        content: 'Who would you like to connect with in this community?',
        membership_id: null,
        sequence: 3,
        seed_id: '2-3',
      },
      {
        content: 'Describe yourself in one sentence:',
        membership_id: null,
        sequence: 4,
        seed_id: '2-4',
      },
      {
        content: 'Is there anything specific you are looking for support with?',
        membership_id: null,
        sequence: 5,
        seed_id: '2-5',
      },
      {
        content: 'What do you need most right now?',
        membership_id: null,
        sequence: 6,
        seed_id: '2-6',
      },
      {
        content: 'Something that could stop me getting the most out of this community is:',
        membership_id: null,
        sequence: 7,
        seed_id: '2-7',
      },
      {
        content: '6 months from now, my business will be:',
        membership_id: null,
        sequence: 8,
        seed_id: '2-8',
      },
      {
        content: "Share the best business advice you've ever been given:",
        membership_id: null,
        sequence: 9,
        seed_id: '2-9',
      },
      {
        content: 'Share your best elevator pitch below:',
        membership_id: null,
        sequence: 10,
        seed_id: '2-10',
      },
      {
        content: 'What limiting belief holds you back the most?',
        membership_id: null,
        sequence: 11,
        seed_id: '2-11',
      },
      {
        content: "What's something you've implemented in your business that's had a huge impact?",
        membership_id: null,
        sequence: 12,
        seed_id: '2-12',
      },
      {
        content: 'What book has influenced your business decisions the most & why?',
        membership_id: null,
        sequence: 13,
        seed_id: '2-13',
      },
      {
        content: 'Share why your business needs to exist below:',
        membership_id: null,
        sequence: 14,
        seed_id: '2-14',
      },
      {
        content: 'What do you need the most help with in your business right now?',
        membership_id: null,
        sequence: 15,
        seed_id: '2-15',
      },
      {
        content: "Best marketing advice you've ever been given:",
        membership_id: null,
        sequence: 16,
        seed_id: '2-16',
      },
      {
        content: 'A pivotal moment in my business has been:',
        membership_id: null,
        sequence: 17,
        seed_id: '2-17',
      },
      {
        content: 'Everything changed in my business when I realized:',
        membership_id: null,
        sequence: 18,
        seed_id: '2-18',
      },
      {
        content: 'Describe the ideal client/customer for your business:',
        membership_id: null,
        sequence: 19,
        seed_id: '2-19',
      },
      {
        content: "What's one thing you can do today to move the needle forward in your business?",
        membership_id: null,
        sequence: 20,
        seed_id: '2-20',
      },
      {
        content: "I'm available to help others in this community with:",
        membership_id: null,
        sequence: 21,
        seed_id: '2-21',
      },
      {
        content: "I'd love to connect with other business owners who:",
        membership_id: null,
        sequence: 22,
        seed_id: '2-22',
      },
      {
        content: 'My biggest insecurity in business is:',
        membership_id: null,
        sequence: 23,
        seed_id: '2-23',
      },
      {
        content: "Share a recent win that you're proud of yourself for:",
        membership_id: null,
        sequence: 24,
        seed_id: '2-24',
      },
      {
        content: "Is there anything you've been too scared to ask for help with, but know you should?",
        membership_id: null,
        sequence: 25,
        seed_id: '2-25',
      },
      { content: 'My business superpower is:', membership_id: null, sequence: 26, seed_id: '2-26' },
      {
        content: 'What I look for in a business partner is:',
        membership_id: null,
        sequence: 27,
        seed_id: '2-27',
      },
      {
        content: 'Are you currently hiring / available for hire? Share below:',
        membership_id: null,
        sequence: 28,
        seed_id: '2-28',
      },
      {
        content: '5 years from now, what are you celebrating?',
        membership_id: null,
        sequence: 29,
        seed_id: '2-29',
      },
      {
        content: 'How do you feel the economy is impacting different industries right now?',
        membership_id: null,
        sequence: 30,
        seed_id: '2-30',
      },
      {
        content: 'What is a mistake you made and learned from?',
        membership_id: null,
        sequence: 31,
        seed_id: '2-31',
      },
      {
        content: 'What is the best thing about this community?',
        membership_id: null,
        sequence: 32,
        seed_id: '2-32',
      },
      { content: 'What was your first job?', membership_id: null, sequence: 33, seed_id: '2-33' },
      {
        content: 'If you could ask for help with one thing today, what would that be?',
        membership_id: null,
        sequence: 34,
        seed_id: '2-34',
      },
      {
        content: 'What is your favorite quote?',
        membership_id: null,
        sequence: 35,
        seed_id: '2-35',
      },
      {
        content: 'What is your dream career if money did not matter?',
        membership_id: null,
        sequence: 36,
        seed_id: '2-36',
      },
      {
        content: 'What is one skill you are committed to learning over the next year?',
        membership_id: null,
        sequence: 37,
        seed_id: '2-37',
      },
      {
        content: 'What mark do you hope to leave on this community?',
        membership_id: null,
        sequence: 38,
        seed_id: '2-38',
      },
      {
        content: 'What does it mean for you to be successful?',
        membership_id: null,
        sequence: 39,
        seed_id: '2-39',
      },
      {
        content: 'What is the hardest thing you have ever done in business?',
        membership_id: null,
        sequence: 40,
        seed_id: '2-40',
      },
      {
        content: 'If you made 1 million dollars in revenue today what would you do?',
        membership_id: null,
        sequence: 41,
        seed_id: '2-41',
      },
      {
        content: 'What types of connections are you looking for in your life?',
        membership_id: null,
        sequence: 42,
        seed_id: '2-42',
      },
      {
        content: 'What is one thing you can do today to be a better leader?',
        membership_id: null,
        sequence: 43,
        seed_id: '2-43',
      },
      {
        content: "Best career advice you've ever received:",
        membership_id: null,
        sequence: 44,
        seed_id: '2-44',
      },
      {
        content: 'Describe your perfect day from the time you wake up to the time you go to sleep:',
        membership_id: null,
        sequence: 45,
        seed_id: '2-45',
      },
      {
        content: "What's the most costly mistake you've made in business:",
        membership_id: null,
        sequence: 46,
        seed_id: '2-46',
      },
      {
        content: 'Something I could use more clarity on is:',
        membership_id: null,
        sequence: 47,
        seed_id: '2-47',
      },
      {
        content: "I feel I'd be able to make more progress in my business if I had:",
        membership_id: null,
        sequence: 48,
        seed_id: '2-48',
      },
      {
        content: 'Have you allowed yourself to rest this week? If so, what did you do?',
        membership_id: null,
        sequence: 49,
        seed_id: '2-49',
      },
      {
        content: 'Who is one creator whose content has had a positive impact on your business?',
        membership_id: null,
        sequence: 50,
        seed_id: '2-50',
      },
      {
        content: 'The area I have the most room to grow in is:',
        membership_id: null,
        sequence: 51,
        seed_id: '2-51',
      },
      {
        content: 'Who is your favorite business leader and why?',
        membership_id: null,
        sequence: 52,
        seed_id: '2-52',
      },
      {
        content: "Describe something that's exciting in your business right now:",
        membership_id: null,
        sequence: 53,
        seed_id: '2-53',
      },
      {
        content: 'What is your hidden superpower?',
        membership_id: null,
        sequence: 54,
        seed_id: '2-54',
      },
      {
        content: 'I feel most supported by my peers when:',
        membership_id: null,
        sequence: 55,
        seed_id: '2-55',
      },
      {
        content: 'If you could instantly become an expert in a given field, what field would you choose and why?',
        membership_id: null,
        sequence: 56,
        seed_id: '2-56',
      },
      {
        content: 'Did you go to college? If so, did you find that it helped you in your career?',
        membership_id: null,
        sequence: 57,
        seed_id: '2-57',
      },
      {
        content: "What's the largest amount of money you've ever made in one day?",
        membership_id: null,
        sequence: 58,
        seed_id: '2-58',
      },
      {
        content: "What's your relationship with money like?",
        membership_id: null,
        sequence: 59,
        seed_id: '2-59',
      },
      {
        content: 'One thing that could improve this community is:',
        membership_id: null,
        sequence: 60,
        seed_id: '2-60',
      },
      {
        content: "Share a story of when you've been the most embarrassed in your career:",
        membership_id: null,
        sequence: 61,
        seed_id: '2-61',
      },
      {
        content: 'Share your businesses mission statement below:',
        membership_id: null,
        sequence: 62,
        seed_id: '2-62',
      },
      {
        content: 'Do you have a skill you think this community could benefit from? Share below:',
        membership_id: null,
        sequence: 63,
        seed_id: '2-63',
      },
      {
        content: 'What do you need to make you feel confident you will achieve your goals?',
        membership_id: null,
        sequence: 64,
        seed_id: '2-64',
      },
      {
        content: "What are the apps / tools you can't live without:",
        membership_id: null,
        sequence: 65,
        seed_id: '2-65',
      },
      { content: "The reason I'm here is to:", membership_id: null, sequence: 66, seed_id: '2-66' },
      {
        content: "What's your go to work playlist:",
        membership_id: null,
        sequence: 67,
        seed_id: '2-67',
      },
      {
        content: 'Something you could do all day everyday in business is:',
        membership_id: null,
        sequence: 68,
        seed_id: '2-68',
      },
      {
        content: "Have you been holding on to a secret you'd like to get off your chest?",
        membership_id: null,
        sequence: 69,
        seed_id: '2-69',
      },
      {
        content: 'Looking at social media makes you feel:',
        membership_id: null,
        sequence: 70,
        seed_id: '2-70',
      },
      {
        content: 'Shamelessly plug your business below:',
        membership_id: null,
        sequence: 71,
        seed_id: '2-71',
      },
      {
        content: 'Have you hit your goals this year? Why / why not?',
        membership_id: null,
        sequence: 72,
        seed_id: '2-72',
      },
      {
        content: "What's something you can do to take care of yourself today?",
        membership_id: null,
        sequence: 73,
        seed_id: '2-73',
      },
      {
        content: "Someone I'm grateful for today is:",
        membership_id: null,
        sequence: 74,
        seed_id: '2-74',
      },
      {
        content: 'How do you stay motivated when you feel like quitting?',
        membership_id: null,
        sequence: 75,
        seed_id: '2-75',
      },
      {
        content: "What's something you previously believed about business but have changed your mind about?",
        membership_id: null,
        sequence: 76,
        seed_id: '2-76',
      },
      {
        content: "Tell a story of a time where you've felt as though you failed:",
        membership_id: null,
        sequence: 77,
        seed_id: '2-77',
      },
      {
        content: 'Who/what made you take an interest in business?',
        membership_id: null,
        sequence: 78,
        seed_id: '2-78',
      },
      {
        content: 'What do you feel is more important: a great team, or great ideas?',
        membership_id: null,
        sequence: 79,
        seed_id: '2-79',
      },
      {
        content: 'What is your guilty pleasure?',
        membership_id: null,
        sequence: 80,
        seed_id: '2-80',
      },
      {
        content: 'Who do you turn to when you need business advice?',
        membership_id: null,
        sequence: 81,
        seed_id: '2-81',
      },
      {
        content: 'The most important quality I look for in a team member is:',
        membership_id: null,
        sequence: 82,
        seed_id: '2-82',
      },
      {
        content: 'What are your 3 favorite books and why?',
        membership_id: null,
        sequence: 83,
        seed_id: '2-83',
      },
      {
        content: "How do you hope your peers would describe you when you're not in the room?",
        membership_id: null,
        sequence: 84,
        seed_id: '2-84',
      },
      {
        content: "Do you feel it's important to ignore the competition in your industry or study them?",
        membership_id: null,
        sequence: 85,
        seed_id: '2-85',
      },
      {
        content: "One hack that's changed your life is:",
        membership_id: null,
        sequence: 86,
        seed_id: '2-86',
      },
      {
        content: "What's your morning routine?",
        membership_id: null,
        sequence: 87,
        seed_id: '2-87',
      },
      {
        content: "Someone I'm inspired by in business is:",
        membership_id: null,
        sequence: 88,
        seed_id: '2-88',
      },
      {
        content: 'If you could have lunch with anyone dead or alive, who would it be and why?',
        membership_id: null,
        sequence: 89,
        seed_id: '2-89',
      },
      {
        content: 'I feel most productive when:',
        membership_id: null,
        sequence: 90,
        seed_id: '2-90',
      },
      {
        content: "What's something you've been putting off that you know you need to address?",
        membership_id: null,
        sequence: 91,
        seed_id: '2-91',
      },
      {
        content: 'What excites you most about your career is:',
        membership_id: null,
        sequence: 92,
        seed_id: '2-92',
      },
      {
        content: "What helps you relax when you're stressed or upset?",
        membership_id: null,
        sequence: 93,
        seed_id: '2-93',
      },
      {
        content: 'What movie gets you hyped to conquer the world?',
        membership_id: null,
        sequence: 94,
        seed_id: '2-94',
      },
      {
        content: "What's a dream you hold that you haven't shared with the world yet?",
        membership_id: null,
        sequence: 95,
        seed_id: '2-95',
      },
      {
        content: 'What habits get in the way of you reaching your full potential?',
        membership_id: null,
        sequence: 96,
        seed_id: '2-96',
      },
      {
        content: 'Something I love about this community is:',
        membership_id: null,
        sequence: 97,
        seed_id: '2-97',
      },
    ],
  },
  {
    name: 'Finance',
    slug: 'finance',
    icon: '💰',
    description:
      'Activate this question pack to help your members spark meaningful conversations around finance and building wealth.',
    seed_id: '3',
    spark_questions: [
      {
        content: 'What sparked you to join this community?',
        membership_id: null,
        sequence: 0,
        seed_id: '3-0',
      },
      {
        content: 'What do you hope to achieve from joining this community?',
        membership_id: null,
        sequence: 1,
        seed_id: '3-1',
      },
      {
        content: 'Share one fun fact about where you live?',
        membership_id: null,
        sequence: 2,
        seed_id: '3-2',
      },
      {
        content: 'Who would you like to connect with in this community?',
        membership_id: null,
        sequence: 3,
        seed_id: '3-3',
      },
      {
        content: 'Describe yourself in one sentence:',
        membership_id: null,
        sequence: 4,
        seed_id: '3-4',
      },
      {
        content: 'Is there anything specific you are looking for support with?',
        membership_id: null,
        sequence: 5,
        seed_id: '3-5',
      },
      {
        content: 'What do you need most right now?',
        membership_id: null,
        sequence: 6,
        seed_id: '3-6',
      },
      {
        content: 'Something that could stop me getting the most out of this community is:',
        membership_id: null,
        sequence: 7,
        seed_id: '3-7',
      },
      {
        content: '6 months from now, my financial situation will be:',
        membership_id: null,
        sequence: 8,
        seed_id: '3-8',
      },
      {
        content: 'What limiting belief holds you back the most?',
        membership_id: null,
        sequence: 9,
        seed_id: '3-9',
      },
      {
        content: "Best finance advice you've ever been given?",
        membership_id: null,
        sequence: 10,
        seed_id: '3-10',
      },
      {
        content: "I'd love to connect with others who:",
        membership_id: null,
        sequence: 11,
        seed_id: '3-11',
      },
      {
        content: 'What does it mean to you to be successful?',
        membership_id: null,
        sequence: 12,
        seed_id: '3-12',
      },
      {
        content: 'If you received a check for 1 million dollars today, what would you do with it?',
        membership_id: null,
        sequence: 13,
        seed_id: '3-13',
      },
      {
        content: "Best money advice you've ever received?",
        membership_id: null,
        sequence: 14,
        seed_id: '3-14',
      },
      {
        content: "What's your relationship with money like?",
        membership_id: null,
        sequence: 15,
        seed_id: '3-15',
      },
      {
        content: 'What did you hear about money when you were growing up?',
        membership_id: null,
        sequence: 16,
        seed_id: '3-16',
      },
      { content: "The reason I'm here is to:", membership_id: null, sequence: 17, seed_id: '3-17' },
      {
        content: 'Have you hit your financial goals this year? Why / why not?',
        membership_id: null,
        sequence: 18,
        seed_id: '3-18',
      },
      {
        content: 'What habits get in the way of you reaching your full potential?',
        membership_id: null,
        sequence: 19,
        seed_id: '3-19',
      },
      {
        content: 'What limiting beliefs do you hold about money?',
        membership_id: null,
        sequence: 20,
        seed_id: '3-20',
      },
      {
        content: 'What does financial freedom mean to you?',
        membership_id: null,
        sequence: 21,
        seed_id: '3-21',
      },
      {
        content: "What's your favorite asset class to invest in?",
        membership_id: null,
        sequence: 22,
        seed_id: '3-22',
      },
      {
        content: 'What is your biggest financial fear?',
        membership_id: null,
        sequence: 23,
        seed_id: '3-23',
      },
      {
        content: "What's your desired annual income and why?",
        membership_id: null,
        sequence: 24,
        seed_id: '3-24',
      },
      {
        content: 'Do you know the cost of your dream lifestyle?',
        membership_id: null,
        sequence: 25,
        seed_id: '3-25',
      },
      {
        content: 'Do you openly discuss finances with your loved ones or choose to keep private?',
        membership_id: null,
        sequence: 26,
        seed_id: '3-26',
      },
      {
        content: "What's the best finance book you've ever read?",
        membership_id: null,
        sequence: 27,
        seed_id: '3-27',
      },
      {
        content: 'What is the main thing you want to learn in this community?',
        membership_id: null,
        sequence: 28,
        seed_id: '3-28',
      },
      {
        content: 'Would you rather be given 10M in cash or a business that makes 1M a year?',
        membership_id: null,
        sequence: 29,
        seed_id: '3-29',
      },
      {
        content: 'Was your family rich or poor as a child and how did that affect you?',
        membership_id: null,
        sequence: 30,
        seed_id: '3-30',
      },
      {
        content: 'How much money would you like to make to each month and why?',
        membership_id: null,
        sequence: 31,
        seed_id: '3-31',
      },
      {
        content: 'If you were given $50,000 and you had to spend on a material product, what would you buy?',
        membership_id: null,
        sequence: 32,
        seed_id: '3-32',
      },
      {
        content: 'What skills do you use to make money?',
        membership_id: null,
        sequence: 33,
        seed_id: '3-33',
      },
      {
        content: 'If you could learn one new skill to make money, what would it be?',
        membership_id: null,
        sequence: 34,
        seed_id: '3-34',
      },
      {
        content: 'What is your view on crypto?',
        membership_id: null,
        sequence: 35,
        seed_id: '3-35',
      },
      {
        content: 'If you had to invest all of your money into one asset, what would it be and why?',
        membership_id: null,
        sequence: 36,
        seed_id: '3-36',
      },
      {
        content: 'What advice would you give to your children about money?',
        membership_id: null,
        sequence: 37,
        seed_id: '3-37',
      },
      {
        content: 'How do you prefer to spend your money when you make it?',
        membership_id: null,
        sequence: 38,
        seed_id: '3-38',
      },
      {
        content: 'Who are your money role models?',
        membership_id: null,
        sequence: 39,
        seed_id: '3-39',
      },
      {
        content: 'What are you doing to invest more?',
        membership_id: null,
        sequence: 40,
        seed_id: '3-40',
      },
      {
        content: 'Would you rather buy free time or luxury products?',
        membership_id: null,
        sequence: 41,
        seed_id: '3-41',
      },
      {
        content: 'What is one thing you can do to increase your income?',
        membership_id: null,
        sequence: 42,
        seed_id: '3-42',
      },
      {
        content: 'What are your long term financial goals?',
        membership_id: null,
        sequence: 43,
        seed_id: '3-43',
      },
      {
        content: 'What are your short term financial goals (less than 12 months)?',
        membership_id: null,
        sequence: 44,
        seed_id: '3-44',
      },
      {
        content: 'What is your favorite book about finance?',
        membership_id: null,
        sequence: 45,
        seed_id: '3-45',
      },
      {
        content: 'What percentage of your income do you save every month?',
        membership_id: null,
        sequence: 46,
        seed_id: '3-46',
      },
      {
        content: 'Do you have an emergency fund and why?',
        membership_id: null,
        sequence: 47,
        seed_id: '3-47',
      },
      {
        content: 'Do you focus on building your credit score?',
        membership_id: null,
        sequence: 48,
        seed_id: '3-48',
      },
      {
        content: 'What age do you plan to retire?',
        membership_id: null,
        sequence: 49,
        seed_id: '3-49',
      },
      {
        content: 'What mindset blocks are in the way of you reaching your financial goals?',
        membership_id: null,
        sequence: 50,
        seed_id: '3-50',
      },
      {
        content: 'What are your favorite tools you use to organize your finances?',
        membership_id: null,
        sequence: 51,
        seed_id: '3-51',
      },
      {
        content: 'Do you worry about money and if so, why?',
        membership_id: null,
        sequence: 52,
        seed_id: '3-52',
      },
      {
        content: 'Do you automate or manually invest?',
        membership_id: null,
        sequence: 53,
        seed_id: '3-53',
      },
      {
        content: 'Do you have a financial advisor?',
        membership_id: null,
        sequence: 54,
        seed_id: '3-54',
      },
      {
        content: 'What do you do to save on taxes?',
        membership_id: null,
        sequence: 55,
        seed_id: '3-55',
      },
      {
        content: 'Do you enjoy your job or method of creating income?',
        membership_id: null,
        sequence: 56,
        seed_id: '3-56',
      },
      {
        content: 'Do you feel guilty when you spend money?',
        membership_id: null,
        sequence: 57,
        seed_id: '3-57',
      },
      {
        content: 'What was the last purchase you regret?',
        membership_id: null,
        sequence: 58,
        seed_id: '3-58',
      },
      {
        content: 'How often do you do a check in with your finances?',
        membership_id: null,
        sequence: 59,
        seed_id: '3-59',
      },
      {
        content: 'If you have a romantic partner, do you share finances or keep them seperate and why?',
        membership_id: null,
        sequence: 60,
        seed_id: '3-60',
      },
      {
        content: "What's your financial guilty pleasure?",
        membership_id: null,
        sequence: 61,
        seed_id: '3-61',
      },
      {
        content: 'Do you prefer to buy name brand or generic?',
        membership_id: null,
        sequence: 62,
        seed_id: '3-62',
      },
      {
        content: 'What is the largest purchase you made this year and was it worth it?',
        membership_id: null,
        sequence: 63,
        seed_id: '3-63',
      },
      {
        content: 'Are you a saver or a spender?',
        membership_id: null,
        sequence: 64,
        seed_id: '3-64',
      },
      {
        content: 'How many credit cards do you have and do you pay them off every month?',
        membership_id: null,
        sequence: 65,
        seed_id: '3-65',
      },
      {
        content: 'Do you live to work or work to live?',
        membership_id: null,
        sequence: 66,
        seed_id: '3-66',
      },
      {
        content: 'How much money do you need to have to feel wealthy?',
        membership_id: null,
        sequence: 67,
        seed_id: '3-67',
      },
      {
        content: 'What is your plan to fund your retirement?',
        membership_id: null,
        sequence: 68,
        seed_id: '3-68',
      },
      {
        content: 'What do you want to make monthly to feel comfortable?',
        membership_id: null,
        sequence: 69,
        seed_id: '3-69',
      },
      {
        content: 'What is more important, job satisfaction or income?',
        membership_id: null,
        sequence: 70,
        seed_id: '3-70',
      },
      {
        content: 'What monthly expenses do you spend most on?',
        membership_id: null,
        sequence: 71,
        seed_id: '3-71',
      },
      {
        content: 'If you lost everything and had to start over, how would you make money?',
        membership_id: null,
        sequence: 72,
        seed_id: '3-72',
      },
      {
        content: 'What are your deepest fears about money?',
        membership_id: null,
        sequence: 73,
        seed_id: '3-73',
      },
      {
        content: 'Do you have any rituals you perform to improve your money mindset?',
        membership_id: null,
        sequence: 74,
        seed_id: '3-74',
      },
      {
        content: 'When do you feel most confident about your finances?',
        membership_id: null,
        sequence: 75,
        seed_id: '3-75',
      },
      {
        content: 'Do you talk to your romantic partner about money?',
        membership_id: null,
        sequence: 76,
        seed_id: '3-76',
      },
      {
        content: 'Do you prefer gifts or experiences?',
        membership_id: null,
        sequence: 77,
        seed_id: '3-77',
      },
      {
        content: 'Do you have high intererest debt and if so how are you planning to pay it off?',
        membership_id: null,
        sequence: 78,
        seed_id: '3-78',
      },
      {
        content: 'What is your dream vacation, explain in detail?',
        membership_id: null,
        sequence: 79,
        seed_id: '3-79',
      },
      {
        content: 'Would you rather have a fancy house to live in or investment properties that pay you?',
        membership_id: null,
        sequence: 80,
        seed_id: '3-80',
      },
      {
        content: 'What is one way you could improve your knowledge of finance?',
        membership_id: null,
        sequence: 81,
        seed_id: '3-81',
      },
      {
        content: 'What lessons about money were you taught in school?',
        membership_id: null,
        sequence: 82,
        seed_id: '3-82',
      },
      {
        content: 'Why are you focused on improving your financal status?',
        membership_id: null,
        sequence: 83,
        seed_id: '3-83',
      },
      {
        content: 'Would you rather be wealthy and unknown or broke and famous?',
        membership_id: null,
        sequence: 84,
        seed_id: '3-84',
      },
      {
        content: 'Do you find learning about finance boring or fun?',
        membership_id: null,
        sequence: 85,
        seed_id: '3-85',
      },
      {
        content: 'If you could give away $1M, what you do with it?',
        membership_id: null,
        sequence: 86,
        seed_id: '3-86',
      },
      {
        content: 'Would you rather make $1M per year doing nothing or $10M working 40 hours a week?',
        membership_id: null,
        sequence: 87,
        seed_id: '3-87',
      },
      {
        content: 'What is your favorite youtube channel about finance?',
        membership_id: null,
        sequence: 88,
        seed_id: '3-88',
      },
      {
        content: 'What is your favorite conspiracy theory about finance?',
        membership_id: null,
        sequence: 89,
        seed_id: '3-89',
      },
      {
        content: 'Do you beleive the government has your best financial interest in mind?',
        membership_id: null,
        sequence: 90,
        seed_id: '3-90',
      },
      {
        content: 'What is your favorite thing to spend money on and why?',
        membership_id: null,
        sequence: 91,
        seed_id: '3-91',
      },
      {
        content: 'What do you unconsciously spend money on?',
        membership_id: null,
        sequence: 92,
        seed_id: '3-92',
      },
      {
        content: 'How many times do you check your bank account to see how much money you have?',
        membership_id: null,
        sequence: 93,
        seed_id: '3-93',
      },
      {
        content: 'How risk tolerant do you think you are with money and why?',
        membership_id: null,
        sequence: 94,
        seed_id: '3-94',
      },
      {
        content: "If you could learn any skill to make money that you don't already have, what would it be?",
        membership_id: null,
        sequence: 95,
        seed_id: '3-95',
      },
      {
        content: 'What is more important to you, time or money?',
        membership_id: null,
        sequence: 96,
        seed_id: '3-96',
      },
      {
        content: 'If you found a $100 bill on the sidewalk, what would you do with it?',
        membership_id: null,
        sequence: 97,
        seed_id: '3-97',
      },
      {
        content: 'Do you prefer the feeling of making money or spending money?',
        membership_id: null,
        sequence: 98,
        seed_id: '3-98',
      },
      {
        content: 'If you could have lunch with anyone to ask questions about money, who would it be?',
        membership_id: null,
        sequence: 99,
        seed_id: '3-99',
      },
      {
        content: 'What is the most valuable skill to have when it comes to making money?',
        membership_id: null,
        sequence: 100,
        seed_id: '3-100',
      },
    ],
  },
  {
    name: 'Introspective',
    slug: 'introspective',
    icon: '💭',
    description:
      'Use this question pack to get your members to go deeper and spark conversations around vulnerable and introspective topics.',
    seed_id: '4',
    spark_questions: [
      { content: 'Am I using my time wisely?', membership_id: null, sequence: 0, seed_id: '4-0' },
      {
        content: 'Am I taking anything for granted?',
        membership_id: null,
        sequence: 1,
        seed_id: '4-1',
      },
      {
        content: 'Am I employing a healthy perspective?',
        membership_id: null,
        sequence: 2,
        seed_id: '4-2',
      },
      { content: 'Am I living true to myself?', membership_id: null, sequence: 3, seed_id: '4-3' },
      {
        content: 'How do you feel when you wake up in the morning?',
        membership_id: null,
        sequence: 4,
        seed_id: '4-4',
      },
      {
        content: 'How could you be kinder to yourself today?',
        membership_id: null,
        sequence: 5,
        seed_id: '4-5',
      },
      {
        content: 'What worries you about the future?',
        membership_id: null,
        sequence: 6,
        seed_id: '4-6',
      },
      {
        content: 'What matters most in your life?',
        membership_id: null,
        sequence: 7,
        seed_id: '4-7',
      },
      {
        content: 'What are you scared of most these days?',
        membership_id: null,
        sequence: 8,
        seed_id: '4-8',
      },
      {
        content: 'What dream have you given up on and why?',
        membership_id: null,
        sequence: 9,
        seed_id: '4-9',
      },
      {
        content: 'When was the last time a stranger made you smile?',
        membership_id: null,
        sequence: 10,
        seed_id: '4-10',
      },
      {
        content: 'When was the last time you pushed past your comfort zone?',
        membership_id: null,
        sequence: 11,
        seed_id: '4-11',
      },
      {
        content: 'When was the last time you failed at something?',
        membership_id: null,
        sequence: 12,
        seed_id: '4-12',
      },
      {
        content: 'Who has had the greatest impact on your life?',
        membership_id: null,
        sequence: 13,
        seed_id: '4-13',
      },
      {
        content: 'When do you feel most productive?',
        membership_id: null,
        sequence: 14,
        seed_id: '4-14',
      },
      {
        content: 'I could not live without...',
        membership_id: null,
        sequence: 15,
        seed_id: '4-15',
      },
      {
        content: 'What does unconditional love look like?',
        membership_id: null,
        sequence: 16,
        seed_id: '4-16',
      },
      {
        content: 'I really wish others knew this about me:',
        membership_id: null,
        sequence: 17,
        seed_id: '4-17',
      },
      {
        content: 'I feel most energized when...',
        membership_id: null,
        sequence: 18,
        seed_id: '4-18',
      },
      {
        content: 'What’s surprised you the most about your life or life in general?',
        membership_id: null,
        sequence: 19,
        seed_id: '4-19',
      },
      {
        content: 'What can you learn from your biggest mistakes?',
        membership_id: null,
        sequence: 20,
        seed_id: '4-20',
      },
      {
        content: 'What’s one topic you need to learn more about to help you live a more fulfilling life?',
        membership_id: null,
        sequence: 21,
        seed_id: '4-21',
      },
      {
        content: 'What qualities or traits do you most admire in others?',
        membership_id: null,
        sequence: 22,
        seed_id: '4-22',
      },
      {
        content: 'What are the three most important things to you?',
        membership_id: null,
        sequence: 23,
        seed_id: '4-23',
      },
      {
        content: 'What are the values that you hold nearest to your heart?',
        membership_id: null,
        sequence: 24,
        seed_id: '4-24',
      },
      {
        content: 'How is the “public you” different from the “private you”?',
        membership_id: null,
        sequence: 25,
        seed_id: '4-25',
      },
      {
        content: 'What do you want people to think and say about you?',
        membership_id: null,
        sequence: 26,
        seed_id: '4-26',
      },
      {
        content: 'Is it more important to be liked by others or to be yourself? Why?',
        membership_id: null,
        sequence: 27,
        seed_id: '4-27',
      },
      {
        content: 'What three things are you most proud of in your life to date?',
        membership_id: null,
        sequence: 28,
        seed_id: '4-28',
      },
      {
        content: 'What do you hope to achieve in life?',
        membership_id: null,
        sequence: 29,
        seed_id: '4-29',
      },
      {
        content: 'If you could accomplish only one thing before you died, what would it be?',
        membership_id: null,
        sequence: 30,
        seed_id: '4-30',
      },
      {
        content: 'What three things would you like to change most about yourself?',
        membership_id: null,
        sequence: 31,
        seed_id: '4-31',
      },
      { content: 'I do my best when...', membership_id: null, sequence: 32, seed_id: '4-32' },
      { content: 'I struggle when...', membership_id: null, sequence: 33, seed_id: '4-33' },
      { content: 'I am comfortable when...', membership_id: null, sequence: 34, seed_id: '4-34' },
      { content: 'I feel stress when...', membership_id: null, sequence: 35, seed_id: '4-35' },
      { content: 'I am courageous when...', membership_id: null, sequence: 36, seed_id: '4-36' },
      {
        content: 'One of the most important things I learned was...',
        membership_id: null,
        sequence: 37,
        seed_id: '4-37',
      },
      {
        content: 'I missed a great opportunity when...',
        membership_id: null,
        sequence: 38,
        seed_id: '4-38',
      },
      {
        content: 'One of my favorite memories is...',
        membership_id: null,
        sequence: 39,
        seed_id: '4-39',
      },
      {
        content: 'My toughest decisions involve...',
        membership_id: null,
        sequence: 40,
        seed_id: '4-40',
      },
      {
        content: 'Being myself is hard because...',
        membership_id: null,
        sequence: 41,
        seed_id: '4-41',
      },
      { content: 'I can be myself when...', membership_id: null, sequence: 42, seed_id: '4-42' },
      { content: 'I wish I were more...', membership_id: null, sequence: 43, seed_id: '4-43' },
      { content: 'I wish I could...', membership_id: null, sequence: 44, seed_id: '4-44' },
      {
        content: 'I wish I would regularly...',
        membership_id: null,
        sequence: 45,
        seed_id: '4-45',
      },
      { content: 'I wish I had...', membership_id: null, sequence: 46, seed_id: '4-46' },
      { content: 'I wish I knew...', membership_id: null, sequence: 47, seed_id: '4-47' },
      { content: 'I wish I felt...', membership_id: null, sequence: 48, seed_id: '4-48' },
      { content: 'I wish I saw...', membership_id: null, sequence: 49, seed_id: '4-49' },
      { content: 'I wish I thought...', membership_id: null, sequence: 50, seed_id: '4-50' },
      { content: 'Life should be about...', membership_id: null, sequence: 51, seed_id: '4-51' },
      {
        content: 'I am going to make my life about...',
        membership_id: null,
        sequence: 52,
        seed_id: '4-52',
      },
      {
        content: 'What battles have you fought and overcome in your life?',
        membership_id: null,
        sequence: 53,
        seed_id: '4-53',
      },
      {
        content: 'Which have been your best moments in life so far?',
        membership_id: null,
        sequence: 54,
        seed_id: '4-54',
      },
      {
        content: 'What have been your biggest mistakes? What have you learned from them?',
        membership_id: null,
        sequence: 55,
        seed_id: '4-55',
      },
      {
        content: 'Which past experiences are you most thankful for?',
        membership_id: null,
        sequence: 56,
        seed_id: '4-56',
      },
      {
        content: 'If you could turn back time, what would you do differently? Why?',
        membership_id: null,
        sequence: 57,
        seed_id: '4-57',
      },
      {
        content: 'What’s the most exciting thing you’ve ever experienced?',
        membership_id: null,
        sequence: 58,
        seed_id: '4-58',
      },
      {
        content: 'What’s the most spontaneous thing you’ve ever done? How did it make you feel?',
        membership_id: null,
        sequence: 59,
        seed_id: '4-59',
      },
      {
        content: 'When was the last time you threw caution to the wind?',
        membership_id: null,
        sequence: 60,
        seed_id: '4-60',
      },
      {
        content: 'What’s the bravest thing you’ve ever done?',
        membership_id: null,
        sequence: 61,
        seed_id: '4-61',
      },
      {
        content: 'What’s on your upside-down bucket list – what have you already accomplished that you’re proud of?',
        membership_id: null,
        sequence: 62,
        seed_id: '4-62',
      },
      {
        content: 'Which things felt important to you ten years ago that no longer matter to you now?',
        membership_id: null,
        sequence: 63,
        seed_id: '4-63',
      },
      {
        content: 'What’s your favorite childhood memory? Why does that one memory stick out the most?',
        membership_id: null,
        sequence: 64,
        seed_id: '4-64',
      },
      {
        content: 'What did you love most about your childhood home? Why was that?',
        membership_id: null,
        sequence: 65,
        seed_id: '4-65',
      },
      {
        content: 'If you had the opportunity, what would you tell your childhood self?',
        membership_id: null,
        sequence: 66,
        seed_id: '4-66',
      },
      {
        content: 'What encouragement would you give to your past self?',
        membership_id: null,
        sequence: 67,
        seed_id: '4-67',
      },
      {
        content: 'What season of life are you in right now?',
        membership_id: null,
        sequence: 68,
        seed_id: '4-68',
      },
      {
        content: 'What are your top priorities right now?',
        membership_id: null,
        sequence: 69,
        seed_id: '4-69',
      },
      {
        content: 'How do you feel when you first wake up?',
        membership_id: null,
        sequence: 70,
        seed_id: '4-70',
      },
      {
        content: 'What takes up the majority of your time?',
        membership_id: null,
        sequence: 71,
        seed_id: '4-71',
      },
      {
        content: 'What thoughts distract you when you’re trying to get to sleep?',
        membership_id: null,
        sequence: 72,
        seed_id: '4-72',
      },
      { content: 'Do you like your job?', membership_id: null, sequence: 73, seed_id: '4-73' },
      {
        content: 'How do you spend your spare time?',
        membership_id: null,
        sequence: 74,
        seed_id: '4-74',
      },
      {
        content: 'How would you like to spend your spare time?',
        membership_id: null,
        sequence: 75,
        seed_id: '4-75',
      },
      {
        content: 'Which aspects of your life are you able to control?',
        membership_id: null,
        sequence: 76,
        seed_id: '4-76',
      },
      {
        content: 'What in your life aren’t you able to control? What can you do about that?',
        membership_id: null,
        sequence: 77,
        seed_id: '4-77',
      },
      {
        content: 'Which three things are more important to you than anything else?',
        membership_id: null,
        sequence: 78,
        seed_id: '4-78',
      },
      {
        content: 'How do you feel about this last week/month/year?',
        membership_id: null,
        sequence: 79,
        seed_id: '4-79',
      },
      {
        content: 'What were your biggest lessons from this last week/month/year?',
        membership_id: null,
        sequence: 80,
        seed_id: '4-80',
      },
      {
        content: 'What are your proudest moments from this last week/month/year?',
        membership_id: null,
        sequence: 81,
        seed_id: '4-81',
      },
      {
        content: 'How have you stepped out of your comfort zone this week/month/year?',
        membership_id: null,
        sequence: 82,
        seed_id: '4-82',
      },
      {
        content: 'How can you free up your calendar this week/month/year?',
        membership_id: null,
        sequence: 83,
        seed_id: '4-83',
      },
      {
        content: 'What’s going to bring you joy this week/month/year?',
        membership_id: null,
        sequence: 84,
        seed_id: '4-84',
      },
      {
        content: 'What aspects of life are you loving most right now?',
        membership_id: null,
        sequence: 85,
        seed_id: '4-85',
      },
      {
        content: 'What does your ideal/dream life look like?',
        membership_id: null,
        sequence: 86,
        seed_id: '4-86',
      },
      {
        content: 'What does your ideal/dream home look like?',
        membership_id: null,
        sequence: 87,
        seed_id: '4-87',
      },
      {
        content: 'If you continue doing what you’re doing right now, where will you be in five years time?',
        membership_id: null,
        sequence: 88,
        seed_id: '4-88',
      },
      {
        content: 'What’s on your bucket list?',
        membership_id: null,
        sequence: 89,
        seed_id: '4-89',
      },
      {
        content: 'What do you see yourself doing work-wise in five years time?',
        membership_id: null,
        sequence: 90,
        seed_id: '4-90',
      },
      {
        content: 'If you had the chance, what would you tell your future self?',
        membership_id: null,
        sequence: 91,
        seed_id: '4-91',
      },
      {
        content: 'If you only had one year left to live, what would you do?',
        membership_id: null,
        sequence: 92,
        seed_id: '4-92',
      },
      {
        content: 'When telling your grandchildren about your life, what would you like to say?',
        membership_id: null,
        sequence: 93,
        seed_id: '4-93',
      },
      {
        content: 'If you met your future self, what advice do you think they would share with you?',
        membership_id: null,
        sequence: 94,
        seed_id: '4-94',
      },
      {
        content: 'What advice or encouragement would you give to your future self?',
        membership_id: null,
        sequence: 95,
        seed_id: '4-95',
      },
      {
        content: 'If you had more time to do what you love, what would you do?',
        membership_id: null,
        sequence: 96,
        seed_id: '4-96',
      },
      {
        content: 'What makes you feel optimistic about the future?',
        membership_id: null,
        sequence: 97,
        seed_id: '4-97',
      },
      {
        content: 'Are you as confident as you would like? How can you improve your self-confidence?',
        membership_id: null,
        sequence: 98,
        seed_id: '4-98',
      },
    ],
  },
  {
    name: 'Relationships',
    slug: 'relationships',
    icon: '💜',
    description:
      'This question pack will spark conversation around what maters most, people. Help your members peel back the layers on the relationships in their life.',
    seed_id: '5',
    spark_questions: [
      {
        content: 'What sparked you to join this community?',
        membership_id: null,
        sequence: 0,
        seed_id: '5-0',
      },
      {
        content: 'What do you hope to achieve from joining this community?',
        membership_id: null,
        sequence: 1,
        seed_id: '5-1',
      },
      {
        content: 'Share one fun fact about where you live?',
        membership_id: null,
        sequence: 2,
        seed_id: '5-2',
      },
      {
        content: 'Who would you like to connect with in this community?',
        membership_id: null,
        sequence: 3,
        seed_id: '5-3',
      },
      {
        content: 'Describe yourself in one sentence:',
        membership_id: null,
        sequence: 4,
        seed_id: '5-4',
      },
      {
        content: 'Is there anything specific you are looking for support with?',
        membership_id: null,
        sequence: 5,
        seed_id: '5-5',
      },
      {
        content: 'What do you need most right now?',
        membership_id: null,
        sequence: 6,
        seed_id: '5-6',
      },
      {
        content: 'What does your partner do that turns you on most?',
        membership_id: null,
        sequence: 7,
        seed_id: '5-7',
      },
      {
        content: 'What is your favorite sexual memory?',
        membership_id: null,
        sequence: 8,
        seed_id: '5-8',
      },
      {
        content: 'What is the hardiest relationship decision you have ever made?',
        membership_id: null,
        sequence: 9,
        seed_id: '5-9',
      },
      {
        content: 'What relationship did you learn the most from?',
        membership_id: null,
        sequence: 10,
        seed_id: '5-10',
      },
      {
        content: 'How many times do you prefer to be intimate each week?',
        membership_id: null,
        sequence: 11,
        seed_id: '5-11',
      },
      {
        content: 'What type of power dynamics excite you?',
        membership_id: null,
        sequence: 12,
        seed_id: '5-12',
      },
      {
        content: 'If you wanted to do something sexual that is not considered "normal" what would it be?',
        membership_id: null,
        sequence: 13,
        seed_id: '5-13',
      },
      {
        content: 'Do you regret sleeping with anyone and why?',
        membership_id: null,
        sequence: 14,
        seed_id: '5-14',
      },
      {
        content: 'If you had a magic wand, what would your perfect partner be like?',
        membership_id: null,
        sequence: 15,
        seed_id: '5-15',
      },
      {
        content: 'If you could date any celebrity for a weekend, who would it be?',
        membership_id: null,
        sequence: 16,
        seed_id: '5-16',
      },
      {
        content: 'How has your childhood shaped you sexually?',
        membership_id: null,
        sequence: 17,
        seed_id: '5-17',
      },
      {
        content: 'How would your partner describe you to other people?',
        membership_id: null,
        sequence: 18,
        seed_id: '5-18',
      },
      { content: 'Share a funny sex story:', membership_id: null, sequence: 19, seed_id: '5-19' },
      { content: 'Describe your first kiss:', membership_id: null, sequence: 20, seed_id: '5-20' },
      {
        content: 'What is the quickest way your partner can turn you off?',
        membership_id: null,
        sequence: 21,
        seed_id: '5-21',
      },
      {
        content: 'What are some ways you stay intimate with your partner when life gets busy?',
        membership_id: null,
        sequence: 22,
        seed_id: '5-22',
      },
      {
        content: 'What is your favorite way to receive affection?',
        membership_id: null,
        sequence: 23,
        seed_id: '5-23',
      },
      {
        content: 'How do you experience love?',
        membership_id: null,
        sequence: 24,
        seed_id: '5-24',
      },
      {
        content: 'How do you feel about public displays of affection?',
        membership_id: null,
        sequence: 25,
        seed_id: '5-25',
      },
      {
        content: 'What are some personal relationship rules you refuse to break?',
        membership_id: null,
        sequence: 26,
        seed_id: '5-26',
      },
      {
        content: 'What do you believe to be your greatest strength in your relationship?',
        membership_id: null,
        sequence: 27,
        seed_id: '5-27',
      },
      {
        content: 'What do you believe to be your greatest weakness in your relationship?',
        membership_id: null,
        sequence: 28,
        seed_id: '5-28',
      },
      {
        content: 'What relationship book has made the biggest impact in your life?',
        membership_id: null,
        sequence: 29,
        seed_id: '5-29',
      },
      {
        content: 'What relationship on social media do you admire most and why?',
        membership_id: null,
        sequence: 30,
        seed_id: '5-30',
      },
      {
        content: 'How does your partner experience love?',
        membership_id: null,
        sequence: 31,
        seed_id: '5-31',
      },
      {
        content: 'If you could change one thing about your partner, what would it be?',
        membership_id: null,
        sequence: 32,
        seed_id: '5-32',
      },
      {
        content: 'If you had a blank check to create a date anywhere in the world, where would it be?',
        membership_id: null,
        sequence: 33,
        seed_id: '5-33',
      },
      {
        content: 'What is a funny dating app experience you have had?',
        membership_id: null,
        sequence: 34,
        seed_id: '5-34',
      },
      {
        content: 'Do you prefer meeting people online or in real life?',
        membership_id: null,
        sequence: 35,
        seed_id: '5-35',
      },
      {
        content: 'What mark do you hope to leave on this community?',
        membership_id: null,
        sequence: 36,
        seed_id: '5-36',
      },
      {
        content: 'What is your best memory you have had with your partner?',
        membership_id: null,
        sequence: 37,
        seed_id: '5-37',
      },
      {
        content: 'What is the hardest thing you and your partner have overcome?',
        membership_id: null,
        sequence: 38,
        seed_id: '5-38',
      },
      {
        content: 'What is one thing about your partner you are grateful for?',
        membership_id: null,
        sequence: 39,
        seed_id: '5-39',
      },
      {
        content: 'What is the craziest date you have been on?',
        membership_id: null,
        sequence: 40,
        seed_id: '5-40',
      },
      {
        content: 'What is the best advice you have received in regards to relationships?',
        membership_id: null,
        sequence: 41,
        seed_id: '5-41',
      },
      {
        content: 'What is a characteristic you find most attractive in a partner?',
        membership_id: null,
        sequence: 42,
        seed_id: '5-42',
      },
      {
        content: 'What lesson about relationships do you plan to teach your grandchildren?',
        membership_id: null,
        sequence: 43,
        seed_id: '5-43',
      },
      {
        content: 'Who is more generous, you or your partner?',
        membership_id: null,
        sequence: 44,
        seed_id: '5-44',
      },
      {
        content: 'What is something vulnerable you are afraid to share with your romantic partner?',
        membership_id: null,
        sequence: 45,
        seed_id: '5-45',
      },
      {
        content: 'What is something you would tell your younger self about relationships?',
        membership_id: null,
        sequence: 46,
        seed_id: '5-46',
      },
      {
        content: 'What is the most romantic thing you have ever experienced?',
        membership_id: null,
        sequence: 47,
        seed_id: '5-47',
      },
      {
        content: 'What is something you and your partner are currently working through? ',
        membership_id: null,
        sequence: 48,
        seed_id: '5-48',
      },
      {
        content: 'Do you think your job affects your relationship positively or negatively?',
        membership_id: null,
        sequence: 49,
        seed_id: '5-49',
      },
      {
        content: 'How do you deal with arguments and conflict resolution?',
        membership_id: null,
        sequence: 50,
        seed_id: '5-50',
      },
      {
        content: 'What are you extra sensitive about with your partner?',
        membership_id: null,
        sequence: 51,
        seed_id: '5-51',
      },
      {
        content: 'What feelings are hard for you to communicate?',
        membership_id: null,
        sequence: 52,
        seed_id: '5-52',
      },
      {
        content: 'What is the first thing you notice when you see someone you are attracted to?',
        membership_id: null,
        sequence: 53,
        seed_id: '5-53',
      },
      {
        content: 'What is your biggest fear in your relationship?',
        membership_id: null,
        sequence: 54,
        seed_id: '5-54',
      },
      {
        content: 'How important is age difference in a relationship to you?',
        membership_id: null,
        sequence: 55,
        seed_id: '5-55',
      },
      {
        content: 'Has your partner changed your mind about anything?',
        membership_id: null,
        sequence: 56,
        seed_id: '5-56',
      },
      {
        content: 'Have you ever unintentionally hurt your romantic partner?',
        membership_id: null,
        sequence: 57,
        seed_id: '5-57',
      },
      {
        content: 'How do you show love without saying it?',
        membership_id: null,
        sequence: 58,
        seed_id: '5-58',
      },
      {
        content: 'When do you feel closest to a romantic partner?',
        membership_id: null,
        sequence: 59,
        seed_id: '5-59',
      },
      {
        content: 'Are there any insecurities from a previous relationship you have carried into the next one?',
        membership_id: null,
        sequence: 60,
        seed_id: '5-60',
      },
      {
        content: 'What is an important lesson a past relationship has taught you?',
        membership_id: null,
        sequence: 61,
        seed_id: '5-61',
      },
      {
        content: 'How do you know you are in love?',
        membership_id: null,
        sequence: 62,
        seed_id: '5-62',
      },
      {
        content: 'What is missing in your relationship and how can you cultivate it?',
        membership_id: null,
        sequence: 63,
        seed_id: '5-63',
      },
      {
        content: 'What about you feels hardest to love?',
        membership_id: null,
        sequence: 64,
        seed_id: '5-64',
      },
      {
        content: 'How open are you with your partner?',
        membership_id: null,
        sequence: 65,
        seed_id: '5-65',
      },
      {
        content: 'What is the best question someone can ask to get to know you more?',
        membership_id: null,
        sequence: 66,
        seed_id: '5-66',
      },
      {
        content: "What is something you don't give yourself enough credit for?",
        membership_id: null,
        sequence: 67,
        seed_id: '5-67',
      },
      {
        content: 'How would you define a successful relationship in one sentence?',
        membership_id: null,
        sequence: 68,
        seed_id: '5-68',
      },
      {
        content: 'What is the most nervous you have felt on a date?',
        membership_id: null,
        sequence: 69,
        seed_id: '5-69',
      },
      {
        content: 'What is something you think every couple should do once in their lives?',
        membership_id: null,
        sequence: 70,
        seed_id: '5-70',
      },
      {
        content: 'How do you address something that is hard to talk about with a partner?',
        membership_id: null,
        sequence: 71,
        seed_id: '5-71',
      },
      {
        content: 'How does your partner inspire you to be better?',
        membership_id: null,
        sequence: 72,
        seed_id: '5-72',
      },
      {
        content: 'What relationship podcast would you recommend and why?',
        membership_id: null,
        sequence: 73,
        seed_id: '5-73',
      },
      {
        content: 'What is the most adventurous thing you have ever done?',
        membership_id: null,
        sequence: 74,
        seed_id: '5-74',
      },
      {
        content: 'What is one area of your relationship where you have room for growth?',
        membership_id: null,
        sequence: 75,
        seed_id: '5-75',
      },
      {
        content: 'Share a story of the last time your partner made you laugh?',
        membership_id: null,
        sequence: 76,
        seed_id: '5-76',
      },
      {
        content: 'What do you wish you had more time for in your relationship?',
        membership_id: null,
        sequence: 77,
        seed_id: '5-77',
      },
      {
        content: 'How important is it for your partner to be close to your friends?',
        membership_id: null,
        sequence: 78,
        seed_id: '5-78',
      },
      {
        content: 'What is a mistake you made in a relationship and regret?',
        membership_id: null,
        sequence: 79,
        seed_id: '5-79',
      },
      {
        content: 'Can you describe what your first heartbreak was like?',
        membership_id: null,
        sequence: 80,
        seed_id: '5-80',
      },
      {
        content: 'What is something you used to believe about relationships but no longer do?',
        membership_id: null,
        sequence: 81,
        seed_id: '5-81',
      },
      {
        content: 'How do you express anger and frustration?',
        membership_id: null,
        sequence: 82,
        seed_id: '5-82',
      },
      {
        content: 'Do you believe in astrological compatibility?',
        membership_id: null,
        sequence: 83,
        seed_id: '5-83',
      },
      {
        content: 'Is there a personality trait that annoys you most?',
        membership_id: null,
        sequence: 84,
        seed_id: '5-84',
      },
      {
        content: 'Who is more jealous in your relationship?',
        membership_id: null,
        sequence: 85,
        seed_id: '5-85',
      },
      {
        content: 'Do you believe in soul mates?',
        membership_id: null,
        sequence: 86,
        seed_id: '5-86',
      },
      {
        content: 'How does your idea about relationships compare to how you viewed them when you were younger?',
        membership_id: null,
        sequence: 87,
        seed_id: '5-87',
      },
      {
        content: 'When do you feel happiest with a romantic partner?',
        membership_id: null,
        sequence: 88,
        seed_id: '5-88',
      },
      {
        content: 'What is one thing you will not tolerate in a relationship?',
        membership_id: null,
        sequence: 89,
        seed_id: '5-89',
      },
      {
        content: 'What is something you learned from a rough breakup?',
        membership_id: null,
        sequence: 90,
        seed_id: '5-90',
      },
      {
        content: 'How important is alone time in a relationship and why?',
        membership_id: null,
        sequence: 91,
        seed_id: '5-91',
      },
      {
        content: 'How do you feel safe with a partner?',
        membership_id: null,
        sequence: 92,
        seed_id: '5-92',
      },
      {
        content: 'Are there any topics you feel nervous to talk to your partner about?',
        membership_id: null,
        sequence: 93,
        seed_id: '5-93',
      },
      {
        content: 'How do you and a partner share household chores?',
        membership_id: null,
        sequence: 94,
        seed_id: '5-94',
      },
      {
        content: 'What is the strangest date you have been on?',
        membership_id: null,
        sequence: 95,
        seed_id: '5-95',
      },
      {
        content: 'What are the top three things on your relationship bucket list?',
        membership_id: null,
        sequence: 96,
        seed_id: '5-96',
      },
      {
        content: 'How do you lead in your relationship and how does your partner?',
        membership_id: null,
        sequence: 97,
        seed_id: '5-97',
      },
      {
        content: 'What do you think is the most attractive personality trait?',
        membership_id: null,
        sequence: 98,
        seed_id: '5-98',
      },
    ],
  },
]

module.exports = {
  sparkCategories,
  university,
}
