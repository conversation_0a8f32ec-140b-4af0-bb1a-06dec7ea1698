// Ensure Doppler is set up to run on the staging environment
// doppler run -- node  apps/memberup/scripts/update-users-email-for-staging.js

require('dotenv').config()

const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const NEXT_PUBLIC_ENV = process.env.NEXT_PUBLIC_ENV

const updateUsersEmailsForStaging = async () => {
  if (NEXT_PUBLIC_ENV !== 'staging') {
    process.exit(-1)
  }

  try {
    await prisma.$queryRaw`UPDATE users
            SET email = CONCAT('staging+', REPLACE(REPLACE(REPLACE(email, '@', '_at_'), '.', '_'), '+', '_plus_'), '@memberup.com')
            WHERE email NOT LIKE '%@memberup.com';`
  } catch (e) {
    console.error(e)
    process.exit(1)
  }
}

updateUsersEmailsForStaging()
