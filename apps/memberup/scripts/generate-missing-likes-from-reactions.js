require('dotenv').config()
const { askQuestion, getStreamApps } = require('./common')
const { StreamChat } = require('stream-chat')
const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET
const DATABASE_URL = process.env.DATABASE_URL

const createStreamLike = async (client, channelType, channelId, messageId, reactionData) => {
  try {
    const channel = client.channel(channelType, channelId)
    const result = await channel.sendReaction(messageId, reactionData)
    console.log(`Create like with id: ${reactionData.id}`)
  } catch (err) {
    console.error('createMessage Error =====', err.message)
  }
}

async function main() {
  console.log(`Using Database URL ${DATABASE_URL}`)
  console.log(`Using GetStream App: key=${GET_STREAM_APP_KEY}, name=${getStreamApps[GET_STREAM_APP_KEY].name}`)

  const answer = await askQuestion('Do you want to proceed? (y/n): ')
  if (answer.toLowerCase() !== 'y') {
    console.log('Good bye.')
    return
  }

  const serverClient = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
    timeout: 30000,
  })

  const likes = await prisma.reaction.findMany({
    where: {
      type: 'like',
    },
    select: {
      id: true,
      user_id: true,
      message_id: true,
    },
    orderBy: [{ id: 'desc' }],
  })

  // Get getstream message.
  let j = 0
  for (let like of likes) {
    j++
    try {
      console.log(`Processing like ${j} of ${likes.length}`)
      const messageResult = await serverClient.getMessage(like.message_id)
      const reactionData = {
        type: 'like',
        user_id: like.user_id,
        id: like.id,
      }
      const message = messageResult.message
      await createStreamLike(serverClient, message.channel_type, message.channel_id, message.id, reactionData)
    } catch (e) {
      console.log(`Error: ${e.message}`)
    }
  }

  console.log(likes)
}

main()
