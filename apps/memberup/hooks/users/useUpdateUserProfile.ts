import { useState } from 'react'

import { uploadFileToCloudinaryApi } from '@memberup/shared/src/services/apis/cloudinary.api'
import { EditProfileSchemaType } from '@/components/settings/user-profile-settings'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { formSubmitError } from '@/lib/error-messages'
import { updateMemberProfile } from '@/memberup/store/features/memberSlice'
import { updateUserProfileSuccess } from '@/memberup/store/features/userSlice'
import { useAppDispatch } from '@/memberup/store/store'

type UpdateUserProfileData = EditProfileSchemaType & {
  image_file?: File
  cover_image_file?: File
  image?: string
  cover_image?: string
}

export function useUpdateUserProfile() {
  const dispatch = useAppDispatch()
  const user = useStore((state) => state.auth.user)
  const updateProfile = useStore((state) => state.auth.updateProfile)
  const updateUser = useStore((state) => state.auth.updateUser)
  const [saving, setSaving] = useState(false)

  const updateUserProfile = async (data: UpdateUserProfileData) => {
    setSaving(true)

    try {
      const { image_file, cover_image_file, ...profileData } = data

      if (image_file) {
        const result = await uploadFileToCloudinaryApi(image_file)
        profileData.image = result.data.secure_url as string
      }

      if (cover_image_file) {
        const result = await uploadFileToCloudinaryApi(cover_image_file)
        profileData.cover_image = result.data.secure_url as string
      }

      const response = await fetch(`/api/user/profile?id=${user.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      })

      setSaving(false)

      if (response.status !== 200) {
        return false
      }

      const { data: updatedProfileData } = await response.json()

      updateProfile(updatedProfileData)
      dispatch(updateUserProfileSuccess(updatedProfileData))
      dispatch(updateMemberProfile(updatedProfileData))

      if (updatedProfileData.user) {
        updateUser(updatedProfileData.user)
      }

      return true
    } catch (error) {
      toast.error(formSubmitError)
      setSaving(false)
      return false
    }
  }

  return {
    saving,
    updateUserProfile,
  }
}
