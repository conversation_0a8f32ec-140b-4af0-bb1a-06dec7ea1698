import { useState } from 'react'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { toast } from '@/components/ui/sonner'
import { addLikeData, selectLikes } from '@/memberup/store/features/likesSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { createLikeApi, deleteLikeApi } from '@/shared-services/apis/feed.api'
import { IFeed } from '@/shared-types/interfaces'

const useFeedLike = () => {
  const mountedRef = useMounted(true)
  const dispatch = useAppDispatch()
  const [loading, setLoading] = useState(false)
  const likesData = useAppSelector((state) => selectLikes(state))

  const handleLike = async (message: IFeed, status: 'liked' | 'unliked') => {
    setLoading(true)
    const originalLikeCount = message.reaction_counts['like']
    const likeCount =
      (likesData?.[message.id] ? likesData[message.id].likeCount : (message?.reaction_counts?.['like'] ?? 0)) +
      (status === 'liked' ? 1 : -1)

    dispatch(
      addLikeData({
        feedId: message.id,
        likeCount,
        status: status,
      }),
    )

    try {
      if (status === 'liked') {
        await createLikeApi(message.id)
      } else {
        await deleteLikeApi(message.id)
      }
    } catch (err) {
      console.error(err)
      if (mountedRef.current) {
        // Revert optimistic update
        dispatch(
          addLikeData({
            feedId: message.id,
            likeCount: originalLikeCount,
            status: status === 'liked' ? 'unliked' : 'liked',
          }),
        )
        toast.error(
          `An error occurred while ${status === 'liked' ? 'liking' : 'unliking'} the post. Please check your connection and try again.`,
        )
      }
    } finally {
      if (mountedRef.current) {
        setLoading(false)
      }
    }
  }

  return { loading, handleLike }
}

useFeedLike.displayName = 'useFeedLike'

export default useFeedLike
