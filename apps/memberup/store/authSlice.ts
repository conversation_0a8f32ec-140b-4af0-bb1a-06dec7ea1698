import { StateCreator } from 'zustand'
import { compute } from 'zustand-computed-state'

import { type StoreState } from './store'
import { USER_ROLE_ENUM, USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { getFullName } from '@/lib/formatting'
import { IAuthenticatedUser, IAuthenticatedUserData, IUserMembership, IUserProfile } from '@/shared-types/interfaces'

export enum AuthForms {
  customizeCommunity = 'customize-community',
  customizeTheme = 'customize-theme',
  joinCommunity = 'join-community',
  login = 'login',
  profileSetup = 'profile-setup',
  signup = 'signup',
  resetPassword = 'resetPassword',
  verifyEmail = 'verifyEmail',
}

export interface AuthSliceState {
  user?: IAuthenticatedUser | null
  profile?: IUserProfile | null
  streamChatUserToken?: string | null
  knockToken?: string | null
  showForm?: AuthForms | null
  joinCommunityId?: string | null
  inviteToken?: string | null
  sparkStreak?: number
}

export interface AuthSliceActions {
  closeModal: () => void
  setAuthenticatedUserData: (data: IAuthenticatedUserData) => void
  setJoinCommunityId: (joinCommunityId: string | null) => void
  setInviteToken: (inviteToken: string | null) => void
  setKnockToken: (knockToken: string | null) => void
  setProfile: (profile: IUserProfile) => void
  setShowForm: (value: AuthForms | null) => void
  setStreamChatUserToken: (streamChatUserToken: string | null) => void
  setUnauthenticatedState: () => void
  setUser: (user: IAuthenticatedUser | null) => void
  setSparkStreak: (sparkStreak: number) => void
  updateProfile: (profile: Partial<IUserProfile>) => void
  updateUser: (user: Partial<IAuthenticatedUser>) => void
  updateUserMembership: (value: Partial<IUserMembership>) => void
}

export interface AuthSliceComputed {
  activeModalForm: AuthForms | null
  userFullName: string
}

export type AuthSlice = {
  auth: AuthSliceState & AuthSliceActions & AuthSliceComputed
}

const defaultInitialAuthSliceState: AuthSliceState = {
  user: null,
  profile: null,
  streamChatUserToken: null,
  knockToken: null,
  showForm: null,
  joinCommunityId: null,
  inviteToken: null,
}

export const createAuthSlice: (
  initialData?: AuthSliceState,
) => StateCreator<StoreState, [], [['zustand/devtools', never]], AuthSlice> =
  (initialData = defaultInitialAuthSliceState) =>
  (set: any, get: any, _: any) => {
    return {
      auth: {
        closeModal: () => {
          set((state: StoreState) => ({ auth: { ...state.auth, joinCommunityId: null, showForm: null } }))
        },
        setAuthenticatedUserData: (data: IAuthenticatedUserData) => {
          set((state: StoreState) => ({
            auth: {
              ...state.auth,
              user: data.user,
              profile: data.profile,
              streamChatUserToken: data.streamChatUserToken,
              knockToken: data.knockToken,
            },
          }))
        },
        setJoinCommunityId: (joinCommunityId: string | null) => {
          set((state: StoreState) => ({ auth: { ...state.auth, joinCommunityId } }))
        },
        setSparkStreak: (sparkStreak: number) => {
          set((state: StoreState) => ({ auth: { ...state.auth, sparkStreak } }))
        },
        setInviteToken: (inviteToken: string | null) => {
          set((state: StoreState) => ({ auth: { ...state.auth, inviteToken } }))
        },
        setKnockToken: (knockToken: string | null) => {
          set((state: StoreState) => ({ auth: { ...state.auth, knockToken } }))
        },
        setProfile: (profile: IUserProfile) => {
          set((state: StoreState) => ({ auth: { ...state.auth, profile } }))
        },
        setShowForm: (value: AuthForms | null, joinCommunityId?: string | null) => {
          set((state: StoreState) => ({
            auth: { ...state.auth, showForm: value },
          }))
        },
        setStreamChatUserToken: (streamChatUserToken: string | null) => {
          set((state: StoreState) => ({ auth: { ...state.auth, streamChatUserToken } }))
        },
        setUnauthenticatedState: () => {
          set((state: StoreState) => ({
            auth: { ...state.auth, user: null, profile: null, knockToken: null, streamChatUserToken: null },
          }))
        },
        setUser: (user: IAuthenticatedUser) => {
          set((state: StoreState) => ({ auth: { ...state.auth, user } }))
        },
        updateUser: (user: Partial<IAuthenticatedUser>) => {
          set((state: StoreState) => ({ auth: { ...state.auth, user: { ...state.auth.user, ...user } } }))
        },
        updateProfile: (profile: Partial<IUserProfile>) => {
          set((state: StoreState) => ({ auth: { ...state.auth, profile: { ...state.auth.profile, ...profile } } }))
        },
        updateUserMembership: (value: Partial<IUserMembership>) => {
          set((state: StoreState) => {
            const { user_memberships } = state.auth.user

            const newMemberships = user_memberships?.some((userMembership: any) => userMembership.id === value.id)
              ? user_memberships?.map((userMembership: any) =>
                  userMembership.id === value.id ? { ...userMembership, ...value } : userMembership,
                )
              : [...(user_memberships || []), value]

            return {
              auth: {
                ...state.auth,
                user: {
                  ...state.auth.user,
                  user_memberships: newMemberships,
                },
              },
            }
          })
        },
        ...initialData,
      },
      ...compute('authSlice', get, (state) => {
        const {
          auth,
          community: { membership, userMembership },
        } = state as StoreState
        const { user, profile, showForm } = auth

        let activeModalForm = showForm

        if (user) {
          if (user.status === USER_STATUS_ENUM.unverified) {
            activeModalForm = AuthForms.verifyEmail
          } else if (auth.joinCommunityId && !auth.showForm) {
            activeModalForm = AuthForms.joinCommunity
          } else if (!profile.image) {
            activeModalForm = AuthForms.profileSetup
          } else if (!profile.theme_mode) {
            activeModalForm = AuthForms.customizeTheme
          } else if (
            userMembership &&
            membership !== null &&
            !membership.membership_setting.theme_main_color &&
            (userMembership.user_role === USER_ROLE_ENUM.owner || userMembership.user_role === USER_ROLE_ENUM.admin)
          ) {
            activeModalForm = AuthForms.customizeCommunity
          } else {
          }
        }

        return {
          auth: {
            ...auth,
            activeModalForm,
            userFullName: getFullName(auth.user),
          },
        }
      }),
    }
  }
