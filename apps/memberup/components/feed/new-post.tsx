'use client'

import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import React, { useState } from 'react'

import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { SkeletonBox } from '@/components/ui/skeleton'
import { useStore } from '@/hooks/useStore'
import EditPost from '@/memberup/components/dialogs/feed/edit-post'
import { selectCurrentPostDraft } from '@/memberup/store/features/feedSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { IMembership } from '@/shared-types/interfaces'

const NewPost: React.FC<{ membership: IMembership; loading: boolean }> = ({ membership, loading }) => {
  const user = useStore((state) => state.auth.user)
  const userProfile = useStore((state) => state.auth.profile)
  const currentPostDraft = useAppSelector((state) => selectCurrentPostDraft(state))
  const currentEditorText = currentPostDraft?.editorState?.getCurrentContent()?.getPlainText()
  const [editingPost, setEditingPost] = useState(false)
  let draftDisplayText = currentPostDraft?.title ? `${currentPostDraft?.title}` : null

  if (!draftDisplayText && currentEditorText) {
    draftDisplayText = `${currentEditorText.slice(0, 30)}${currentEditorText.length > 30 ? '...' : ''}`
  }

  if (!user) return null

  if (loading) return <SkeletonBox className="h-16 w-full" />

  return (
    <div>
      <Card
        onClick={() => setEditingPost(true)}
        sx={{
          borderRadius: '16px',
          width: '100%',
          cursor: 'pointer',
        }}
      >
        <CardHeader
          className="bg-white-500 dark:bg-black-500"
          sx={{
            p: '12px 14px',
          }}
          avatar={
            <AppProfileImage
              imageUrl={userProfile?.image || user?.image}
              cropArea={userProfile?.image_crop_area}
              name={user?.first_name || user?.email}
              size={40}
            />
          }
          subheader={
            <div className="rounded-[10px] bg-white-100 p-4 text-sm text-grey-700 dark:bg-black-400 dark:text-black-100">
              {draftDisplayText || 'Say something...'}
            </div>
          }
        />
      </Card>
      <EditPost membership={membership} mode="create" open={editingPost} onClose={() => setEditingPost(false)} />
    </div>
  )
}

export default React.memo(NewPost)
