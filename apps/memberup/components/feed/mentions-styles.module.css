.mention {
  text-decoration: none;
  border-radius: 14px;
  padding: 0.4rem;
}

.mentionSuggestions {
  /*   border-top: 1px solid #eee;
 */
  background: #fff;
  color: #000;
  border-radius: 12px;
  cursor: pointer;
  padding-top: 8px;
  padding-bottom: 8px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  transform-origin: 50% 0%;
  transform: scaleY(0);
  z-index: 1000;
  position: absolute;
  min-width: 200px;
  font-weight: 600;
  border: solid 1px hsl(var(--grey-400)) !important;
}

.mentionSuggestionsDark {
  color: #ffffff;
  /* add your additional styles here */
  background: hsl(var(--black-500));
  border-radius: 12px;
  cursor: pointer;
  padding-top: 2px;
  padding-bottom: 2px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  transform-origin: 50% 0%;
  transform: scaleY(0);
  z-index: 1000;
  position: absolute;
  min-width: 200px;
  font-weight: 600;
  border: solid 1px #2a2b30 !important;
}

.mentionSuggestionsEntryContainer {
  display: table;
  width: 100%;
}

.mentionSuggestionsEntryContainerLeft,
.mentionSuggestionsEntryContainerRight {
  display: table-cell;
  vertical-align: middle;
}

.mentionSuggestionsEntryContainerRight {
  width: 100%;
  padding-left: 8px;
}

.mentionSuggestionsEntry {
  padding: 7px 10px 7px 10px;
  margin: 2px 3px;
  border-radius: 12px;
}

.mentionSuggestionsEntry:active {
  background-color: #434343;
}

.mentionSuggestionsEntryFocused {
  composes: mentionSuggestionsEntry;
  border-radius: 12px;
  background-color: hsl(var(--grey-100));
}

.mentionSuggestionsEntryFocusedDark {
  composes: mentionSuggestionsEntry;
  border-radius: 12px;
  background-color: hsl(var(--black-400));
}

.mentionSuggestionsEntryText,
.mentionSuggestionsEntryTitle {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mentionSuggestionsEntryText {
  font-size: 0.8125rem;
}

.mentionSuggestionsEntryTitle {
  font-size: 80%;
  color: #a7a7a7;
}

.mentionSuggestionsEntryAvatar {
  display: block;
  width: 30px;
  height: 30px;
  border-radius: 50%;
}
