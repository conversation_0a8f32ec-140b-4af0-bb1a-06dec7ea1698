import { saveAs } from 'file-saver'
import { useState } from 'react'

import MediaFiles from '../../src/components/common/media-files'
import MediaPdfPreview from '../../src/components/common/media-pdf-preview'
import MediaViewer from '../../src/components/common/media-viewer'
import FullScreenMediaViewer from '../../src/components/dialogs/feed/full-screen-media-viewer'
import { DownloadFileHandler } from '@memberup/shared/src/types/types'
import { setAttachmentToDeleteAction, upsertFeed } from '@/memberup/store/features/feedSlice'
import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

export function processAttachments(feed: any) {
  const result = feed.attachments.reduce((acc, attachment) => {
    if (['video', 'image', 'gif'].includes(attachment.mimetype)) {
      if (!acc['media']) {
        acc['media'] = []
      }
      acc['media'].push(attachment)
    } else if (attachment.mimetype === 'pdf') {
      if (!acc['pdf']) {
        acc['pdf'] = []
      }
      acc['pdf'].push(attachment)
    } else if (['audio', 'text', 'other', 'doc'].includes(attachment.mimetype)) {
      if (!acc['other']) {
        acc['other'] = []
      }
      acc['other'].push(attachment)
    }
    return acc
  }, {})
  return result
}

const FeedAttachments = ({ feed }) => {
  const attachments = processAttachments(feed)
  const [isFullScreenViewerOpen, setIsFullScreenViewerOpen] = useState(false)
  const [selectedMedias, setSelectedMedias] = useState([])
  const [selectedMediaIndex, setSelectedMediaIndex] = useState(-1)
  const membership = useAppSelector((state) => selectMembership(state))

  const dispatch = useAppDispatch()

  const handleDownload: DownloadFileHandler = (file) => {
    if (file.url) {
      saveAs(file.url, file.filename)
    }
  }

  const handleMediaClick = (index: number, medias: any[]) => {
    setSelectedMediaIndex(index)
    setSelectedMedias(medias)
    setIsFullScreenViewerOpen(true)
  }

  const handleOpenFileInNewTab = (url) => {
    window.open(url, '__blank')
  }

  const handleRemovePreview = (feed, attachment) => {
    const payload = {
      id: feed.id,
      attachments: feed.attachments.map((a) => {
        if (a.filename === attachment.filename) {
          const updated = { ...a, show_preview: false }
          return updated
        }
        return a
      }),
    }

    dispatch(
      upsertFeed({
        data: payload,
        membershipId: membership.id,
        messages: {
          success: `Successfully removed preview for ${attachment.filename}.`,
          fail: `Failed to remove preview fro ${attachment.filename}.`,
        },
      }),
    )
  }

  const handleDeleteFile = (feed, attachment) => {
    dispatch(setAttachmentToDeleteAction({ feed, attachment, membershipId: membership.id }))
  }

  const handleFullScreenViewerClose = () => {
    setIsFullScreenViewerOpen(false)
  }

  if (
    // Single pdf
    Object.keys(attachments).length === 1 &&
    Object.keys(attachments).includes('pdf') &&
    attachments['pdf'].length == 1 &&
    (attachments['pdf'][0].show_preview === undefined || attachments['pdf'][0].show_preview)
  ) {
    return (
      <MediaPdfPreview
        feed={feed}
        file={attachments['pdf'][0]}
        previewMode={false}
        handleRemovePreview={handleRemovePreview}
        handleDownloadFile={handleDownload}
        handleOpenInNewTab={handleOpenFileInNewTab}
        handleDeleteFile={handleDeleteFile}
      />
    )
  }

  return (
    <>
      {Object.keys(attachments).includes('media') && (
        <>
          {isFullScreenViewerOpen && (
            <FullScreenMediaViewer
              selectedMediaIndex={selectedMediaIndex}
              open={isFullScreenViewerOpen}
              medias={selectedMedias}
              handleClose={handleFullScreenViewerClose}
            />
          )}
          <MediaViewer
            attachments={attachments['media']}
            handleMediaClick={(index, medias) => handleMediaClick(index, medias)}
          />
        </>
      )}
      <MediaFiles
        files={[...(attachments['pdf'] || []), ...(attachments['other'] || [])]}
        handleDownloadFile={handleDownload}
      />
    </>
  )
}
export default FeedAttachments
