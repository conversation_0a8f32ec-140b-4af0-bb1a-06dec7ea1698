import { joi<PERSON>esolver } from '@hookform/resolvers/joi'
import Box from '@mui/material/Box'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import FormControl from '@mui/material/FormControl'
import Stack from '@mui/material/Stack'
import useTheme from '@mui/material/styles/useTheme'
import { makeStyles } from '@mui/styles'
import { AxiosError } from 'axios'
import { EditorState, Modifier, SelectionState } from 'draft-js'
import Joi from 'joi'
import dynamic from 'next/dynamic'
import React, { CSSProperties, useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'

import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { insertStr } from '@memberup/shared/src/libs/string-utils'
import { createFeedApi } from '@memberup/shared/src/services/apis/feed.api'
import { FEED_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { IFeed, IUser } from '@memberup/shared/src/types/interfaces'
import MentionsInput from '@/components/feed/mentions-input'
import { SkeletonBox } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { convertToHtml } from '@/memberup/components/common/editor/editor-utils'
import LoadingSpinner from '@/memberup/components/common/loaders/loading-spinner'
import { mentionGetEditorMentions, mentionGetSuggestions } from '@/memberup/libs/mentions'
import { selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const EmojiPicker = dynamic(() => import('@/memberup/components/common/pickers/emoji-picker'), {
  ssr: false,
})

const useStyles = makeStyles((theme) => ({
  root: {},
  cancelText: {
    opacity: 1,
    color: 'rgba(141,148,163,1)',
    fontFamily: 'Graphik Regular',
    fontSize: '12px',
    fontWeight: 400,
    fontStyle: 'normal',
    letterSpacing: '0px',
    textAlign: 'left',
    lineHeight: '16px',
  },
  cancelButton: {
    backgroundColor: 'transparent !important',
    border: 0,
    padding: 0,
    cursor: 'pointer',
    display: 'inline-block',
    color: 'rgba(219,139,231,1)', // TODO 1870: theme color
    fontFamily: 'Graphik Semibold',
    fontSize: '12px',
    fontWeight: 600,
    fontStyle: 'normal',
    letterSpacing: '0px',
    textAlign: 'left',
    lineHeight: '16px',
  },
  contentBox: {
    borderRadius: '16px',
    position: 'relative',
    marginLeft: '0',
    '& .MuiOutlinedInput-root': {
      backgroundColor: theme.palette.action.disabledBackground,
      borderRadius: 12,
      paddingRight: 8,
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none',
      },
      position: 'relative',
      overflow: 'visible',
    },
    '& .MuiInputAdornment-root': {
      height: 18,
      alignSelf: 'self-end',
    },
  },
  emojiPickerWrapper: {
    position: 'absolute',
    right: 8,
    top: -390,
  },
  replyButton: {
    padding: 0,
    minWidth: 22,
    marginLeft: 4,
  },
}))

type FormDataType = {
  text: string
}

const FormValue: FormDataType = {
  text: '',
}

const FormSchema = Joi.object({
  text: Joi.string().required(),
}).options({ allowUnknown: true })

const NewComment: React.FC<{
  isReply?: boolean
  secondaryLevelMessage?: IFeed
  topLevelMessage: IFeed
  focus?: boolean
  handleCancel?: () => void
  members?: { [key: string]: IUser }
  parentPermalink?: string | unknown
  styles?: CSSProperties
  commentType?: 'reply' | 'comment'
  addMentionByDefault?: boolean
  scrollToBottom: () => void
  setIsSubmittingComment?: any
  portal?: boolean
}> = ({
  isReply,
  secondaryLevelMessage,
  topLevelMessage,
  focus = false,
  handleCancel,
  members,
  parentPermalink,
  styles,
  commentType = 'comment',
  addMentionByDefault = false,
  scrollToBottom,
  setIsSubmittingComment = undefined,
  portal = false,
}) => {
  const classes = useStyles()
  const theme = useTheme()
  const mountedRef = useMounted(true)
  const selectionRef = useRef({
    start: 1,
    end: -1,
  })
  const boxRef = useRef(null)
  const currentUser = useStore((state) => state.auth.user)
  const currentUserProfile = useStore((state) => state.auth.profile)
  const [openMentionSuggestions, setOpenMentionSuggestions] = useState(false)
  const membership = useStore((state) => state.community.membership)
  const hasSetDefaultEditorStateRef = useRef(false)

  useLayoutEffect(() => {
    if (boxRef.current) {
      boxRef.current.scrollIntoView({ block: 'center' })
    }
  }, [])

  const { control, formState, getValues, setValue, handleSubmit } = useForm<FormDataType>({
    mode: 'onBlur',
    reValidateMode: 'onChange',
    defaultValues: FormValue,
    resolver: joiResolver(FormSchema),
  })
  const [requestComment, setRequestComment] = useState(false)
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)

  const [mentionedUsers, setMentionedUsers] = useState([])
  const [editorState, setEditorState] = useState(null)
  const editorStateRef = useRef(editorState)
  editorStateRef.current = editorState
  const createDefaultEditorStateCalled = useRef(false)

  let replyToMessage: IFeed
  let attachedToMessage: IFeed
  let hierarchyOrderString: string
  const isTopLevelComment = topLevelMessage && !secondaryLevelMessage

  if (isTopLevelComment) {
    attachedToMessage = topLevelMessage
    replyToMessage = topLevelMessage
    let hierarchyOrderSeq = 0
    if (topLevelMessage.reply_count) {
      hierarchyOrderSeq = topLevelMessage.reply_count
    }
    hierarchyOrderString = hierarchyOrderSeq.toString().padStart(4, '0')
  } else {
    attachedToMessage = topLevelMessage
    replyToMessage = secondaryLevelMessage

    if (!replyToMessage['hierarchy_order']) {
      // MEM-1870: For compatiblity reasons, when replying to old comments, we don't want to produce a hierarchy order, just the relation.
      hierarchyOrderString = null
    } else {
      const parts = String(replyToMessage['hierarchy_order']).split('.')
      if (parts.length == 2) {
        const currentSeq = parseInt(parts[1])
        const nextSeq = currentSeq + 1
        hierarchyOrderString = `${parts[0]}.${nextSeq.toString().padStart(4, '0')}`
      } else {
        // TODO 1870: Must compute the next based on the child count when commenting on a top level to produce a the secondary level comment.
        const nextSeq = 0
        hierarchyOrderString = `${parts[0]}.${nextSeq.toString().padStart(4, '0')}`
      }
    }
  }

  const handleMentionStateChange = (stateValue) => {
    setOpenMentionSuggestions(stateValue)
  }

  function setCursorAtEnd() {
    // Move focus to the end of the editor content
    const contentState = editorStateRef.current.getCurrentContent()
    const blockMap = contentState.getBlockMap()
    const key = blockMap.last().getKey()
    const length = blockMap.last().getLength()
    const selection = new SelectionState({
      anchorKey: key,
      anchorOffset: length,
      focusKey: key,
      focusOffset: length,
    })
    const newEditorState = EditorState.forceSelection(editorStateRef.current, selection)
    setEditorState(newEditorState)
  }

  const createDefaultEditorState = () => {
    if (!addMentionByDefault || replyToMessage.user.id == currentUser.id) {
      return EditorState.createEmpty()
    }
    const mentionUser = replyToMessage.user
    const mentionText = `${mentionUser.name}`
    const mentionObj = {
      id: mentionUser.id,
      image: mentionUser.image,
      image_crop_area: mentionUser.image_crop_area,
      markup: `{{mention:user:${mentionUser.id}}}`,
      name: mentionUser.name,
    }
    const editorState = EditorState.createEmpty()
    const stateWithEntity = editorState.getCurrentContent().createEntity('mention', 'SEGMENTED', {
      mention: mentionObj,
    })
    const entityKey = stateWithEntity.getLastCreatedEntityKey()
    const stateWithText = Modifier.insertText(stateWithEntity, editorState.getSelection(), ' ', null, null)

    const newStateWithText = Modifier.insertText(
      stateWithText,
      editorState.getSelection(),
      mentionText,
      null,
      entityKey,
    )
    createDefaultEditorStateCalled.current = true

    return EditorState.push(editorState, newStateWithText)
  }

  useEffect(() => {
    if (!hasSetDefaultEditorStateRef.current) {
      hasSetDefaultEditorStateRef.current = true
      const defaultEditorState = createDefaultEditorState()
      setEditorState(defaultEditorState)
      setTimeout(setCursorAtEnd, 500)
    }
  }, [secondaryLevelMessage, topLevelMessage])

  const onInputChangeHandler = (field, editorCurrentContent) => {
    field.onChange(editorCurrentContent.getPlainText())
    const editorMentions: string[] = mentionGetEditorMentions(editorCurrentContent)
    if (JSON.stringify(mentionedUsers) !== JSON.stringify(editorMentions)) {
      // update only if the mentions changed
      setMentionedUsers(editorMentions)
    }
  }

  const resetEditor = () => {
    setEditorState(createDefaultEditorState())
  }

  const handleCreateComment = async (payload: FormDataType) => {
    try {
      setRequestComment(true)
      setIsSubmittingComment?.(true)

      const channelId = (attachedToMessage.cid || '').split(':').pop()
      const parentId = attachedToMessage.id
      let replyParentId: string = replyToMessage.id
      if (
        !isTopLevelComment &&
        replyToMessage.reply_parent_id !== undefined &&
        replyToMessage.parent_id !== replyToMessage.reply_parent_id
      ) {
        replyParentId = replyToMessage.reply_parent_id // This is a reply to a comment of a comment.
      }

      const filteredMentionedUsers = mentionedUsers.map((user) => {
        const { markup, imageUrl, cropArea, ...rest } = user
        return rest
      })

      try {
        await createFeedApi(
          {
            channel_id: channelId,
            feed_type: FEED_TYPE_ENUM.comment,
            parent_id: parentId,
            reply_parent_id: replyParentId,
            hierarchy_order: hierarchyOrderString,
            text: convertToHtml(editorState) || '',
            attachments: [],
            reports: [],
            mentioned_users: filteredMentionedUsers || [],
            parent_permalink: parentPermalink,
            update_latest_comment_timestamp: true,
          } as any,
          membership.id,
        )
        resetEditor()
        toast.success('Comment succesfully added.')

        setIsSubmittingComment?.(false)

        if (mountedRef.current) {
          handleCancel?.()
          scrollToBottom()
        }
      } catch (error) {
        if (mountedRef.current) {
          if (error instanceof AxiosError) {
            toast.error('Network error. Please try again.')
          } else if (error.response?.data?.error) {
            toast.error(error.response.data.error)
          }
        }
      } finally {
        if (mountedRef.current) {
          setRequestComment(false)
        }
      }
    } catch (err: any) {
      console.log(err)
    }
  }

  const handleClickEmoji = useCallback(
    (emoji: any) => {
      setShowEmojiPicker(false)
      const temp = getValues('text')
      setValue('text', insertStr(temp, emoji, selectionRef.current.start, selectionRef.current.end))
    },
    [getValues, setValue],
  )

  const renderEmojiPicker = useMemo(() => {
    if (typeof window === 'undefined') return null
    return (
      <div className={classes.emojiPickerWrapper}>
        <EmojiPicker onClickEmoji={handleClickEmoji} />
      </div>
    )
  }, [classes.emojiPickerWrapper, handleClickEmoji])

  const formattedSuggestions = mentionGetSuggestions(members || {}, currentUser as IUser, membership.id).filter((u) => {
    return u.id !== currentUser?.id
  })

  return (
    <div
      data-cy="new-comment"
      ref={boxRef}
      className={classes.root}
      style={{
        marginLeft: commentType === 'reply' ? '5px' : undefined,
      }}
    >
      <form autoComplete="off" onSubmit={handleSubmit(handleCreateComment)}>
        {formattedSuggestions ? (
          <Stack direction="row" spacing={2}>
            <AppProfileImage
              imageUrl={currentUserProfile?.image || currentUser?.image}
              cropArea={currentUserProfile?.image_crop_area || currentUser?.image_crop_area}
              name={currentUser?.first_name || currentUser?.email}
              size={commentType === 'reply' ? 32 : 40}
              className="mr-3 mt-1"
            />
            <Stack sx={{ flexGrow: 1, width: '100%' }} className="ml-0">
              <Box className={classes.contentBox}>
                <Controller
                  render={({ field, fieldState: { error } }) => (
                    <FormControl
                      error={Boolean(error)}
                      className="form-control"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey && !showEmojiPicker && !openMentionSuggestions) {
                          e.preventDefault()
                          handleSubmit(handleCreateComment)()
                        }
                      }}
                      fullWidth
                      sx={{
                        '&.MuiFormControl-root': {
                          '& .text-editor': {
                            border:
                              theme.palette.mode === 'dark'
                                ? '1px solid #292a2e !important'
                                : {
                                    xs: '1px solid hsl(var(--ui-light-300)) !important',
                                    twMd: '1px solid #f2f2f3 !important',
                                  },
                            backgroundColor: {
                              xs: theme.palette.mode === 'dark' ? 'transparent' : 'hsl(var(--ui-light-300)) !important',
                              twMd: 'transparent !important',
                            },
                          },
                          '&:focus-within .text-editor': {
                            border:
                              theme.palette.mode === 'dark'
                                ? '#4d4f56 1px solid !important'
                                : '#bcbec2 1px solid !important',
                          },
                          '& .public-DraftEditorPlaceholder-hasFocus #placeholder-editor': {
                            color: 'hsl(var(--ui-light-800))',
                          },
                          '& #placeholder-editor': {
                            color: 'hsl(var(--ui-light-1000))',
                          },
                        },
                      }}
                    >
                      {requestComment && <SkeletonBox className={'h-[50px]'} />}
                      {!requestComment && editorState && (
                        <MentionsInput
                          {...field}
                          error={Boolean(error)}
                          editorState={editorState}
                          focus={focus}
                          formState={formState}
                          isPosting={requestComment}
                          onMentionStateChange={handleMentionStateChange}
                          placeholder={commentType === 'reply' ? 'Write your reply...' : 'Write your comment...'}
                          setEditorState={setEditorState}
                          showLoadingSpinner={true}
                          styles={styles}
                          type={commentType}
                          users={formattedSuggestions}
                          onChangeEditorState={setEditorState}
                          onChange={(editorState) => onInputChangeHandler(field, editorState)}
                          onCancel={handleCancel}
                          onSubmit={() => handleSubmit(handleCreateComment)}
                          portal={portal}
                        />
                      )}
                    </FormControl>
                  )}
                  control={control}
                  name="text"
                />
              </Box>
            </Stack>

            {showEmojiPicker && (
              <ClickAwayListener onClickAway={(e) => setShowEmojiPicker(false)}>{renderEmojiPicker}</ClickAwayListener>
            )}
          </Stack>
        ) : (
          <div
            style={{
              display: 'flex',
              width: '100%',
              minHeight: '300px',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <span>
              <LoadingSpinner />
            </span>
          </div>
        )}
      </form>
    </div>
  )
}

export default NewComment
