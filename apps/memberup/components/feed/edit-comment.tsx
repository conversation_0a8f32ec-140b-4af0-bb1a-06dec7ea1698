import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import React, { CSSProperties, useEffect, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { StreamMessage } from 'stream-chat-react'
import { z } from 'zod'

import { convertToHtml, createEditorStateFromHtml } from '../../src/components/common/editor/editor-utils'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { updateFeedApi } from '@memberup/shared/src/services/apis/feed.api'
import { IFeed, IUser } from '@memberup/shared/src/types/interfaces'
import MentionsInput from '@/components/feed/mentions-input'
import { LoaderCircle24Icon } from '@/components/icons'
import { Button } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { formSubmitError } from '@/lib/error-messages'
import { mentionCreateEditorState, mentionGetEditorMentions, mentionGetSuggestions } from '@/memberup/libs/mentions'
import { unescapeSlashes } from '@/memberup/libs/utils'

type FormDataType = {
  text: string
}

const FormValue: FormDataType = {
  text: '',
}

const FormSchema = z.object({
  text: z.string().min(1, 'Text is required'),
})

const EditComment: React.FC<{
  data?: StreamMessage | IFeed
  placeholder?: string
  members: { [key: string]: IUser }
  styles?: CSSProperties
  onClose?: () => void
  onCancel?: () => void
  editingCommentIds: string[]
  addEditingCommentId: (id: string) => void
  removeEditingCommentId: (id: string) => void
}> = ({
  data,
  placeholder,
  members,
  styles,
  onClose,
  onCancel,
  editingCommentIds,
  addEditingCommentId,
  removeEditingCommentId,
}) => {
  const mountedRef = useMounted(true)
  const membership = useStore((state) => state.community.membership)
  const currentUser = useStore((state) => state.auth.user)
  const [requestComment, setRequestComment] = useState(false)
  const [mentions, setMentions] = useState([])
  const [editorState, setEditorState] = useState(null)
  const [formattedSuggestions, setFormattedSuggestions] = useState([])

  const { control, formState, handleSubmit } = useForm<FormDataType>({
    mode: 'onBlur',
    reValidateMode: 'onChange',
    defaultValues: FormValue,
    resolver: zodResolver(FormSchema),
  })

  useEffect(() => {
    if (!editingCommentIds.includes(data.id)) {
      const suggestions = mentionGetSuggestions(
        { ...members, [data.user.id]: data.user as IUser },
        currentUser,
        membership.id,
      )
      setFormattedSuggestions(suggestions)
      setEditorState(createEditorStateFromHtml(unescapeSlashes(data?.text || '')))
    }
  }, [data, members, editingCommentIds])

  const handleInputChange = (field, editorCurrentContent) => {
    addEditingCommentId(data.id)
    field.onChange(editorCurrentContent.getPlainText())
    const editorMentions = mentionGetEditorMentions(editorCurrentContent)
    if (JSON.stringify(mentions) !== JSON.stringify(editorMentions)) {
      setMentions(editorMentions)
    }
  }

  const resetEditor = () => {
    setEditorState(mentionCreateEditorState('', { ...members, [data.user.id]: data.user }))
  }

  const handleCancel = () => {
    resetEditor()
    removeEditingCommentId(data.id)
    onCancel?.()
  }

  const handleFormSubmit = (payload: FormDataType) => {
    try {
      addEditingCommentId(data.id)
      setRequestComment(true)

      const filteredMentionedUsers = mentions.map((user) => {
        const { markup, ...rest } = user
        return rest
      })

      updateFeedApi(data.id, {
        text: convertToHtml(editorState),
        mentioned_users: filteredMentionedUsers || [],
      } as any)
        .then(() => {
          if (mountedRef.current) {
            resetEditor()
            onCancel?.()
          }
        })
        .catch(() => {
          toast.error(formSubmitError)
        })
        .finally(() => {
          if (mountedRef.current) {
            setRequestComment(false)
            removeEditingCommentId(data.id)
          }
        })
    } catch (err: any) {
      console.log(err)
    }
  }

  return (
    <div className="w-full">
      <div>
        {requestComment ? (
          <LoaderCircle24Icon className="h-10 w-10 animate-spin text-community-primary" />
        ) : (
          <>
            <div
              className="relative mb-1 mr-0 rounded-2xl border border-gray-200 p-1 dark:border-[#2A2B30]"
              style={styles}
            >
              {editorState && formattedSuggestions.length > 0 && (
                <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
                  <Controller
                    render={({ field, fieldState: { error } }) => (
                      <div className="w-full">
                        <MentionsInput
                          {...field}
                          editorState={editorState}
                          error={Boolean(error)}
                          formState={formState}
                          isPosting={requestComment}
                          placeholder={placeholder}
                          showLoadingSpinner={true}
                          styles={styles}
                          type="comment"
                          users={formattedSuggestions}
                          onCancel={handleCancel}
                          onChangeEditorState={setEditorState}
                          onChange={(editorState) => handleInputChange(field, editorState)}
                          onSubmit={() => handleSubmit(handleFormSubmit)}
                        />
                      </div>
                    )}
                    control={control}
                    name="text"
                  />
                </form>
              )}
            </div>
            <div>
              <span className="text-xs text-gray-400 dark:text-gray-500">Press Esc to&nbsp;</span>
              <Button
                className="font-medium text-community-primary hover:text-community-secondary"
                variant="inline"
                onClick={handleCancel}
              >
                cancel
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

EditComment.displayName = 'EditComment'
export default EditComment
