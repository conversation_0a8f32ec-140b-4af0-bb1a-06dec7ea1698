import Link from 'next/link'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { searchClient } from '@memberup/shared/src/config/algolia-client'
import { getDateTimeFromNow } from '@memberup/shared/src/libs/date-utils'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { FEED_STATUS_ENUM, USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { IFeed, IUser } from '@memberup/shared/src/types/interfaces'
import { UserDetailsHoverCard } from '@/components/community/user-details-hover-card'
import { MoreHorizontal20Icon } from '@/components/icons'
import { Verified16Icon } from '@/components/icons/16px/verified-16-icon'
import { ProfilePicture } from '@/components/images/profile-picture'
import { Button } from '@/components/ui'
import {
  AdaptiveTooltip,
  AdaptiveTooltipContent,
  Adaptive<PERSON>ooltipProvider,
  AdaptiveTooltipTrigger,
} from '@/components/ui/adaptive-tooltip'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { useStore } from '@/hooks/useStore'
import { formatDistanceToNowMinimal } from '@/lib/dates'
import { cn } from '@/lib/utils'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import { selectPinnedPostsCount } from '@/memberup/store/features/feedAggregationSlice'
import { reportFeed, setFeedToDelete } from '@/memberup/store/features/feedSlice'
import { openDialog } from '@/memberup/store/features/uiSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { RootState } from '@/memberup/store/store'
import { formatDateLong } from '@/shared-libs/date-utils'
import { isUserActiveAndAcceptedInCommunity } from '@/shared-libs/profile'
import { IChannel, IMembership } from '@/shared-types/interfaces'
import EditPost from '@/src/components/dialogs/feed/edit-post'
import checkStreamUserRole from '@/src/components/hooks/check-stream-user-role'
import { selectMembersMap } from '@/src/store/features/memberSlice'

const MAX_PINNED_POSTS = 3

export interface PostHeaderProps {
  className?: string
  feed: IFeed
  isSinglePost?: boolean
  isPostPage?: boolean
  userData: IUser
  showPinnedMessageIndicator?: boolean
  extraHeaderComponents?: React.ReactNode
  onPinMessage?: (feed: IFeed) => void
  onUnpinMessage?: (feed: IFeed) => void
  membership: IMembership
}

export function PostHeader({
  className,
  feed,
  isSinglePost,
  extraHeaderComponents,
  onPinMessage,
  onUnpinMessage,
  membership,
}: PostHeaderProps) {
  const user = useStore((state) => state.auth.user)
  const pinnedPostsCount = useAppSelector((state) => selectPinnedPostsCount(state))
  const dispatch = useAppDispatch()
  const mountedRef = useMounted(true)
  const [, setCurrentTime] = useState(Date.now()) // This is used to force a re-render of the activity time
  const { isCurrentUserAdmin } = useCheckUserRole()

  const members = useSelector((state: RootState) => selectMembersMap(state))
  const { isAdminOrCreatorActor } = checkStreamUserRole(feed.user, members)
  const isOwnPost = feed.user.id === user?.id
  const hideContextMenu = !isOwnPost && isAdminOrCreatorActor && user?.role === USER_ROLE_ENUM.member
  const [editingPost, setEditingPost] = useState(false)

  useEffect(() => {
    if (!mountedRef.current) return
    setCurrentTime(Date.now())
    const interval = setInterval(() => {
      if (mountedRef.current) {
        setCurrentTime(Date.now())
      }
    }, 60000)
    return () => clearInterval(interval)
  }, [feed.created_at, feed.createdAt, mountedRef])

  const handleDelete = () => {
    dispatch(setFeedToDelete(feed))
    searchClient.clearCache()
  }
  const handleEdit = () => {
    setEditingPost(true)
    dispatch(openDialog({ dialog: 'EditPost', open: true, props: { data: feed, mode: 'edit' } }))
  }
  const handleReport = () => {
    dispatch(
      reportFeed({
        data: {
          id: feed.id as string,
          feed_status: FEED_STATUS_ENUM.reported,
          reports: [
            ...(feed.reports || []),
            {
              date: new Date().toUTCString(),
              name: getFullName(user.first_name, user.last_name, ''),
              email: user.email,
            },
          ],
        },
        messages: {
          success: 'Successfully reported to community admins.',
          fail: 'Failed to report to community admins.',
        },
      }),
    )
  }

  const handlePin = async (pin: boolean) => {
    if (pin) {
      onPinMessage(feed)
    } else {
      onUnpinMessage(feed)
    }
  }

  const createdAt = new Date(feed.created_at || feed.createdAt).getTime()
  const canPinPosts = pinnedPostsCount < MAX_PINNED_POSTS

  const getSpaceData = (message: IFeed) => {
    const [, channelId] = message.cid.split(':')
    return membership.channels.find((s: IChannel) => s.id === channelId)
  }

  const streamUser = feed.user as IUser
  const name = streamUser?.name || getFullName(streamUser?.first_name, streamUser?.last_name)
  const isFeedCreator = feed.user.id === user?.id
  const editable = isCurrentUserAdmin || isFeedCreator

  const spaceData = getSpaceData(feed)
  if (!spaceData) {
    // NOTE: We don't render the header if we don't have the membership in context yet.
    return null
  }

  const spaceURL = spaceData?.slug ? `${membership.slug}?space=${spaceData.slug}` : ''

  const linkUserProfile = streamUser?.status !== 'deleted' && streamUser?.username

  const isUserAllowedToPost = isUserActiveAndAcceptedInCommunity(user, membership)

  const UserProfileComponent = ({ children }: { children: React.ReactNode }) =>
    linkUserProfile ? (
      <UserDetailsHoverCard username={streamUser.username}>
        <Link
          className="text-sm font-semibold text-black-700 hover:text-grey-700 dark:text-white-500 hover:dark:text-grey-100"
          href={`/@${streamUser?.username}`}
          onClick={(e) => e.stopPropagation()}
        >
          {children}
        </Link>
      </UserDetailsHoverCard>
    ) : (
      <span className="cursor-default font-semibold text-black-700 dark:text-white-500">{children}</span>
    )

  return (
    <div className={cn('relative flex w-full flex-col justify-between', className)} data-testid="post-header">
      <div>
        <div className="flex w-full flex-nowrap rounded-2xl pt-0">
          <div className="mr-3.5">
            <ProfilePicture
              className={cn(!linkUserProfile && 'cursor-auto')}
              src={streamUser?.image || streamUser?.profile?.image}
              cropArea={streamUser?.image_crop_area || streamUser?.profile?.image_crop_area}
              alt={name || ''}
              width={40}
              height={40}
            />
          </div>
          <div className="flex grow flex-row">
            <div className="flex grow flex-col">
              <div className="flex items-center">
                <UserProfileComponent>
                  <div className="flex items-center gap-1">
                    {name ? name : 'No Name'}
                    {isAdminOrCreatorActor && <Verified16Icon className="text-community-primary" />}
                  </div>
                </UserProfileComponent>
              </div>
              <div className="mt-0.5 flex items-center">
                <AdaptiveTooltipProvider delayDuration={700}>
                  <AdaptiveTooltip>
                    <AdaptiveTooltipTrigger asChild>
                      <div className="cursor-pointer select-none text-xs text-black-200 dark:text-black-100">
                        {formatDistanceToNowMinimal(new Date(createdAt))} ago&nbsp;in&nbsp;
                      </div>
                    </AdaptiveTooltipTrigger>
                    <AdaptiveTooltipContent side="bottom">
                      {formatDateLong(new Date(feed.created_at))}
                    </AdaptiveTooltipContent>
                  </AdaptiveTooltip>
                </AdaptiveTooltipProvider>
                <Link
                  className="text-xs font-semibold text-black-200 hover:text-black-100 dark:text-black-100 dark:hover:text-black-200"
                  href={spaceURL}
                  onClick={(e) => {
                    e.stopPropagation()
                  }}
                >
                  {spaceData.name}
                </Link>
              </div>
            </div>
            <div className={''}>{extraHeaderComponents}</div>
          </div>
          {isSinglePost && !hideContextMenu && isUserAllowedToPost && (
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button aria-label="More options" className="grow-0 self-start" variant="inline">
                  <MoreHorizontal20Icon className="text-black-100 hover:text-white-200 dark:text-black-100 dark:hover:text-white-200" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {isCurrentUserAdmin && (
                  <DropdownMenuItem onClick={() => handlePin(!feed.pinned)} disabled={!canPinPosts && !feed.pinned}>
                    {feed.pinned ? 'Unpin' : 'Pin'}
                  </DropdownMenuItem>
                )}
                {editable && <DropdownMenuItem onClick={handleEdit}>Edit</DropdownMenuItem>}
                {editable && (
                  <DropdownMenuItem
                    className="text-red-200 hover:text-red-200 focus:text-red-200"
                    onClick={handleDelete}
                  >
                    Delete
                  </DropdownMenuItem>
                )}
                {!isOwnPost && <DropdownMenuItem onClick={handleReport}>Report</DropdownMenuItem>}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
      {membership?.id && (
        <EditPost
          membership={membership}
          data={feed}
          mode="edit"
          open={editingPost}
          onClose={() => {
            setEditingPost(null)
          }}
        />
      )}
    </div>
  )
}
