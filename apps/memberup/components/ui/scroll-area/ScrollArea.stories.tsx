import type { <PERSON>a, StoryObj } from '@storybook/react'

import { ScrollArea } from './ScrollArea'

const meta: Meta<typeof ScrollArea> = {
  title: 'UI/ScrollArea',
  component: ScrollArea,
  parameters: {
    layout: 'centered',
  },
}

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: () => {
    return (
      <ScrollArea className="h-[200px] w-[350px] rounded-md border p-4">
        <PERSON><PERSON><PERSON> began sneaking into the castle in the middle of the night and leaving jokes all over the place: under
        the king's pillow, in his soup, even in the royal toilet. The king was furious, but he couldn't seem to stop
        <PERSON><PERSON><PERSON>. And then, one day, the people of the kingdom discovered that the jokes left by <PERSON><PERSON><PERSON> were so funny
        that they couldn't help but laugh. And once they started laughing, they couldn't stop.
      </ScrollArea>
    )
  },
}
