import { toast as sonnerToast } from 'sonner'

import { Check20Icon, Info20Icon, Report20Icon } from '@/components/icons'
import { cn } from '@/lib/utils'

const ToastContainer = ({ className, ...props }: any) => {
  return (
    <div
      className={cn(
        'tailwind-component toast-container group rounded-base p-4 bg-white-500 dark:bg-black-500 border border-grey-200 dark:border-grey-900 text-black-700 dark:text-white-500 font-semibold text-ssm shadow-xl text-left flex',
        className
      )}
      {...props}
    />
  )
}

export const toast = {
  success: (message: string) => {
    sonnerToast.success(
      <ToastContainer>
        <Check20Icon className="text-green-200 mr-2" />
        {message}
      </ToastContainer>
    )
  },
  error: (message: string) => {
    sonnerToast.error(
      <ToastContainer>
        <Report20Icon className="text-red-200 mr-2" />
        {message}
      </ToastContainer>
    )
  },
  info: (message: string) => {
    sonnerToast.info(
      <ToastContainer>
        <Info20Icon className="text-orange-100 mr-2" />
        {message}
      </ToastContainer>
    )
  },
}
