import { cn } from '@/lib/utils'

function Skeleton({
  children,
  className,
  variant,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & { variant?: 'normal' | 'dialog' }) {
  return (
    <div
      className={cn(
        'animate-pulse rounded-md',
        variant === 'normal' ? 'bg-grey-700/10 dark:bg-black-500' : 'bg-grey-500/10 dark:bg-black-400',
        className,
      )}
      {...props}
    >
      {children}
    </div>
  )
}

export { Skeleton }
