'use client'

import * as SliderPrimitive from '@radix-ui/react-slider'
import * as React from 'react'

import { cn } from '@/lib/utils'

type SliderVariant = 'default' | 'community-primary'

type SliderProps = React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root> & {
  variant?: SliderVariant
}

const Slider = React.forwardRef<React.ElementRef<typeof SliderPrimitive.Root>, SliderProps>(
  ({ className, variant = 'default', ...props }, ref) => {
    const rangeClassName = variant === 'community-primary' ? 'bg-community-secondary' : 'bg-primary-100'

    const thumbClassName =
      variant === 'community-primary'
        ? 'bg-community-secondary hover:brightness-[95%]'
        : 'bg-primary-200 hover:brightness-[95%]'

    return (
      <SliderPrimitive.Root
        ref={ref}
        className={cn('relative flex w-full touch-none select-none items-center', className)}
        {...props}
      >
        <SliderPrimitive.Track className="relative h-[0.3125rem] w-full grow overflow-hidden rounded-full bg-black-100/30">
          <SliderPrimitive.Range className={cn('absolute h-full', rangeClassName)} />
        </SliderPrimitive.Track>
        <SliderPrimitive.Thumb
          className={cn(
            'block h-4 w-4 cursor-pointer rounded-full shadow transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50',
            thumbClassName,
          )}
        />
      </SliderPrimitive.Root>
    )
  },
)
Slider.displayName = SliderPrimitive.Root.displayName

export { Slider }
