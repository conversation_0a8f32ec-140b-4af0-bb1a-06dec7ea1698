import { cn } from '@/lib/utils'

export function NotificationsIndicator({
  children,
  count,
  small,
}: {
  children?: React.ReactNode
  count: number
  small?: boolean
}) {
  if (!count) return children

  return (
    <div className="relative">
      <div
        className={cn(
          'absolute left-1/2 -mt-[50%] flex items-center justify-center rounded-full bg-red-200 text-center font-inter-numbers font-medium transition-opacity group-hover:opacity-95',
          count > 10 && count < 100 ? 'text-sxs' : 'text-ssm',
          small
            ? '-top-0.5 h-[1.125rem] w-[1.125rem] leading-[1.125rem] text-white-500'
            : 'h-5 w-5 leading-5 text-white-500',
        )}
      >
        {count < 100 ? count : '∞'}
      </div>
      {children}
    </div>
  )
}
