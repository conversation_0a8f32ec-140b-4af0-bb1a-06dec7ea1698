import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'

import { Checkbox } from './Checkbox'

const meta: Meta<typeof Checkbox> = {
  title: 'UI/Checkbox',
  component: Checkbox,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    children: {
      control: {
        type: 'text',
      },
    },
  },
  args: {
    children: 'Accept terms and conditions',
  },
}

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: ({ children, ...args }) => {
    return (
      <div className="flex items-center space-x-2 p-12 dark:bg-black-500">
        <Checkbox {...args} id="checkbox">
          {children}
        </Checkbox>
        <label
          htmlFor="checkbox"
          className="cursor-pointer text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {children}
        </label>
      </div>
    )
  },
}
