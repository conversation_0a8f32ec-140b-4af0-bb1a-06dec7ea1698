import * as React from 'react'

import { cn } from '@/lib/utils'

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

const StandardInput = React.forwardRef<HTMLInputElement, InputProps>(({ className, type, ...props }, ref) => {
  return (
    <input
      type={type}
      className={cn(
        'flex h-10 w-full rounded-lg border border-white-100 bg-white-100 px-3 py-1 text-sm text-black-600 shadow-sm outline-none transition-colors file:bg-white-100 file:text-sm file:font-medium placeholder:text-black-200 focus:border-black-200 focus:bg-transparent disabled:cursor-not-allowed disabled:opacity-50 dark:border-black-500 dark:bg-black-500 dark:text-white-200 dark:file:bg-black-500 dark:placeholder:text-black-100 dark:focus:border-black-100 dark:focus:bg-transparent',
        className,
      )}
      ref={ref}
      {...props}
    />
  )
})

StandardInput.displayName = 'StandardInput'

export { StandardInput }
