'use client'

import * as DialogPrimitive from '@radix-ui/react-dialog'
import { cva, type VariantProps } from 'class-variance-authority'
import * as React from 'react'

import { Close24Icon } from '@/components/icons'
import { overlayClassName } from '@/components/ui/overlay'
import { cn } from '@/lib/utils'

const Dialog = DialogPrimitive.Root

const DialogTrigger = DialogPrimitive.Trigger

const DialogPortal = DialogPrimitive.Portal

const DialogClose = DialogPrimitive.Close

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay> & { overlayAll?: boolean }
>(({ className, overlayAll = false, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(overlayClassName, overlayAll ? 'z-1000' : 'z-50', className)}
    {...props}
  />
))
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName

const dialogContentVariants = cva(
  'dialog-content fixed flex flex-col left-[50%] top-[50%] z-50 w-full max-w-[30rem] sm:max-h-[calc(100%_-_60px)] translate-x-[-50%] translate-y-[-50%] bg-white-500 dark:bg-black-500 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-2xl text-black-600 dark:text-white-500',
  {
    variants: {
      variant: {
        default: 'w-full h-full items-start max-w-none sm:w-full sm:h-auto sm:rounded-2xl sm:max-w-[29.75rem]',
        plain: 'shadow-none border-none',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
)

export interface DialogContentProps
  extends React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>,
    VariantProps<typeof dialogContentVariants> {
  overlay?: React.ReactNode
  overlayAll?: boolean
}

const DialogContent = React.forwardRef<React.ElementRef<typeof DialogPrimitive.Content>, DialogContentProps>(
  ({ className, children, overlay, variant = 'default', overlayAll = false, ...props }, ref) => (
    <DialogPortal>
      {overlay ?? <DialogOverlay overlayAll={overlayAll} />}
      <DialogPrimitive.Content
        ref={ref}
        className={cn(dialogContentVariants({ variant, className }))}
        /**
         * pointer-events: none bug workaround
         * Fixes an issue where pointer-events remain disabled after closing the dialog
         * Solution from: https://github.com/shadcn-ui/ui/issues/1859#issuecomment-2589432274
         */
        onCloseAutoFocus={(event) => {
          event.preventDefault()
          document.body.style.pointerEvents = ''
        }}
        {...props}
      >
        {children}
        {variant === 'default' && (
          <DialogPrimitive.Close className="outline:none absolute right-5 top-5 flex h-10 w-10 items-center justify-center rounded-full bg-white-200 text-black-200 opacity-70 transition-colors hover:bg-grey-400 hover:opacity-100 focus:outline-none disabled:pointer-events-none dark:bg-black-700 dark:text-black-100 dark:hover:bg-grey-900 sm:hidden">
            <Close24Icon />
            <span className="sr-only">Close</span>
          </DialogPrimitive.Close>
        )}
      </DialogPrimitive.Content>
    </DialogPortal>
  ),
)
DialogContent.displayName = DialogPrimitive.Content.displayName

const DialogHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn('flex flex-col justify-center pl-8 pr-8 pt-8 text-left', className)} {...props} />
)
DialogHeader.displayName = 'DialogHeader'

const DialogInner = ({ children, className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn('mb-8 w-full shrink overflow-y-auto px-8', className)} {...props}>
    {children}
  </div>
)

const DialogFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'dialog-footer sm:space-y-normal flex w-full flex-col-reverse space-y-3 space-y-reverse px-8 pb-8 sm:flex-row sm:justify-end sm:space-x-2 sm:space-y-0',
      className,
    )}
    {...props}
  />
)
DialogFooter.displayName = 'DialogFooter'

const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      'dialog-title relative -top-0.5 mb-2 text-lg font-semibold text-black-700 dark:text-white-500',
      className,
    )}
    {...props}
  />
))
DialogTitle.displayName = DialogPrimitive.Title.displayName

const DialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ children, className, ...props }, ref) => (
  <DialogPrimitive.Description asChild ref={ref} {...props}>
    <div className={cn('mb-4 pr-12 text-sm text-muted-foreground sm:pr-0', className)}>{children}</div>
  </DialogPrimitive.Description>
))
DialogDescription.displayName = DialogPrimitive.Description.displayName

export {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogTrigger,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogInner,
  DialogFooter,
  DialogTitle,
  DialogDescription,
}
