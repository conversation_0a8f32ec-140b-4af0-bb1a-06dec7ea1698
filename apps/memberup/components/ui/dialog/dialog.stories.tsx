import { <PERSON>a, <PERSON>Obj } from '@storybook/react'

import { But<PERSON> } from '../button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogInner,
  DialogTitle,
  DialogTrigger,
} from '../dialog'
import { Input } from '../input'

const meta: Meta<typeof Dialog> = {
  title: 'ui/Dialog',
  component: Dialog,
  argTypes: {},
  parameters: {
    layout: 'centered',
  },
}
export default meta

type Story = StoryObj<typeof Dialog>

export const Base: Story = {
  render: () => (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">Edit Profile</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Change email</DialogTitle>
          <DialogDescription>
            <p>We'll send you an email to verify your new email.</p>
          </DialogDescription>
        </DialogHeader>
        <DialogInner>
          <div className="">
            <Input placeholder="Current passsword" />
          </div>
          <div className="mt-6 w-full">
            <Input placeholder="New email" />
          </div>
        </DialogInner>
        <DialogFooter>
          <Button type="submit">Save changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  ),
  args: {},
}
