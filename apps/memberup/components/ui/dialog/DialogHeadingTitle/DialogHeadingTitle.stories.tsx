import type { Meta, StoryObj } from '@storybook/react'

import { DialogHeadingTitle } from './DialogHeadingTitle'

const meta: Meta<typeof DialogHeadingTitle> = {
  title: 'Dialog/DialogHeadingTitle',
  component: DialogHeadingTitle,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    backAction: {
      control: {
        type: 'select',
        labels: {
          true: 'backAction function provided',
          false: 'backAction function not provided',
        },
      },
      mapping: {
        true: () => {},
        false: undefined,
      },
      options: [true, false],
      label: 'Back action',
    },
    title: {
      control: 'text',
      label: 'Title',
    },
  },
  args: {
    /** @ts-expect-error https://github.com/storybookjs/storybook/issues/22578 */
    backAction: true,
    className: 'w-96',
    title: 'Dialog heading title',
  },
}

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: (args) => (
    <DialogHeadingTitle {...args} className={args.className} backAction={args.backAction} title={args.title} />
  ),
}
