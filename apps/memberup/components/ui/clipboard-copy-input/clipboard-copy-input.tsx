'use client'

import { useState } from 'react'

import { Button } from '@/components/ui'
import { useMountedRef } from '@/hooks/useMountedRef'

export function ClipboardCopyInput({ value }: { value: string }) {
  const mountedRef = useMountedRef()
  const [copied, setCopied] = useState(false)

  const copyToClipboard = async () => {
    await navigator.clipboard.writeText(value)

    setCopied(true)
    setTimeout(() => {
      if (mountedRef.current) {
        setCopied(false)
      }
    }, 3000)
  }

  return (
    <div className="flex items-center rounded-xl bg-black-200 p-1 pl-4 dark:bg-black-300">
      <div className="grow text-sm font-normal text-black-200 dark:text-black-100">{value}</div>
      <Button onClick={copyToClipboard} size="sm" variant="community-primary">
        {copied ? 'Copied!' : 'Copy'}
      </Button>
    </div>
  )
}
