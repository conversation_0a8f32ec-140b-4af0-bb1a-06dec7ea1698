import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { cn } from '@/lib/utils'

export function BoxesRadioGroup({
  options,
  onValueChange,
  value,
}: {
  options: {
    label: string
    value: string
    description: string
  }[]
  onValueChange: (value: string) => void
  value: string
}) {
  return (
    <RadioGroup className="md:flex md:space-x-5" defaultValue={value} onValueChange={onValueChange}>
      {options.map((option) => (
        <Label
          key={option.value}
          htmlFor={option.value}
          className={cn(
            'flex-1 cursor-pointer select-none rounded-lg border transition-colors',
            option.value === value
              ? 'border-2 border-black-200 bg-white-100 dark:bg-grey-800'
              : 'border-black-200/30 p-px transition-colors hover:border-black-200',
          )}
        >
          <div className="p-5">
            <div className="flex">
              <RadioGroupItem className="mr-3" value={option.value} id={option.value} variant="flat" />
              <div className="text-sm font-medium text-black-700 dark:text-white-500">{option.label}</div>
            </div>
            <div className="mt-3 text-ssm font-normal text-black-200 dark:text-black-100">{option.description}</div>
          </div>
        </Label>
      ))}
    </RadioGroup>
  )
}
