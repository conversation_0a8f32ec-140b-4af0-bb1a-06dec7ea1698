import { <PERSON>a, <PERSON>Obj } from '@storybook/react'
import { useState } from 'react'

import { Input } from './Input'
import { PasswordInput } from './PasswordInput'

const meta: Meta<typeof Input> = {
  title: 'ui/Input',
  component: Input,
  parameters: {
    layout: 'centered',
  },
  argTypes: {},
}
export default meta

type Story = StoryObj<typeof Input>

const InputWithState = (props: any) => {
  const [value, setValue] = useState<string | null>('')

  return (
    <Input
      className="w-72"
      value={value}
      variant={props.variant}
      onChange={(e: any) => setValue(e.target.value)}
      {...props}
    />
  )
}

export const Default: Story = {
  render: (args) => <InputWithState {...args} />,
  args: {
    type: 'email',
    placeholder: 'Email',
  },
}

export const Error: Story = {
  render: (args) => <InputWithState {...args} error />,
  args: {
    type: 'email',
    placeholder: 'Email',
  },
}

export const Disabled: Story = {
  render: (args) => (
    <div className="flex flex-col">
      <InputWithState {...args} disabled className="mb-12" />
      <InputWithState {...args} value="Input value" disabled />
    </div>
  ),
  args: { ...Default.args },
}

const PasswordInputWithState = (props: any) => {
  const [value, setValue] = useState<string | null>(null)

  return (
    <PasswordInput
      className="w-72"
      value={value}
      variant={props.variant}
      onChange={(e: any) => setValue(e.target.value)}
      placeholder="Password"
      {...props}
    />
  )
}

export const Password: Story = {
  render: (args) => <PasswordInputWithState {...args} />,
}

export const PasswordDisabled: Story = {
  render: (args) => <PasswordInputWithState {...args} disabled value="memberup" />,
}
