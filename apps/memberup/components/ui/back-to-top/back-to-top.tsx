'use client'

import { useEffect, useState } from 'react'

import { ChevronDown24Icon } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

export function BackToTop() {
  const [isScrollable, setIsScrollable] = useState(false)
  const [scrollTop, setScrollTop] = useState(0)

  const checkScrollable = () => {
    const viewport = document.getElementById('app-scroll-area-viewport')
    if (!viewport) return

    const hasScroll = viewport.scrollHeight > viewport.clientHeight
    setIsScrollable(hasScroll)
  }

  useEffect(() => {
    checkScrollable()

    const viewport = document.getElementById('app-scroll-area-viewport')
    if (!viewport) return

    const resizeObserver = new ResizeObserver(() => {
      checkScrollable()
    })

    resizeObserver.observe(viewport)

    const handleScroll = () => {
      if (!viewport) return
      setScrollTop(viewport.scrollTop)
    }
    viewport.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      resizeObserver.disconnect()
      viewport?.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const scrollToTop = () => {
    const viewport = document.getElementById('app-scroll-area-viewport')
    viewport?.scrollTo({ top: 0, behavior: 'smooth' })
  }

  if (!isScrollable || scrollTop === 0) return null

  return (
    <Button
      onClick={scrollToTop}
      className={cn(
        'hover:bg-white-600 fixed bottom-8 right-8 z-[9999] h-10 w-10 rounded-full bg-white-500 p-0 opacity-100 shadow-lg transition-all duration-300 dark:bg-black-700 dark:hover:bg-black-600',
      )}
      aria-label="Back to top"
    >
      <ChevronDown24Icon className="rotate-180" />
    </Button>
  )
}
