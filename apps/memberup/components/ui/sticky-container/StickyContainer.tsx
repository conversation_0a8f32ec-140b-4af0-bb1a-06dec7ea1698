'use client'

import { Slot } from '@radix-ui/react-slot'

import { useStore } from '@/hooks/useStore'
import { cn } from '@/lib/utils'

export function StickyContainer({
  asChild,
  children,
  className,
}: {
  asChild?: boolean
  children: React.ReactNode
  className?: string
}) {
  const Comp = asChild ? Slot : 'div'
  const stickyItemsOffset = useStore((state) => state.ui.stickyItemsOffset)

  return (
    <Comp
      className={cn('w-full md:sticky', className)}
      style={{
        top: `${stickyItemsOffset}px`,
      }}
    >
      {children}
    </Comp>
  )
}
