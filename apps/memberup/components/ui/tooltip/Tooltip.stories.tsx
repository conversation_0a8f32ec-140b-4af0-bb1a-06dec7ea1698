import { <PERSON>a, StoryObj } from '@storybook/react'
import { useState } from 'react'

import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

const meta: Meta<typeof Tooltip> = {
  title: 'ui/Tooltip',
  component: Tooltip,
  parameters: {
    layout: 'centered',
  },
}
export default meta

type Story = StoryObj<typeof Tooltip>

const TooltipStoryComponent = () => {
  const [dateTooltipOpen, setDateTooltipOpen] = useState(false)

  return (
    <TooltipProvider>
      <Tooltip open={dateTooltipOpen} onOpenChange={setDateTooltipOpen}>
        <TooltipTrigger
          asChild
          // Workaround from: https://github.com/radix-ui/primitives/issues/955#issuecomment-**********
          onClick={() => setDateTooltipOpen((prevOpen) => !prevOpen)}
          // Timeout runs setOpen after onOpenChange to prevent bug on mobile
          onFocus={() => setTimeout(() => setDateTooltipOpen(true), 0)}
          onBlur={() => setDateTooltipOpen(false)}
        >
          <Badge>Hover for tooltip</Badge>
        </TooltipTrigger>
        <TooltipContent>
          Activities: <span className="font-semibold text-font-light-ui-black dark:text-pure-white">8</span>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

export const Base: Story = {
  render: () => <TooltipStoryComponent />,
  args: {},
}
