import { useRef, useState } from 'react'

import { ConfirmModal } from '@/components/ui/confirm-modal'
import { TiptapEditorField } from '@/components/ui/tiptap/editor-field'
import { cn } from '@/lib/utils'

export default function TextEditorField({
  characterLimit,
  className,
  editing,
  placeholder,
  setEditing,
  setValue,
  value,
  autoFocus = true,
}: {
  characterLimit?: number
  className?: string
  editing: boolean
  label?: string
  placeholder?: string
  setEditing: (editing: boolean) => void
  setValue: (value: string) => void
  value: string
  autoFocus?: boolean
}) {
  const preEditingValueRef = useRef(value)
  const [discardChangesWarningOpen, setDiscardChangesWarningOpen] = useState(false)

  if (!editing) {
    return (
      <div
        className={cn('cursor-pencil', className)}
        onClick={() => {
          preEditingValueRef.current = value
          setEditing(true)
        }}
      >
        {value ? (
          <div
            className="rendered-editor-text"
            dangerouslySetInnerHTML={{
              __html: value,
            }}
          />
        ) : (
          <div className="rendered-editor-text">{placeholder}</div>
        )}
      </div>
    )
  }

  return (
    <div className={cn(className)}>
      <TiptapEditorField
        characterLimit={characterLimit}
        initialValue={value}
        onUpdate={({ editor }) => {
          setValue(editor.getHTML())
        }}
        placeholder={placeholder}
        autoFocus={autoFocus}
      />
      <ConfirmModal
        title="Are you sure you want to discard changes?"
        onConfirm={() => {
          setValue(preEditingValueRef.current)
          setDiscardChangesWarningOpen(false)
          setEditing(false)
        }}
        open={discardChangesWarningOpen}
        onCancel={() => setDiscardChangesWarningOpen(false)}
      />
    </div>
  )
}
