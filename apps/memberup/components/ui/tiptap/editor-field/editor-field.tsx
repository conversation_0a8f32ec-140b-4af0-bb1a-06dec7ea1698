import '@/app/ui/tiptap.css'

import CharacterCount from '@tiptap/extension-character-count'
import Link from '@tiptap/extension-link'
import Underline from '@tiptap/extension-underline'
import { EditorContent, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import React from 'react'

import { Link16Icon } from '@/components/icons/16px/link-16-icon'
import { BoldIcon } from '@/components/icons/text-editor/bold'
import { BulletListIcon } from '@/components/icons/text-editor/bullet-list'
import { ItalicIcon } from '@/components/icons/text-editor/italic'
import { UnderlineIcon } from '@/components/icons/text-editor/underline'
import { Control, ControlTypes } from '@/components/ui/control'
import { cn } from '@/lib/utils'

export function TiptapEditorField({
  characterLimit,
  className,
  disabled,
  error,
  initialValue,
  onUpdate,
  placeholder,
  rightContent,
  autoFocus = true,
}: {
  characterLimit?: number
  className?: string
  disabled?: boolean
  error?: boolean
  initialValue?: string
  onUpdate: (editor: any) => void
  placeholder?: string
  rightContent?: React.ReactNode
  autoFocus?: boolean
}) {
  const [active, setActive] = React.useState(Boolean(initialValue))
  const [focused, setFocused] = React.useState(true)

  const editorExtensions: any[] = [Link, StarterKit, Underline]

  if (characterLimit) {
    editorExtensions.push(
      CharacterCount.configure({
        limit: characterLimit,
      }),
    )
  }

  const editor = useEditor({
    autofocus: autoFocus ? 'end' : false,
    extensions: editorExtensions,
    content: initialValue || '',
    onFocus: () => {
      setFocused(true)
    },
    onBlur: () => {
      setFocused(false)
    },
    onUpdate: (event) => {
      const { editor } = event

      if (editor.isEmpty) {
        if (active) {
          setActive(false)
        }
      } else if (!active) {
        setActive(true)
      }

      onUpdate(event)
    },
  })

  return (
    <>
      <Control
        type={ControlTypes.textarea}
        active={false}
        className={cn(className, '[&_div.input-container]:overflow-visible', '[&_label]:hidden', '[&_legend]:hidden')}
        disabled={disabled}
        rightContent={rightContent}
        error={error}
        focused={focused}
        placeholder={placeholder}
      >
        <div className="relative z-50 mt-[-0.6875rem] flex w-full flex-col items-start p-2">
          {editor && (
            <div className="mb-[0.625rem] flex items-center gap-1">
              <button
                onClick={() => editor.chain().focus().toggleBold().run()}
                className={cn(
                  'flex h-6 w-6 items-center justify-center rounded-lg text-black-200 transition-colors hover:bg-white-100 dark:text-black-100 hover:dark:bg-black-300',
                  editor.isActive('bold') ? 'bg-white-100 dark:bg-black-300' : 'bg-transparent',
                )}
              >
                <BoldIcon className="h-4 w-4" />
              </button>
              <button
                className={cn(
                  'flex h-6 w-6 items-center justify-center rounded-lg text-black-200 transition-colors hover:bg-white-100 dark:text-black-100 hover:dark:bg-black-300',
                  editor.isActive('italic') ? 'bg-white-100 dark:bg-black-300' : 'bg-transparent',
                )}
                onClick={() => editor.chain().focus().toggleItalic().run()}
              >
                <ItalicIcon className="h-4 w-4" />
              </button>
              <button
                className={cn(
                  'flex h-6 w-6 items-center justify-center rounded-lg text-black-200 transition-colors hover:bg-white-100 dark:text-black-100 hover:dark:bg-black-300',
                  editor.isActive('link') ? 'bg-white-100 dark:bg-black-300' : 'bg-transparent',
                )}
                onClick={() => {
                  const url = window.prompt('URL')
                  if (url) {
                    editor.chain().focus().setLink({ href: url }).run()
                  }
                }}
              >
                <Link16Icon className="h-4 w-4" />
              </button>
              <button
                className={cn(
                  'flex h-6 w-6 items-center justify-center rounded-lg text-black-200 transition-colors hover:bg-white-100 dark:text-black-100 hover:dark:bg-black-300',
                  editor.isActive('underline') ? 'bg-white-100 dark:bg-black-300' : 'bg-transparent',
                )}
                onClick={() => editor.chain().focus().toggleUnderline().run()}
              >
                <UnderlineIcon className="h-4 w-4" />
              </button>
              <button
                className={cn(
                  'flex h-6 w-6 items-center justify-center rounded-lg text-black-200 transition-colors hover:bg-white-100 dark:text-black-100 hover:dark:bg-black-300',
                  editor.isActive('bulletList') ? 'bg-white-100 dark:bg-black-300' : 'bg-transparent',
                )}
                onClick={() => editor.chain().focus().toggleBulletList().run()}
              >
                <BulletListIcon className="h-4 w-4" />
              </button>
            </div>
          )}
          <EditorContent
            className="rendered-editor-text w-full pl-1 pt-3 text-black-600 dark:text-white-200"
            editor={editor}
          />
          {editor?.isEmpty && (
            <div className="pointer-events-none absolute left-3 top-14 text-sm text-black-200 dark:text-black-100">
              {placeholder}
            </div>
          )}
        </div>
      </Control>
      {editor && characterLimit && (
        <div className="mt-[0.375rem] text-right text-xs text-black-200 dark:text-black-100">
          {editor.storage.characterCount.characters()} / {2000} characters
        </div>
      )}
    </>
  )
}
