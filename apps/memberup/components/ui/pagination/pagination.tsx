import * as React from 'react'

import { ChevronLeft20Icon, ChevronRight20Icon } from '@/components/icons'
import { Button, ButtonProps } from '@/components/ui/button'
import { cn } from '@/lib/utils'

const Pagination = ({ className, ...props }: React.ComponentProps<'nav'>) => (
  <nav
    role="navigation"
    aria-label="pagination"
    className={cn('mx-auto mt-6 flex w-full max-w-[530px] justify-center', className)}
    {...props}
  />
)
Pagination.displayName = 'Pagination'

const PaginationContent = React.forwardRef<HTMLUListElement, React.ComponentProps<'ul'>>(
  ({ className, ...props }, ref) => (
    <ul ref={ref} className={cn('flex w-full flex-row items-center justify-between gap-1', className)} {...props} />
  ),
)
PaginationContent.displayName = 'PaginationContent'

const PaginationItem = React.forwardRef<HTMLLIElement, React.ComponentProps<'li'>>(({ className, ...props }, ref) => (
  <li ref={ref} className={cn('', className)} {...props} />
))
PaginationItem.displayName = 'PaginationItem'

type PaginationNextPrevProps = {
  isActive?: boolean
} & ButtonProps &
  React.ComponentProps<typeof Button>

type PaginationLinkProps = {
  isActive?: boolean
  page: number
  onClick: (page: number) => void
} & React.ComponentProps<typeof Button>

const PaginationLink = ({ className, isActive, page, onClick, ...props }: PaginationLinkProps) => (
  <Button
    aria-current={isActive ? 'page' : undefined}
    className={cn(
      'h-10 w-10 rounded-full text-center',
      isActive
        ? 'bg-primary-100 text-white-500'
        : 'bg-grey-200 text-black-200 hover:text-black-100 dark:bg-grey-800 dark:text-black-100 dark:hover:text-black-200',
      className,
    )}
    variant="inline"
    onClick={() => onClick(page)}
    {...props}
  >
    {page}
  </Button>
)
PaginationLink.displayName = 'PaginationLink'

const PaginationNextPrev = ({ children, className, isActive, ...props }: PaginationNextPrevProps) => (
  <Button
    aria-current={isActive ? 'page' : undefined}
    className="h-10 w-10 whitespace-nowrap rounded-full bg-grey-200 leading-10 text-black-200 transition-colors disabled:text-grey-400 dark:bg-grey-800 dark:text-black-100 disabled:dark:text-grey-700 md:w-auto md:rounded-none md:bg-transparent md:px-5 md:dark:bg-transparent [&:not(:disabled)]:hover:text-black-100 [&:not(:disabled)]:hover:dark:text-black-200"
    variant="inline"
    {...props}
  >
    {children}
  </Button>
)

const PaginationPrevious = ({ className, ...props }: React.ComponentProps<typeof PaginationNextPrev>) => (
  <PaginationNextPrev aria-label="Go to previous page" size="default" className={cn(className)} {...props}>
    <ChevronLeft20Icon className="h-5 md:mr-2" />
  </PaginationNextPrev>
)
PaginationPrevious.displayName = 'PaginationPrevious'

const PaginationNext = ({ className, ...props }: React.ComponentProps<typeof PaginationNextPrev>) => (
  <PaginationNextPrev aria-label="Go to next page" size="default" className={cn(className)} {...props}>
    <ChevronRight20Icon className="h-5 md:ml-2" />
  </PaginationNextPrev>
)
PaginationNext.displayName = 'PaginationNext'

const PaginationEllipsis = ({ className, ...props }: React.ComponentProps<'span'>) => (
  <span aria-hidden className={cn('flex h-9 w-9 items-center justify-center', className)} {...props}>
    ...
    <span className="sr-only">More pages</span>
  </span>
)
PaginationEllipsis.displayName = 'PaginationEllipsis'

export {
  Pagination,
  PaginationContent,
  PaginationLink,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationEllipsis,
}
