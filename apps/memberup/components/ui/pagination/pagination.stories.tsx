import { <PERSON>a, <PERSON>Obj } from '@storybook/react'

import { Pagin<PERSON>, PaginationContent, PaginationItem, PaginationNext, PaginationPrevious } from './pagination'

const meta: Meta<typeof Pagination> = {
  title: 'ui/Pagination',
  component: Pagination,
  argTypes: {},
  parameters: {
    layout: 'centered',
  },
}

export default meta

type Story = StoryObj<typeof Pagination>

export const Base: Story = {
  render: () => (
    <div className="w-80 md:w-96">
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious />
          </PaginationItem>
          {/*
          <PaginationItem>
            <PaginationLink href="#">1</PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <PaginationLink href="#" isActive>
              2
            </PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <PaginationLink href="#">3</PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <PaginationEllipsis />
          </PaginationItem>
          */}
          <PaginationItem>
            <PaginationNext />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  ),
}
