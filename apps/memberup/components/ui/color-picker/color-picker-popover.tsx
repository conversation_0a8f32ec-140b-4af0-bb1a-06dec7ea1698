'use client'

import { useEffect, useState } from 'react'
import { ColorResult } from 'react-color'

import { Popover, PopoverContent, PopoverTrigger } from '../popover'
import { ContrastCanvasOverlay } from './contrast-canvas-overlay'
import { ColorPicker } from '@/components/ui/color-picker'
import {
  getColorContrastCurves,
  getDefaultColorFormats,
  getNearestValidColorAtX,
  isColorPositionValid,
} from '@/lib/colors'
import { initialColorPickerHex, saturationAreaHeight, saturationAreaWidth } from '@/lib/constants'
import { TColorPickerContrastReferenceColor } from '@/shared-types/types'

interface ColorPickerPopoverProps {
  children: React.ReactNode
  onChange: (color: string) => void
  open: boolean
  setOpen: (open: boolean) => void
  value: string
  referenceColors?: TColorPickerContrastReferenceColor[]
}

export const ColorPickerPopover = ({
  children,
  onChange,
  open,
  setOpen,
  value,
  referenceColors = [],
}: ColorPickerPopoverProps) => {
  const [color, setColor] = useState(getDefaultColorFormats(initialColorPickerHex))
  let currentHue = color.hsl.h

  if (isNaN(currentHue)) {
    currentHue = 0
  }

  const { upperCurve, lowerCurve } =
    referenceColors.length > 0
      ? getColorContrastCurves(saturationAreaWidth, saturationAreaHeight, currentHue, referenceColors)
      : { upperCurve: null, lowerCurve: null }

  useEffect(() => {
    if (!referenceColors.length) return
    const { s, v } = getDefaultColorFormats(color.hex).hsv
    if (!isColorPositionValid(upperCurve, lowerCurve, s, v)) {
      const validColor = getNearestValidColorAtX(
        currentHue,
        s,
        v,
        upperCurve,
        lowerCurve,
        saturationAreaWidth,
        saturationAreaHeight,
      )
      setColor(getDefaultColorFormats(validColor.hex))
      onChange(validColor.hex)
    }
  }, [currentHue, upperCurve, lowerCurve, color.hex, onChange, referenceColors.length])

  // Sync overlay hue with selected color when opening popover from onboarding section
  useEffect(() => {
    if (value && value.toLowerCase() !== color.hex.toLowerCase()) {
      setColor(getDefaultColorFormats(value))
    }
  }, [value, color.hex])

  const handleColorChange = (newColor: ColorResult) => {
    const { s, v } = getDefaultColorFormats(newColor.hex).hsv
    if (referenceColors.length === 0 || isColorPositionValid(upperCurve, lowerCurve, s, v)) {
      setColor(getDefaultColorFormats(newColor.hex))
      onChange(newColor.hex)
    }
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>{children}</PopoverTrigger>
      <PopoverContent className="w-60">
        <div className="relative">
          <ColorPicker
            hex={color.hex}
            hsl={color.hsl}
            hsv={color.hsv}
            rgb={color.rgb}
            onChange={handleColorChange}
            color={value}
            upperCurve={upperCurve}
            lowerCurve={lowerCurve}
          />
          {referenceColors.length > 0 && (
            <ContrastCanvasOverlay width={224} height={160} hue={currentHue} referenceColors={referenceColors} />
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}
