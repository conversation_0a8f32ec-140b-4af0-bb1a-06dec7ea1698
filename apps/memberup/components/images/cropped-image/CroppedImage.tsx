import Image from 'next/image'
import { forwardRef, memo } from 'react'

import { cloudinaryLoader, generateCloudinaryPretransforms } from '@/lib/cloudinary'
import { cn } from '@/lib/utils'
import { TAppCropArea } from '@/shared-types/types'

interface CroppedImageProps {
  src: string
  className?: string
  cropArea?: TAppCropArea
  priority?: boolean
  width: number
  height: number
  alt?: string
}

const CroppedImage = memo(
  forwardRef<HTMLDivElement, CroppedImageProps>(
    ({ src, className, cropArea, priority = false, width, height, alt }, ref) => {
      const imgUrl = src as string

      if (!imgUrl || imgUrl === 'null' || imgUrl === 'undefined') return null

      const preTransforms = generateCloudinaryPretransforms(cropArea)

      return (
        <div className={cn('cropped-image relative h-full w-full overflow-hidden', className)} ref={ref}>
          <Image
            className="h-full max-h-full w-full select-none object-cover"
            src={imgUrl}
            alt={alt || ''}
            width={width}
            height={height}
            loader={(loaderOptions) => cloudinaryLoader({ loaderOptions, preTransforms })}
            priority={priority}
          />
        </div>
      )
    },
  ),
)

CroppedImage.displayName = 'CroppedImage'

export { CroppedImage }
