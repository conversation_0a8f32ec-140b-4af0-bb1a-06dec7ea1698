import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { resetPasswordApi, resetPasswordVerifyTokenApi } from '@memberup/shared/src/services/apis/reset-password.api'
import { Button, ControlVariants, Input, PasswordInput } from '@/components/ui'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { toast } from '@/components/ui/sonner'
import { failedToUpdatePasswordError, unexpectedError } from '@/lib/error-messages'
import { passwordSchema, passwordsMatch, passwordsMatchMessage } from '@/lib/validation/zod'
import { ResetPasswordErrorCode } from '@/shared-types/enum'

const FormSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: z.string(),
  })
  .refine(passwordsMatch, passwordsMatchMessage)

type FormDataType = z.infer<typeof FormSchema>

interface ChangePasswordFormProps {
  className?: string
  onUpdatedPassword: (e: boolean) => void
  onTokenExpired: () => void
  onWrongUser: () => void
  onInvalidToken: () => void
}

export function ChangePasswordForm({
  className,
  onUpdatedPassword,
  onTokenExpired,
  onWrongUser,
  onInvalidToken,
}: ChangePasswordFormProps) {
  const mountedRef = useMounted(true)
  const params = useSearchParams()
  const router = useRouter()
  const autosaveButtonRef = useRef(null)
  const form = useForm<FormDataType>({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
    resolver: zodResolver(FormSchema),
  })
  const [requestVerifyToken, setRequestVerifyToken] = useState(false)
  const [verifiedToken, setVerifiedToken] = useState(false)
  const [requestResetPassword, setRequestResetPassword] = useState(false)

  const token = params.get('token')

  useEffect(() => {
    if (token) {
      setRequestVerifyToken(true)
      resetPasswordVerifyTokenApi(token as string)
        .then((res) => {
          if (mountedRef.current) {
            if (res.data.success) {
              setVerifiedToken(true)
            }
          }
        })
        .catch((error) => {
          const errorType = error?.response?.data?.error?.code

          if (errorType === ResetPasswordErrorCode.TOKEN_EXPIRED) {
            onTokenExpired()
          } else if (errorType === ResetPasswordErrorCode.INVALID_USER) {
            onWrongUser()
          } else if (errorType === ResetPasswordErrorCode.INVALID_TOKEN) {
            onInvalidToken()
          } else {
            toast.error(unexpectedError)
          }
        })
        .finally(() => {
          if (mountedRef.current) {
            setRequestVerifyToken(false)
          }
        })
    }
  }, [token, mountedRef, onTokenExpired, onWrongUser, onInvalidToken])

  const handleFormSubmit = async (data: FormDataType) => {
    setRequestResetPassword(true)
    resetPasswordApi(token as string, data.password)
      .then((res) => {
        if (mountedRef.current) {
          if (res.data.success) {
            autosaveButtonRef.current.click()
          } else {
            toast.error(failedToUpdatePasswordError)
          }
        }
      })
      .catch((error) => {
        toast.error(unexpectedError)
      })
      .finally(() => {
        if (mountedRef.current) {
          setRequestResetPassword(false)
        }
      })
  }

  const redirectToAlternativePage = () => {
    router.push('/change-password/confirmation')
  }

  return (
    <Form {...form}>
      <form className={className} onSubmit={form.handleSubmit(handleFormSubmit)}>
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="password"
            render={({ field, fieldState: { error } }) => (
              <FormItem>
                <FormControl>
                  <PasswordInput
                    className="w-full"
                    placeholder="New password"
                    autoComplete="new-password"
                    error={Boolean(error)}
                    variant={ControlVariants.default}
                    disabled={requestResetPassword || !verifiedToken || requestVerifyToken}
                    {...field}
                  />
                </FormControl>
                {error && <FormMessage>{error.message}</FormMessage>}
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field, fieldState: { error } }) => (
              <FormItem>
                <FormControl>
                  <PasswordInput
                    className="w-full"
                    placeholder="Confirm new password"
                    autoComplete="new-password"
                    error={Boolean(error)}
                    disabled={requestResetPassword || !verifiedToken || requestVerifyToken}
                    {...field}
                  />
                </FormControl>
                {error && <FormMessage>{error.message}</FormMessage>}
              </FormItem>
            )}
          />
          <Button
            type="submit"
            className="w-full"
            loading={requestResetPassword}
            disabled={!form.formState.isValid || requestResetPassword || !verifiedToken || requestVerifyToken}
          >
            Save
          </Button>
        </div>
      </form>

      <form
        action="/change-password"
        onSubmit={(event) => {
          event.preventDefault()
          onUpdatedPassword(true)
          redirectToAlternativePage()
        }}
      >
        <Input
          className="hidden"
          autoComplete="email"
          name="username"
          type="email"
          placeholder="Email"
          readOnly
          value={params.get('email') || ''}
        />
        <Input
          className="hidden"
          name="password"
          type="password"
          placeholder="Password"
          readOnly
          autoComplete="new-password"
          value={form.getValues().password}
        />
        <Button ref={autosaveButtonRef} type="submit" className="hidden">
          Submit
        </Button>
      </form>
    </Form>
  )
}
