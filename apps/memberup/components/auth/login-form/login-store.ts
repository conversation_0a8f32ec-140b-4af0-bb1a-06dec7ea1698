import { useRouter } from 'next/navigation'
import { create } from 'zustand'

export enum LoginState {
  IDLE = 'idle',
  AUTHENTICATING = 'authenticating',
  NAVIGATING = 'navigating',
  SUCCESS = 'success',
  ERROR = 'error',
}

interface LoginStore {
  loginState: LoginState
  setLoginState: (state: LoginState) => void
  isNavigating: boolean
  setIsNavigating: (isNavigating: boolean) => void
  reset: () => void
}

const useLoginStore = create<LoginStore>((set) => ({
  loginState: LoginState.IDLE,
  setLoginState: (state) => set({ loginState: state }),
  isNavigating: false,
  setIsNavigating: (isNavigating) => set({ isNavigating }),
  reset: () => set({ loginState: LoginState.IDLE, isNavigating: false }),
}))

export const useLoginState = () => {
  const router = useRouter()
  const { loginState, setLoginState, isNavigating, setIsNavigating } = useLoginStore()

  const navigateAndWait = async (url: string) => {
    setIsNavigating(true)
    setLoginState(LoginState.NAVIGATING)

    try {
      router.push(url)
    } finally {
      setLoginState(LoginState.SUCCESS)
      setIsNavigating(false)
    }
  }

  const isFormDisabled = loginState !== 'idle' && loginState !== 'error'

  return {
    loginState,
    setLoginState,
    isNavigating,
    isFormDisabled,
    navigateAndWait,
    reset: useLoginStore.getState().reset,
  }
}
