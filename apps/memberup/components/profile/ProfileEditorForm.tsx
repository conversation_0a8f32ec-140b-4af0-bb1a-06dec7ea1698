'use client'

import { memo } from 'react'
import { UseFormReturn } from 'react-hook-form'

import { CoverPictureInput } from './cover-picture-input'
import { ProfilePictureInput } from './profile-picture-input'
import { PERSONALITY_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { IUser } from '@memberup/shared/src/types/interfaces'
import { Button, Input, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui'
import { Form, FormControl, FormCounter, FormDescription, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Separator } from '@/components/ui/separator'
import useMounted from '@/hooks/useMounted'
import { maxBioLength, mbtiTypes, profileSocialLinks, type TMbtiType, type TProfileSocialLink } from '@/lib/constants'
import { getFullName } from '@/lib/formatting'
import { cn } from '@/lib/utils'
import { <PERSON>ropArea } from '@/shared-types/types'

interface ProfileEditorFormComponentProps {
  editingName: boolean
  editingUsername: boolean
  setEditingName: (value: boolean) => void
  setEditingUsername: (value: boolean) => void
  form: UseFormReturn<{
    first_name: string
    last_name: string
    username: string
    bio?: string
    location?: string
    personality_type?: string
    social?: {
      [key: string]: string
    }
  }>
  onCoverPictureChange: (image: string, cropArea: CropArea, file: File) => void
  onChangeProfilePicture: (image: string, cropArea: CropArea, file: File) => void
  onRemoveCoverPicture: () => void
  saving: boolean
  updatedUser: IUser
  user: IUser
}

function ProfileEditorFormComponent({
  editingName,
  editingUsername,
  setEditingName,
  setEditingUsername,
  form,
  onCoverPictureChange,
  onChangeProfilePicture,
  onRemoveCoverPicture,
  saving,
  updatedUser,
  user,
}: ProfileEditorFormComponentProps) {
  const mounted = useMounted()

  const cancelEditingName = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()
    if (saving) return

    setEditingName(false)
    form.setValue('first_name', user.first_name)
    form.setValue('last_name', user.last_name)
  }

  const cancelEditingUsername = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()
    if (saving) return

    setEditingUsername(false)
    form.setValue('username', user.username)
  }

  const renderNameFields = () => (
    <>
      <FormField
        control={form.control}
        name="first_name"
        render={({ field, fieldState: { error } }) => (
          <FormItem>
            <FormControl>
              <Input
                className="w-full"
                placeholder="First name"
                error={Boolean(error)}
                disabled={saving || !!user.name_updated_at || !editingName}
                {...field}
              />
            </FormControl>
            {error && <FormMessage>{error.message}</FormMessage>}
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="last_name"
        render={({ field, fieldState: { error } }) => (
          <FormItem>
            <FormControl>
              <Input
                className="w-full"
                placeholder="Last name"
                error={Boolean(error)}
                disabled={saving || !!user.name_updated_at || !editingName}
                {...field}
              />
            </FormControl>
            <FormDescription className="text-xs text-black-200 dark:text-black-100">
              {user.name_updated_at ? (
                <span>
                  You have already changed your name once. Please contact support if you need to update it again.
                </span>
              ) : (
                <>
                  You can change your name once and you must use your real name.&nbsp;
                  {!saving &&
                    (editingName ? (
                      <Button
                        className={cn('text-xs font-semibold', saving ? 'text-black-100' : 'text-red-200')}
                        variant="inline"
                        onClick={cancelEditingName}
                      >
                        Cancel
                      </Button>
                    ) : (
                      <Button
                        className="text-xs font-semibold text-primary-100 transition-colors hover:text-primary-200"
                        variant="inline"
                        onClick={(e) => {
                          e.preventDefault()
                          setEditingName(true)
                        }}
                      >
                        Change Name
                      </Button>
                    ))}
                </>
              )}
            </FormDescription>
            {error && <FormMessage>{error.message}</FormMessage>}
          </FormItem>
        )}
      />
    </>
  )

  return (
    <Form {...form}>
      <form className="space-y-6" autoComplete="off">
        <div className="xl:flex xl:space-x-4">
          <div className="xl:w-6/12">
            <ProfilePictureInput
              className="mb-6"
              onSelectPicture={onChangeProfilePicture}
              src={updatedUser.profile?.image}
              cropArea={updatedUser.profile?.image_crop_area}
              alt={getFullName(updatedUser)}
            />
            <div className="hidden space-y-6 xl:block">{renderNameFields()}</div>
          </div>
          <div className="mb-6 xl:mb-0 xl:w-6/12">
            <CoverPictureInput
              disabled={saving}
              onRemovePicture={onRemoveCoverPicture}
              onSelectPicture={onCoverPictureChange}
              alt={`${getFullName(updatedUser)} cover photo`}
            />
          </div>
        </div>
        <div className="space-y-6 xl:hidden">{renderNameFields()}</div>
        <FormField
          control={form.control}
          name="username"
          render={({ field, fieldState: { error } }) => (
            <FormItem>
              <FormControl>
                <Input
                  className="[&_input]:max-w-[45%]"
                  inputClassName="mx-px"
                  placeholder="URL"
                  error={Boolean(error)}
                  disabled={saving || !!user.username_updated_at || !editingUsername}
                  prepend={
                    <div className="mx-0 mr-px flex h-11 max-w-[55%] shrink overflow-hidden bg-transparent pl-4 text-sm leading-[2.75rem] text-grey-700 dark:text-grey-700">
                      <div className="overflow-hidden overflow-ellipsis whitespace-nowrap">
                        {mounted && window.location.origin}
                      </div>
                      <div>/@</div>
                    </div>
                  }
                  {...field}
                />
              </FormControl>
              <FormDescription className="text-xs text-black-200 dark:text-black-100">
                {user.username_updated_at ? (
                  <span>
                    You have already changed your username once. Please contact support if you need to update it again.
                  </span>
                ) : (
                  <>
                    You can only change your URL once, so be careful.&nbsp;
                    {!saving &&
                      (editingUsername ? (
                        <Button
                          className={cn('text-xs font-semibold', saving ? 'text-black-100' : 'text-red-200')}
                          variant="inline"
                          onClick={cancelEditingUsername}
                        >
                          Cancel
                        </Button>
                      ) : (
                        <Button
                          className="text-xs font-semibold text-primary-100 transition-colors hover:text-primary-200"
                          variant="inline"
                          onClick={(e) => {
                            e.preventDefault()
                            setEditingUsername(true)
                          }}
                        >
                          Change URL
                        </Button>
                      ))}
                  </>
                )}
              </FormDescription>
              {error && <FormMessage>{error.message}</FormMessage>}
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="bio"
          render={({ field, fieldState: { error } }) => (
            <FormItem>
              <FormControl>
                <Input
                  className="w-full"
                  disabled={saving}
                  placeholder="Bio"
                  error={Boolean(error)}
                  maxLength={maxBioLength}
                  {...field}
                />
              </FormControl>
              <FormCounter>
                {form.getValues('bio').length}/{maxBioLength}
              </FormCounter>
            </FormItem>
          )}
        />
        <div className="lg:flex lg:space-x-4">
          <div className="lg:w-6/12">
            <FormField
              control={form.control}
              name="location"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      className="w-full"
                      placeholder="Location"
                      error={Boolean(error)}
                      disabled={saving}
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <div className="mt-6 lg:mt-0 lg:w-6/12">
            <FormField
              control={form.control}
              name="personality_type"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <Select
                      className="w-full"
                      placeholder="Myers Briggs"
                      error={Boolean(error)}
                      empty={field.value === PERSONALITY_TYPE_ENUM.DS}
                      value={field.value}
                      name={field.name}
                      onValueChange={field.onChange}
                      disabled={saving}
                    >
                      <SelectTrigger className="w-full" disabled={saving}>
                        <SelectValue onBlur={field.onBlur} ref={field.ref} />
                      </SelectTrigger>
                      <SelectContent>
                        {mbtiTypes.map((type: TMbtiType) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>
        <Separator />
        <h2 className="font-semibold">Social links</h2>
        <div className="space-y-6">
          {profileSocialLinks.map((socialField: TProfileSocialLink) => (
            <FormField
              key={socialField.value}
              control={form.control}
              name={`social.${socialField.value}`}
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      className="w-full"
                      disabled={saving}
                      placeholder={socialField.label}
                      error={Boolean(error)}
                      maxLength={socialField.maxLength}
                      {...field}
                    />
                  </FormControl>
                  {error && <FormMessage>{error.message}</FormMessage>}
                </FormItem>
              )}
            />
          ))}
        </div>
      </form>
    </Form>
  )
}

const ProfileEditorForm = memo(ProfileEditorFormComponent)

export { ProfileEditorForm }
