import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { Button, Input, Switch } from '@/components/ui'
import { Form, FormControl, FormCounter, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { updateMembershipApi } from '@/shared-services/apis/membership.api'
import {
  selectMembership,
  selectMembershipSetting,
  updateMembershipSettingSuccess,
} from '@/src/store/features/membershipSlice'
import { useAppDispatch, useAppSelector } from '@/src/store/hooks'

const LINK_LABEL_MAX_LENGTH = 30

export default function AddLink({ link, onCancel, onSuccess }) {
  const [requestingAddOrUpdateLink, setRequestingAddOrUpdateLink] = useState(false)
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = membership?.membership_setting
  const links = membershipSetting?.external_links
  const setMembership = useStore((state) => state.community.setMembership)
  const linkSchema = z.object({
    label: z.string().max(LINK_LABEL_MAX_LENGTH).min(1),
    url: z.string().url('Invalid URL'),
    is_private: z.boolean(),
  })
  const dispatch = useAppDispatch()

  type LinkSchemaType = z.infer<typeof linkSchema>

  const linkForm = useForm<LinkSchemaType>({
    mode: 'onBlur',
    reValidateMode: 'onSubmit',
    defaultValues: {
      label: link?.label || '',
      url: link?.url || '',
      is_private: link?.is_private || false,
    },
    resolver: zodResolver(linkSchema),
  })

  const onSubmit = async (formData: LinkSchemaType) => {
    setRequestingAddOrUpdateLink(true)
    try {
      let updatedExternalLinks
      if (link?.label) {
        updatedExternalLinks = links.map((l) =>
          l.label === link?.label
            ? {
                label: formData.label,
                url: formData.url,
              }
            : l,
        )
      } else {
        updatedExternalLinks = [...links, formData]
      }

      const data = { external_links: updatedExternalLinks }
      const result = await updateMembershipApi(data, membership.id)

      setMembership({
        ...membership,
        membership_setting: result.data.data.membership_setting,
      })

      dispatch(
        updateMembershipSettingSuccess({
          data: result.data.data.membership_setting,
          partialChanged: true,
        }),
      )

      if (result.data.success) {
        toast.success('Community links updated')
      }

      onSuccess?.()
    } catch (e) {
      toast.error(e.message)
    } finally {
      setRequestingAddOrUpdateLink(false)
    }
  }

  const handleCancel = () => {
    onCancel?.()
  }

  return (
    <div>
      <h2 className="mb-8 text-lg font-semibold text-white-500">{link ? 'Edit Link' : 'Add Link'}</h2>
      <Form {...linkForm}>
        <div className={'flex w-full flex-col space-y-6'}>
          <FormField
            control={linkForm.control}
            name="label"
            render={({ field, fieldState: { error } }) => (
              <FormItem>
                <FormControl>
                  <Input
                    className="w-full"
                    autoComplete="label"
                    maxLength={LINK_LABEL_MAX_LENGTH}
                    disabled={requestingAddOrUpdateLink}
                    type="text"
                    placeholder="Label"
                    error={Boolean(error)}
                    {...field}
                  />
                </FormControl>
                {error && <FormMessage>{error.message}</FormMessage>}
                <FormCounter>
                  {linkForm.getValues('label').length}/{LINK_LABEL_MAX_LENGTH}
                </FormCounter>
              </FormItem>
            )}
          />
          <FormField
            control={linkForm.control}
            name="url"
            render={({ field, fieldState: { error } }) => (
              <FormItem>
                <FormControl>
                  <Input
                    className="w-full"
                    autoComplete="url"
                    maxLength={LINK_LABEL_MAX_LENGTH}
                    disabled={requestingAddOrUpdateLink}
                    type="text"
                    placeholder="URL"
                    error={Boolean(error)}
                    {...field}
                  />
                </FormControl>
                {error && <FormMessage>{error.message}</FormMessage>}
              </FormItem>
            )}
          />

          <div className={'flex flex-row justify-end gap-2'}>
            <Button
              type="button"
              variant="outline"
              disabled={requestingAddOrUpdateLink}
              onClick={() => handleCancel()}
              data-cy="cancel-button"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="default"
              loading={requestingAddOrUpdateLink}
              disabled={!linkForm.formState.isValid || !linkForm.formState.isDirty || requestingAddOrUpdateLink}
              onClick={linkForm.handleSubmit(onSubmit)}
              data-cy="save-button"
            >
              Save
            </Button>
          </div>
        </div>
      </Form>
    </div>
  )
}
