import React, { useState } from 'react'

import { MembershipQuestionsSettings } from '@/components/settings/admin-settings/powerups/MembershipQuestionsSettings'
import PowerUpsListing from '@/components/settings/admin-settings/powerups/PowerupsListing'
import { SparkSettings } from '@/components/settings/admin-settings/powerups/SparkSettings'

export function PowerUps() {
  const [currentView, setCurrentView] = useState('listing')
  const handleEditMembershipQuestions = () => {
    setCurrentView('membership-questions')
  }
  const handleEditSparkQuestions = () => {
    setCurrentView('spark')
  }
  const handleBackToPowerUps = () => {
    setCurrentView('listing')
  }

  return (
    <div>
      {currentView === 'listing' && (
        <PowerUpsListing
          onEditMembershipQuestionsClick={handleEditMembershipQuestions}
          onEditSparkQuestionsClick={handleEditSparkQuestions}
        />
      )}
      {currentView === 'spark' && <SparkSettings onBackToPowerUpsClick={handleBackToPowerUps} />}
      {currentView === 'membership-questions' && (
        <MembershipQuestionsSettings onBackToPowerUpsClick={handleBackToPowerUps} />
      )}
    </div>
  )
}
