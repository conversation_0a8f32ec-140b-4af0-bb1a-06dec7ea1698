import React, { useEffect, useState } from 'react'

import { Spark20Icon } from '@/components/icons/20px/spark-20-icon'
import { QuestionMark24Icon } from '@/components/icons/24px/question-mark-24-icon'
import { Button } from '@/components/ui'
import { useStore } from '@/hooks/useStore'
import { selectMembership, selectMembershipSetting } from '@/src/store/features/membershipSlice'
import { useAppSelector } from '@/src/store/hooks'

export default function PowerUpsListing({ onEditSparkQuestionsClick, onEditMembershipQuestionsClick }) {
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = membership?.membership_setting
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!membership || !membershipSetting) {
      return
    }
    const loadSpark = async () => {
      setIsLoading(false)
    }
    loadSpark()
    setIsLoading(false)
  }, [membership, membershipSetting])

  const isMembershipQuestionsEnabled = membershipSetting?.form_enabled
  const isSparkEnabled = membershipSetting?.spark_enabled

  return (
    <div className="space-y-3">
      <h2 className="text-lg font-semibold text-white-500">Power-Ups</h2>
      <div className="text-sm font-normal leading-snug text-white-200">Supercharge your community with power-ups.</div>
      {isLoading && <div>Loading...</div>}
      {!isLoading && (
        <div>
          <div className="flex flex-row gap-3 rounded-base bg-black-300 p-5">
            <div className="flex h-12 w-12 items-center justify-center rounded-base bg-gradient-to-r from-community-secondary to-community-primary">
              <QuestionMark24Icon />
            </div>
            <div className="text-white grow text-sm">
              <div className="mb-2 font-semibold">
                Membership questions
                {!isMembershipQuestionsEnabled && (
                  <span className="text-sm font-semibold text-black-100">&nbsp; (off)</span>
                )}
                {isMembershipQuestionsEnabled && (
                  <span className="text-sm font-semibold text-green-100">&nbsp; (on)</span>
                )}
              </div>
              <div className="text-sm text-black-200 dark:text-black-100">
                Ask members questions when they join your community.
              </div>
            </div>
            <div className="flex items-center">
              <Button
                className="w-full min-w-[4.75rem] sm:w-auto"
                variant="outline"
                onClick={onEditMembershipQuestionsClick}
                size="sm"
              >
                Edit
              </Button>
            </div>
          </div>
          <div className="flex flex-row gap-3 rounded-base bg-black-300 p-5">
            <div className="flex h-12 w-12 items-center justify-center rounded-base bg-black-700">
              <Spark20Icon className="h-9 w-9" />
            </div>
            <div className="text-white grow text-sm">
              <div className="mb-2 font-semibold">
                Spark engagement
                {!isSparkEnabled && <span className="text-sm font-semibold text-black-100">&nbsp; (off)</span>}
                {isSparkEnabled && <span className="text-sm font-semibold text-green-100">&nbsp; (on)</span>}
              </div>
              <div className="text-sm text-black-200 dark:text-black-100">
                Automatically post questions that spark conversation.
              </div>
            </div>
            <div className="flex items-center">
              <Button
                className="w-full min-w-[4.75rem] sm:w-auto"
                variant="outline"
                onClick={onEditSparkQuestionsClick}
                size="sm"
              >
                Edit
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
