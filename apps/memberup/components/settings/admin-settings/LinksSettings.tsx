import React, { useState } from 'react'

import AddLink from '@/components/settings/admin-settings/links/AddLink'
import ListLinks from '@/components/settings/admin-settings/links/ListLinks'

export function LinksSettings() {
  const [selectedLink, setSelectedLink] = useState(null)

  const [view, setView] = useState('list')

  const handleAddLink = () => {
    setView('update')
  }

  const handleAddLinkCancel = () => {
    setSelectedLink(null)
    setView('list')
  }

  const handleAddLinkSuccess = () => {
    setSelectedLink(null)
    setView('list')
  }

  const handleEditLink = (link) => {
    setSelectedLink(link)
    setView('update')
  }

  return (
    <>
      {view === 'list' && <ListLinks onAddLink={handleAddLink} onEditLink={handleEditLink} />}
      {view === 'update' && (
        <AddLink onCancel={handleAddLinkCancel} link={selectedLink} onSuccess={handleAddLinkSuccess} />
      )}
    </>
  )
}
