import { DragD<PERSON><PERSON>ontext, Draggable, Droppable } from '@hello-pangea/dnd'
import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import clsx from 'clsx'
import _orderBy from 'lodash/orderBy'
import React, { useEffect, useRef, useState } from 'react'

import { reorder } from '@memberup/shared/src/libs/common'
import { SPACE_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { IChannel } from '@memberup/shared/src/types/interfaces'
import { Drag24Icon } from '@/components/icons/24px/Drag24Icon'
import SpaceSettingsEditSpace from '@/components/settings/admin-settings/spaces/SpaceSettingsEditSpace'
import { Badge, Button } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { bulkUpdateChannelSequencesApi } from '@/shared-services/apis/channel.api'
import { selectMembership } from '@/src/store/features/membershipSlice'
import { deleteChannel, getChannelsSuccess, selectChannels, upsertChannel } from '@/src/store/features/spaceSlice'

export function SpacesSettings() {
  const dispatch = useAppDispatch()
  const membership = useStore((state) => state.community.membership)
  const [mode, setMode] = useState('list')
  const [selectedSpace, setSelectedSpace] = useState(null)
  const [sortedSpaces, setSortedSpaces] = useState([])
  const [openWarningDelete, setOpenWarningDelete] = useState(false)
  const [openArchiveWarning, setOpenArchiveWarning] = useState(false)
  const shouldSaveRef = useRef(false)

  const spaces = membership?.channels

  const handleDelete = () => {
    setOpenWarningDelete(false)
    dispatch(deleteChannel(selectedSpace.id))
    setSelectedSpace(null)
  }

  const handleArchiveSpace = (space: IChannel) => {
    const updatedSpace = { ...space, visibility: !space.visibility }
    dispatch(upsertChannel(updatedSpace))
  }

  useEffect(() => {
    if (!spaces) {
      return
    }
    setSortedSpaces(spaces)
    shouldSaveRef.current = false
  }, [spaces])

  // const onDragEnd = (result) => {
  //     // dropped outside the list
  //     if (!result.destination) {
  //         return
  //     }
  //     const temp = reorder(sortedSpaces, result.source.index, result.destination.index)
  //     const newSpaces = temp.map((item: IChannel, index) => ({...item, sequence: index + 1}))
  //
  //     setSortedSpaces(newSpaces)
  //
  //     // Update sequences in the database
  //     const updates = newSpaces
  //         .filter((item) => {
  //             const temp = (spaces || []).find((lc) => lc.id === item.id)
  //             return temp && temp?.sequence !== item.sequence
  //         })
  //         .map((d) => ({where: {id: d.id}, data: {sequence: d.sequence}}))
  //
  //     bulkUpdateChannelSequencesApi(
  //         {
  //             updates,
  //         },
  //         membership.id
  //     )
  //         .then((res) => {
  //             if (res?.data?.success) {
  //                 toast.success('Space moved')
  //                 const temp = spaces?.map((item) => {
  //                     const temp1 = res.data.data.find((lc) => lc.id === item.id)
  //                     if (temp1) {
  //                         return temp1
  //                     }
  //                     return item
  //                 })
  //                 dispatch(
  //                     getChannelsSuccess({
  //                         data: {
  //                             docs: _orderBy(temp, ['sequence'], ['asc']),
  //                             total: temp.length,
  //                         },
  //                         init: true,
  //                     })
  //                 )
  //             } else {
  //                 toast.error('Failed to update sequence')
  //             }
  //         })
  //         .catch((err) => {
  //         })
  // }

  const handleSpaceEditComplete = () => {
    setSelectedSpace(null)
    setMode('list')
    toast.success('Space updated')
  }
  const handleEditSpace = (space) => {
    setSelectedSpace(space)
    setMode('edit')
  }

  const handleConfirmDelete = (space) => {
    setSelectedSpace(space)
    setOpenWarningDelete(true)
  }

  const handleConfirmArchive = (space) => {
    setSelectedSpace(space)
    setOpenArchiveWarning(true)
  }

  if (mode === 'list') {
    return (
      <div className={'space-y-4'}>
        <ConfirmModal
          title="Are you sure you want to delete the space?"
          onConfirm={handleDelete}
          open={openWarningDelete}
          onCancel={() => setOpenWarningDelete(false)}
        />

        <ConfirmModal
          title="Are you sure you want to delete the space?"
          onConfirm={() => {
            setOpenArchiveWarning(false)
            handleArchiveSpace(selectedSpace)
          }}
          open={openArchiveWarning}
          onCancel={() => setOpenArchiveWarning(false)}
        />
        <div className={'flex'}>
          <div className={'grow'}>
            <h2 className="text-lg font-semibold text-white-500">Spaces</h2>
            <div className="text-sm font-normal leading-snug text-black-100">
              View and manage your community spaces.
            </div>
          </div>
          <Button
            type="submit"
            variant="default"
            loading={false}
            disabled={false}
            onClick={() => {
              setMode('edit')
            }}
            data-cy="add-space-button"
          >
            Add Space
          </Button>
        </div>

        {/* Listing */}

        {sortedSpaces.map((item, index) => (
          <div
            key={item.id}
            className={clsx(
              'hover:bg-white mb-4 rounded rounded-lg bg-black-300 p-5',
              !item.visibility && 'bg-black-300/50',
            )}
            data-cy="space-card"
          >
            <div className={'flex items-center gap-2'}>
              {false && <Drag24Icon />}
              <div className={'grow'}>
                <div className="text-white text-sm font-semibold leading-snug">{item.name}</div>
                <div className={'text-sm text-black-200'}>{item.description}</div>
              </div>

              <div className={'flex items-center gap-4'}>
                {item.is_private && <Badge>Admin only</Badge>}
                <DropdownMenu>
                  <DropdownMenuTrigger>
                    <MoreHorizIcon />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56">
                    {false && (
                      <DropdownMenuItem className="cursor-pointer" onClick={() => handleConfirmArchive(item)}>
                        {item.visibility ? 'Archive Space' : 'Unarchive Space'}
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem className="text-red cursor-pointer" onClick={() => handleEditSpace(item)}>
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem className="cursor-pointer text-red-500" onClick={() => handleConfirmDelete(item)}>
                      Delete Space
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <SpaceSettingsEditSpace
      space={selectedSpace}
      onCancel={handleSpaceEditComplete}
      onSuccess={handleSpaceEditComplete}
    />
  )
}
