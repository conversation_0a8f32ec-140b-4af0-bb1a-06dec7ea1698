import React from 'react'

import { CommunityGradientStops } from '@/components/layout/community-gradient-stops'

type Lock24IconProps = React.SVGProps<SVGSVGElement> & {
  gradient?: boolean
}

export function Lock24Icon({ gradient, ...props }: Lock24IconProps) {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="m 12.00001,2.399995 c 2.5706,0 4.6693,2.02076 4.7941,4.56043 l 0.0059,0.23957 0.0011,2.60521 c 1.3976,0.4945 2.3989,1.8277 2.3989,3.3948 v 4.8 c 0,1.9882 -1.6118,3.6 -3.6,3.6 H 8.39999 c -1.98822,0 -3.6,-1.6118 -3.6,-3.6 v -4.8 c 0,-1.5675 1.00182,-2.901 2.40011,-3.3952 l -1.1e-4,-2.60481 c 0,-2.65097 2.14903,-4.8 4.80002,-4.8 z m 3.6,9.60001 H 8.39999 c -0.66274,0 -1.2,0.5372 -1.2,1.2 v 4.8 c 0,0.6627 0.53726,1.2 1.2,1.2 h 7.20002 c 0.6627,0 1.2,-0.5373 1.2,-1.2 v -4.8 c 0,-0.6628 -0.5373,-1.2 -1.2,-1.2 z m -3.4209,-7.19343 -0.1791,-0.00658 c -1.2653,0 -2.30182,0.97905 -2.39344,2.22089 l -0.00658,0.17911 v 2.40001 h 4.80002 v -2.40001 c 0,-1.26523 -0.9791,-2.3018 -2.2209,-2.39342 l -0.1791,-0.00658 z"
        fill={gradient ? 'url(#community-gradient)' : 'currentColor'}
      />
      {gradient && (
        <defs>
          <linearGradient
            id="community-gradient"
            x1="4.79999"
            y1="12"
            x2="19.20001"
            y2="12"
            gradientUnits="userSpaceOnUse"
          >
            <CommunityGradientStops />
          </linearGradient>
        </defs>
      )}
    </svg>
  )
}
