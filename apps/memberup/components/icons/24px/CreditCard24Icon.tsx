import React from 'react'

import { CommunityGradientStops } from '@/components/layout/community-gradient-stops'

type CreditCard24IconProps = React.SVGProps<SVGSVGElement> & {
  gradient?: boolean
}

export function CreditCard24Icon({ gradient, ...props }: CreditCard24IconProps) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      className="chevron-left"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="m 18.000005,4.8 c 1.9882,0 3.6,1.61178 3.6,3.6 v 7.2 c 0,1.9882 -1.6118,3.6 -3.6,3.6 H 5.999995 c -1.98822,0 -3.6,-1.6118 -3.6,-3.6 V 8.4 c 0,-1.98822 1.61178,-3.6 3.6,-3.6 z M 4.799995,12 v 3.6 c 0,0.6627 0.53726,1.2 1.2,1.2 h 12.00001 c 0.6627,0 1.2,-0.5373 1.2,-1.2 V 12 Z m 0,-2.4 h 14.40001 V 8.4 c 0,-0.66274 -0.5373,-1.2 -1.2,-1.2 H 5.999995 c -0.66274,0 -1.2,0.53726 -1.2,1.2 z"
        fill={gradient ? 'url(#community-gradient)' : 'currentColor'}
      />
      {gradient && (
        <linearGradient
          id="community-gradient"
          x1="2.399995"
          y1="12"
          x2="21.600005"
          y2="12"
          gradientUnits="userSpaceOnUse"
        >
          <CommunityGradientStops />
        </linearGradient>
      )}
    </svg>
  )
}
