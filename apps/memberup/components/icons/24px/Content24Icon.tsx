import React from 'react'

import { CommunityGradientStops } from '@/components/layout/community-gradient-stops'

type Content24IconProps = React.SVGProps<SVGSVGElement> & {
  gradient?: boolean
}

export function Content24Icon({ gradient, ...props }: Content24IconProps) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      className="chevron-left"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="m 21.59999,17.99998 c 0,1.9883 -1.6118,3.6 -3.6,3.6 h -1.2 -2.4 -4.79998 -2.4 -1.2 c -1.98823,0 -3.6,-1.6117 -3.6,-3.6 v -1.2 -2.4 -4.79996 -2.4 -1.2 c 0,-1.98823 1.61177,-3.6 3.6,-3.6 h 1.2 2.4 4.79998 2.4 1.2 c 1.9882,0 3.6,1.61177 3.6,3.6 v 1.2 2.4 4.79996 2.4 z m -16.79998,-1.2 v 1.2 c 0,0.6154 0.46325,1.1226 1.06005,1.192 l 0.13995,0.008 h 1.2 v -2.4 z m 4.8,0 v 2.4 h 4.79998 v -2.4 z m 7.19998,0 v 2.4 h 1.2 c 0.6154,0 1.1226,-0.4632 1.1919,-1.06 l 0.0081,-0.14 v -1.2 z m 2.4,-2.4 V 9.60002 h -6 v 4.79996 z m -8.4,0 H 4.80001 V 9.60002 h 5.99998 z M 9.60001,4.80002 h 4.79998 v 2.4 H 9.60001 Z m 9.59998,1.2 v 1.2 h -2.4 v -2.4 h 1.2 l 0.14,0.00807 c 0.5968,0.06932 1.06,0.57652 1.06,1.19193 z m -13.19998,-1.2 h 1.2 v 2.4 h -2.4 v -1.2 L 4.80808,5.86007 C 4.8774,5.26327 5.38461,4.80002 6.00001,4.80002 Z"
        fill={gradient ? 'url(#community-gradient)' : 'currentColor'}
      />
      {gradient && (
        <defs>
          <linearGradient
            id="community-gradient"
            x1="2.40001"
            y1="12"
            x2="21.59999"
            y2="12"
            gradientUnits="userSpaceOnUse"
          >
            <CommunityGradientStops />
          </linearGradient>
        </defs>
      )}
    </svg>
  )
}
