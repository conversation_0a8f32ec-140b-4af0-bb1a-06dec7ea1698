import {
  Facebook20Icon,
  Globe20Icon,
  Instagram20Icon,
  LinkedIn20Icon,
  TikTok20Icon,
  X20Icon,
  YouTube20Icon,
} from '@/components/icons'
import { cn } from '@/lib/utils'

function SocialLinks({
  className,
  website,
  facebook,
  instagram,
  linkedin,
  youtube,
  tiktok,
  x,
}: {
  className?: string
  website?: string
  facebook?: string
  instagram?: string
  linkedin?: string
  youtube?: string
  tiktok?: string
  x?: string
}) {
  if (!website && !facebook && !instagram && !linkedin && !youtube && !tiktok && !x) {
    return null
  }

  return (
    <div className={cn('social-links', className)}>
      {website && (
        <a href={website} target="_blank" title="Website">
          <Globe20Icon />
        </a>
      )}
      {facebook && (
        <a href={`https://wwww.facebook.com/${facebook}`} target="_blank" title="Facebook">
          <Facebook20Icon />
        </a>
      )}
      {instagram && (
        <a href={`https://instagram.com/${instagram}`} target="_blank" title="Instagram">
          <Instagram20Icon />
        </a>
      )}
      {linkedin && (
        <a href={`https://www.linkedin.com/${linkedin}`} target="_blank" title="LinkedIn">
          <LinkedIn20Icon />
        </a>
      )}
      {youtube && (
        <a href={`https://youtube.com/${youtube}`} target="_blank" title="YouTube">
          <YouTube20Icon />
        </a>
      )}
      {tiktok && (
        <a href={`https://tiktok.com/${tiktok}`} target="_blank" title="TikTok">
          <TikTok20Icon />
        </a>
      )}
      {x && (
        <a href={`https://x.com/${x}`} target="_blank" title="X">
          <X20Icon />
        </a>
      )}
    </div>
  )
}

export { SocialLinks }
