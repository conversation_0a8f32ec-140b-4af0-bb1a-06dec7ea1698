import { utcToZonedTime } from 'date-fns-tz'
import React, { useEffect, useState } from 'react'

import { TActivityCalendarData } from './types'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { Button } from '@/components/ui'
import {
  AdaptiveTooltip,
  AdaptiveTooltipContent,
  AdaptiveTooltipProvider,
  AdaptiveTooltipTrigger,
} from '@/components/ui/adaptive-tooltip'
import { Modal, ModalContent, ModalDescription, ModalFooter, ModalHeader, ModalTitle } from '@/components/ui/modal'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'
import { getDayDateObject } from '@/shared-libs/date-utils'
import { IUser } from '@/shared-types/interfaces'

const ActivityCalendarDay = ({ activity, userSignupDate }) => {
  if (!activity) return ''

  const utcDate = utcToZonedTime(activity.day, 'UTC')

  const formatInUTC = () => {
    return formatDate({ date: utcDate, options: { timeZone: 'UTC' } })
  }

  const userExistedOnDay = utcDate > userSignupDate

  if (userExistedOnDay) {
    return (
      <AdaptiveTooltip>
        <AdaptiveTooltipTrigger asChild>
          <div>
            <div
              className={cn(
                'h-[11px] w-[11px] cursor-pointer rounded-full',
                !userExistedOnDay && 'bg-grey-200 dark:bg-grey-800',
              )}
              style={{
                backgroundColor: `hsl(var(--primary-200) / ${((activity.level + 1) / 5) * 100}%)`,
              }}
            />
          </div>
        </AdaptiveTooltipTrigger>
        <AdaptiveTooltipContent className="leading-1.5 text-xs">
          Activities:&nbsp;
          <span className="font-semibold text-black-200 dark:text-white-500">{activity.count}</span>
          &nbsp;on {formatInUTC()}
        </AdaptiveTooltipContent>
      </AdaptiveTooltip>
    )
  } else {
    return <div className="h-[11px] w-[11px] rounded-full bg-grey-200 dark:bg-grey-800" />
  }
}

function ActivityCalendar({ data: { data, monthsRow }, user }: { data: TActivityCalendarData; user: IUser }) {
  const scrollDivRef = React.createRef<HTMLDivElement>()
  const [modalOpen, setModalOpen] = useState(false)

  useEffect(() => {
    if (!scrollDivRef.current) return

    if (scrollDivRef.current.scrollWidth > scrollDivRef.current.clientWidth) {
      scrollDivRef.current.scrollLeft = scrollDivRef.current.scrollWidth
    }
  }, [])

  if (!data) return null

  const backgrounds = ['bg-primary-200/20', 'bg-primary-200/40', 'bg-primary-200/60', 'bg-primary-200/80', 'bg-primary']

  const labels = ['No activities', '1+', '3+', '6+', '10+']

  const userSignupDate = getDayDateObject(new Date(user.createdAt))

  return (
    <div className="activity-calendar flex flex-col">
      <div className="mb-1.5 flex items-end justify-start">
        <div className="relative mb-px flex h-[6.500rem] flex-col justify-between pb-3.5 pr-2 text-xs">
          {['Mon', 'Wed', 'Fri', 'Sun'].map((day) => (
            <div key={day} className="select-none text-right leading-3 text-black-200 dark:text-black-100">
              {day}
            </div>
          ))}
        </div>
        <AdaptiveTooltipProvider>
          <div className="relative h-[7.85rem] w-full">
            <ScrollArea className="absolute h-full w-full pb-3.5" ref={scrollDivRef}>
              <div className="flex w-full justify-end">
                <table>
                  <tbody>
                    <tr>
                      {monthsRow.map((month: any, index) => (
                        <td
                          colSpan={month.colspan}
                          className="select-none pb-2 pl-0 pr-0 pt-0 text-xs font-normal leading-3 text-black-200 dark:text-black-100"
                          key={index}
                        >
                          {index === monthsRow.length - 1 && month.colspan === 1 ? (
                            <div className="relative h-[0.8125rem] w-[0.8125rem] overflow-visible">
                              <span className="absolute right-0 top-0">{month.month}</span>
                            </div>
                          ) : (
                            month.month
                          )}
                        </td>
                      ))}
                    </tr>
                    {[0, 1, 2, 3, 4, 5, 6].map((columnNumber: number) => (
                      <tr key={columnNumber}>
                        {data[columnNumber].map((activity, index) => (
                          <td key={index}>
                            <ActivityCalendarDay activity={activity} userSignupDate={userSignupDate} />
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <ScrollBar orientation="horizontal" />
            </ScrollArea>
          </div>
        </AdaptiveTooltipProvider>
        <Modal open={modalOpen} onOpenChange={(value) => value !== modalOpen && setModalOpen(value)}>
          <ModalContent>
            <ModalHeader className="mb-0 pb-0">
              <ModalTitle>Daily Activity</ModalTitle>
              <ModalDescription asChild>
                <p className="mb-0 leading-relaxed">
                  This chart shows your activity on MemberUp over the past year. Activities that contribute to your
                  chart include signing in (once daily), liking posts, writing posts, commenting, and completing
                  lessons. Activities are timestamped in Coordinated Universal Time (UTC).
                </p>
              </ModalDescription>
            </ModalHeader>
            <ModalFooter>
              <Button className="w-full" variant="outline" onClick={() => setModalOpen(false)}>
                Close
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
      <div className="flex justify-between pb-2.5 text-xs">
        <Button
          className="text-xs font-normal text-black-200 hover:text-black-100 dark:text-white-200 dark:hover:text-white-500"
          variant="inline"
          onClick={() => setModalOpen(true)}
        >
          What is this?
        </Button>
        <AdaptiveTooltipProvider>
          <div className="leading-1.5 flex items-center justify-end">
            <div className="mr-2 select-none text-black-200 dark:text-white-200">Less</div>
            {[...Array(5)].map((_, index) => (
              <AdaptiveTooltip key={index}>
                <AdaptiveTooltipTrigger asChild>
                  <div className={`h-[11px] w-[11px] rounded-full ${backgrounds[index]} mr-0.5 cursor-pointer`}></div>
                </AdaptiveTooltipTrigger>
                <AdaptiveTooltipContent asChild>
                  <span className="text-font-light-ui-black dark:text-pure-white">{labels[index]}</span>
                </AdaptiveTooltipContent>
              </AdaptiveTooltip>
            ))}
            <div className="ml-1.5 select-none text-black-200 dark:text-white-200">More</div>
          </div>
        </AdaptiveTooltipProvider>
      </div>
    </div>
  )
}

export { ActivityCalendar }
