import { useRouter, useSearchParams } from 'next/navigation'
import React, { useEffect, useState } from 'react'

import { USER_MEMBERSHIP_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { AdminSettings } from '@/components/settings/admin-settings/AdminSettings'
import { MemberSettings } from '@/components/settings/member-settings'
import { Button } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import { inviteVerifyTokenApi } from '@/shared-services/apis/invite.api'
import { cancelMembershipRequestApi } from '@/shared-services/apis/membership.api'
import { extractPriceOrDefault, formatPrice } from '@/src/libs/utils'
import { AuthForms } from '@/store/authSlice'

export function JoinCommunityButton() {
  const { isCurrentUserAdmin } = useCheckUserRole()
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = membership?.membership_setting
  const setShowForm = useStore((state) => state.auth.setShowForm)
  const setJoinCommunityId = useStore((state) => state.auth.setJoinCommunityId)
  const setInviteToken = useStore((state) => state.auth.setInviteToken)

  const [openCancelMembershipRequestConfirm, setOpenCancelMembershipRequestConfirm] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [settingsModalOpen, setSettingsModalOpen] = useState(false)
  const user = useStore((state) => state.auth.user)
  const userMembership = useStore((state) => state.community.userMembership)
  const membershipStatus = useStore((state) => state.community.userMembership?.status)

  const updateUserMembership = useStore((state) => state.auth.updateUserMembership)
  const [buttonLabel, setButtonLabel] = useState('')

  const router = useRouter()
  const searchParams = useSearchParams()

  const inviteTokenParam = searchParams.get('invite_token')
  const inviteToken = inviteTokenParam === 'null' ? null : inviteTokenParam
  const promoCode = searchParams.get('promo_code')

  useEffect(() => {
    setIsLoading(true)

    if (!membership || !membershipSetting) {
      return
    }

    const removeQueryParam = (paramToRemove: string) => {
      const newSearchParams = new URLSearchParams(searchParams.toString())

      newSearchParams.delete(paramToRemove)

      const newPathname = newSearchParams.toString()
        ? `${window.location.pathname}?${newSearchParams.toString()}`
        : window.location.pathname

      router.push(newPathname)
    }

    const initialize = async () => {
      // NOTE: With invite token does not matter if the community is paid or not.
      if (inviteToken) {
        const verifyTokenResult = await inviteVerifyTokenApi(inviteToken, membership.id)

        if (verifyTokenResult.data.success && verifyTokenResult.data.data.valid) {
          setButtonLabel('Join community for free!')
          setIsLoading(false)
          return
        } else {
          toast.error('Invite token is not valid')
          removeQueryParam('invite_token')
        }
      }

      if (membershipSetting.is_pricing_enabled) {
        const defaultPrice = extractPriceOrDefault(membershipSetting.stripe_prices, promoCode)
        const formattedPrice = formatPrice(defaultPrice)
        setButtonLabel(`Join ${formattedPrice}`)
        setIsLoading(false)
        return
      }
      setButtonLabel(`Join community`)
      setIsLoading(false)
    }
    initialize()
  }, [membership, membershipSetting])

  const handleJoinCommunityClick = async () => {
    try {
      if (!user) {
        setJoinCommunityId(membership.id)
        setInviteToken(inviteToken)
        setShowForm(AuthForms.signup)
      } else {
        setJoinCommunityId(membership.id)
        setInviteToken(inviteToken)
        setShowForm(null)
      }
    } catch (e) {
      toast.error(e.message)
    } finally {
      setIsLoading(false)
    }
  }

  const openCommunitySettings = (e: React.MouseEvent) => {
    e.preventDefault()
    setSettingsModalOpen(true)
  }

  const handleOnCancelMembershipRequestClick = () => {
    setOpenCancelMembershipRequestConfirm(true)
  }

  const handleConfirmCancelMembershipRequest = async () => {
    try {
      setIsLoading(true)
      const res = await cancelMembershipRequestApi(membership.id)
      toast.success('Your membership request has been cancelled.')
      updateUserMembership(res.data.data)
      setOpenCancelMembershipRequestConfirm(false)
    } catch (e) {
      toast.error(`Error: ${e.message} `, 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const renderContent = () => {
    if (
      !user ||
      !userMembership ||
      (membershipStatus !== USER_MEMBERSHIP_STATUS_ENUM.accepted &&
        membershipStatus !== USER_MEMBERSHIP_STATUS_ENUM.pending &&
        membershipStatus !== USER_MEMBERSHIP_STATUS_ENUM.banned &&
        membershipStatus !== USER_MEMBERSHIP_STATUS_ENUM.rejected)
    ) {
      return (
        <Button
          className="mt-4 w-full"
          type="submit"
          variant="community-primary"
          loading={isLoading}
          disabled={isLoading}
          onClick={handleJoinCommunityClick}
        >
          {buttonLabel}
        </Button>
      )
    }

    if (membershipStatus === USER_MEMBERSHIP_STATUS_ENUM.accepted) {
      return (
        <Button className="w-full" type="submit" variant="outline" onClick={openCommunitySettings}>
          Settings
        </Button>
      )
    }

    if (membershipStatus === USER_MEMBERSHIP_STATUS_ENUM.banned) {
      return (
        <div className="text-white mt-4 rounded-[10px] bg-red-500 p-4 text-center">
          You are banned from this community.
        </div>
      )
    }

    if (membershipStatus === USER_MEMBERSHIP_STATUS_ENUM.rejected) {
      return <div className="mt-4 rounded-[10px] bg-slate-900 p-4">You were rejected from this community.</div>
    }

    if (membershipStatus === USER_MEMBERSHIP_STATUS_ENUM.pending) {
      return (
        <>
          <ConfirmModal
            onConfirm={handleConfirmCancelMembershipRequest}
            open={openCancelMembershipRequestConfirm}
            loading={isLoading}
            onCancel={() => {
              setOpenCancelMembershipRequestConfirm(false)
            }}
            title={'Cancel membership request'}
          >
            <>
              <div>{`Are you sure you want to cancel your membership request?`}</div>
            </>
          </ConfirmModal>
          <div className="mt-4 rounded-[10px] border border-gray-400 p-4 text-center font-['Graphik'] text-sm font-semibold text-gray-400">
            Membership Pending
          </div>
          <button
            className={'w-full bg-transparent p-4 text-violet-500'}
            onClick={handleOnCancelMembershipRequestClick}
          >
            Cancel Membership Request
          </button>
        </>
      )
    }
  }

  return (
    <>
      {renderContent()}
      {isCurrentUserAdmin ? (
        <AdminSettings open={settingsModalOpen} onOpenChange={(value: boolean) => setSettingsModalOpen(value)} />
      ) : (
        <MemberSettings open={settingsModalOpen} onOpenChange={(value: boolean) => setSettingsModalOpen(value)} />
      )}
    </>
  )
}
