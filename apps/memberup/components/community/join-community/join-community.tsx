import { JoinCommunityCheckoutForm } from '@/components/community/join-community/join-community-checkout-form'
import { JoinCommunityQuestions } from '@/components/community/join-community/join-community-questions'
import { useStore } from '@/hooks/useStore'

export function JoinCommunity() {
  const isPaid = useStore((state) => state.community.isPaid)
  const inviteToken = useStore((state) => state.auth.inviteToken)

  if (isPaid && !inviteToken) {
    return <JoinCommunityCheckoutForm />
  } else {
    return <JoinCommunityQuestions />
  }
}
