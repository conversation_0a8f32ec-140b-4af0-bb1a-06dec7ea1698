'use client'

import { useRouter } from 'next/navigation'

import { CommunityCreationFormContainer } from '@/components/community/community-creation-form'
import { ParticlesContainer } from '@/components/layout/particles-container'
import { PrimaryBlurredBackground } from '@/components/layout/primary-blurred-background'
import { useRouteProtection } from '@/hooks/useRouteProtection'
import { useStore } from '@/hooks/useStore'
import { getMembershipSuccess } from '@/memberup/store/features/membershipSlice'
import { getActiveUserSuccess, useAppDispatch } from '@/memberup/store/store'
import { getActiveUserApi } from '@/shared-services/apis/user.api'
import { IMembership, IMembershipSetting } from '@/shared-types/interfaces'

export function CreateCommunityClient() {
  useRouteProtection()
  const router = useRouter()
  const dispatch = useAppDispatch()
  const setAuthenticatedUserData = useStore((state) => state.auth.setAuthenticatedUserData)
  const setNewlyCreatedId = useStore((state) => state.community.setNewlyCreatedId)
  const setMembership = useStore((state) => state.community.setMembership)

  const onSuccess = async (newCommunity: IMembership) => {
    // Update User's user_memberships data
    const {
      data: { data },
    } = await getActiveUserApi()
    dispatch(getActiveUserSuccess(data))
    setAuthenticatedUserData(data)
    setNewlyCreatedId(newCommunity.id)
    setMembership(newCommunity)
    dispatch(
      getMembershipSuccess({
        membership: newCommunity as IMembership,
        membership_setting: newCommunity.membership_setting as IMembershipSetting,
        owner: null,
      }),
    )
    router.push(`/${newCommunity.slug}`)
  }

  return (
    <>
      <PrimaryBlurredBackground className="-top-20" />
      <ParticlesContainer className="relative z-20 mx-auto w-full rounded-base border border-grey-900 px-4 py-8 sm:px-8">
        <CommunityCreationFormContainer onSuccess={onSuccess} />
      </ParticlesContainer>
      <PrimaryBlurredBackground className="-bottom-[19rem]" />
    </>
  )
}
