import { CroppedImage } from '@/components/images/cropped-image'
import { cn } from '@/lib/utils'
import { TAppCropArea } from '@/shared-types/types'

export function Favicon({
  className,
  src,
  cropArea,
  communityName,
  width,
  height,
  variant = 'regular',
}: {
  className?: string
  src?: string
  cropArea?: TAppCropArea
  communityName: string
  width: number
  height: number
  variant?: 'regular' | 'inverted'
}) {
  return (
    <div
      className={cn(
        'favicon relative flex shrink-0 select-none items-center justify-center overflow-hidden rounded-xl text-base font-semibold',
        variant === 'regular'
          ? `text-black-200 dark:text-black-100 ${!src && 'bg-grey-200 dark:bg-black-500'}`
          : `text-black-300 dark:text-black-700 ${!src && 'bg-white-200 dark:bg-grey-300'}`,
        className,
      )}
    >
      {src ? (
        <CroppedImage
          cropArea={cropArea || { x: 0, y: 0, width: 100, height: 100 }}
          className="absolute"
          src={src}
          alt={communityName}
          width={width}
          height={height}
          priority
        />
      ) : (
        <div>{communityName.replace(/\s+/g, '').slice(0, 2).toUpperCase()}</div>
      )}
    </div>
  )
}
