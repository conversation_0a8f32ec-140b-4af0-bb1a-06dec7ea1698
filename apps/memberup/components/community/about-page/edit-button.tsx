import { Edit16Icon } from '@/components/icons/16px/edit-16-icon'

interface EditButtonProps {
  onClick: () => void
}

export function EditButton({ onClick }: EditButtonProps) {
  return (
    <button
      onClick={onClick}
      className="flex h-6 w-6 shrink-0 items-center justify-center rounded-[7.5px] border-[0.75px] border-black-100 hover:bg-grey-200 dark:bg-grey-900 dark:hover:bg-grey-800"
      aria-label="Edit community"
    >
      <Edit16Icon />
    </button>
  )
}
