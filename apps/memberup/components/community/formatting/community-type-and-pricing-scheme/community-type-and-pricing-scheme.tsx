import { VISIBILITY_ENUM } from '@memberup/shared/src/types/enum'
import { IMembership } from '@memberup/shared/src/types/interfaces'
import { isPaidCommunity } from '@/lib/communities'
import { formatThousands } from '@/lib/formatting'

export const CommunityTypeAndPricingScheme = ({ community }: { community: Partial<IMembership> }) => (
  <>
    {community.membership_setting.visibility === VISIBILITY_ENUM.public ? 'Public' : 'Private'}
    &nbsp;• {formatThousands(community.membership_setting.members_count)}
    &nbsp;• {isPaidCommunity(community.membership_setting) ? 'Paid' : 'Free'}
  </>
)
