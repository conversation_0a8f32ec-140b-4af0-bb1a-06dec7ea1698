import { NavigationMenu } from './NavigationMenu'
import { NavigationMenuItem } from './NavigationMenuItem'
import {
  BillList24Icon,
  CreditCard24Icon,
  Lock24Icon,
  Settings24Icon,
  Theme24Icon,
  User24Icon,
  UserAdd24Icon,
} from '@/components/icons'

export function AccountSettingsNavigationMenu() {
  return (
    <NavigationMenu>
      <NavigationMenuItem href="/settings/account/profile" icon={<User24Icon />}>
        Profile
      </NavigationMenuItem>
      <NavigationMenuItem href="/settings/account/account" icon={<Settings24Icon />}>
        Account
      </NavigationMenuItem>
      <NavigationMenuItem href="/settings/account/password" icon={<Lock24Icon />}>
        Password
      </NavigationMenuItem>
      <NavigationMenuItem href="/settings/account/referrals" icon={<UserAdd24Icon />}>
        Referrals
      </NavigationMenuItem>
      <NavigationMenuItem href="/settings/account/payment-methods" icon={<CreditCard24Icon />}>
        Payment Methods
      </NavigationMenuItem>
      <NavigationMenuItem href="/settings/account/payment-history" icon={<BillList24Icon />}>
        Payment History
      </NavigationMenuItem>
      <NavigationMenuItem href="/settings/account/theme" icon={<Theme24Icon />}>
        Theme
      </NavigationMenuItem>
    </NavigationMenu>
  )
}
