'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState } from 'react'

import { CarouselItem } from '@/components/ui/carousel'
import { useStore } from '@/hooks/useStore'
import { cn } from '@/lib/utils'

export function HorizontalNavigationMenuItem({
  children,
  href,
  onClick,
}: {
  children: React.ReactNode
  href?: string
  onClick?: () => void
}) {
  const pathname = usePathname()
  const active = pathname === href
  const linearGradient = useStore((state) => state.community.linearGradient)
  const linearGradient10 = useStore((state) => state.community.linearGradient10)
  const [isHovered, setIsHovered] = useState(false)

  const buttonBaseClassName = 'rounded-base text-black-700 dark:text-white-500 text-ssm font-semibold'
  const showGradientBorder = active || isHovered

  return (
    <CarouselItem className="basis-auto pl-1.5">
      <div
        className={cn(
          buttonBaseClassName,
          'box-border h-8 select-none rounded-base p-0.5 md:h-10',
          !showGradientBorder && 'bg-white-100 dark:bg-black-700',
        )}
        style={{
          background: showGradientBorder ? linearGradient : undefined,
        }}
      >
        <div className="h-7 rounded-lg bg-white-100 dark:bg-black-700 md:h-9">
          {onClick ? (
            <button
              className="flex h-full w-full items-center rounded-lg px-6"
              style={{
                background: showGradientBorder ? linearGradient10 : 'transparent',
              }}
              onClick={onClick}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              {children}
            </button>
          ) : (
            <Link
              className="flex h-full w-full items-center rounded-lg px-6"
              style={{
                background: showGradientBorder ? linearGradient10 : undefined,
              }}
              href={href}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              {children}
            </Link>
          )}
        </div>
      </div>
    </CarouselItem>
  )
}
