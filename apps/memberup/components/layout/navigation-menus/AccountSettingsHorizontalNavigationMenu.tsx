import { usePathname } from 'next/navigation'

import { HorizontalNavigationMenu } from './HorizontalNavigationMenu'
import { HorizontalNavigationMenuItem } from './HorizontalNavigationMenuItem'

export function AccountSettingsHorizontalNavigationMenu({ className }: { className?: string }) {
  const pathname = usePathname()
  const menuItems = [
    { label: 'Profile', href: '/settings/account/profile' },
    { label: 'Account', href: '/settings/account/account' },
    { label: 'Password', href: '/settings/account/password' },
    { label: 'Payment Methods', href: '/settings/account/payment-methods' },
    { label: 'Payment History', href: '/settings/account/payment-history' },
    { label: 'Theme', href: '/settings/account/theme' },
  ]
  const selectedIndex = menuItems.findIndex((item) => pathname === item.href)

  return (
    <HorizontalNavigationMenu className={className} selectedIndex={selectedIndex}>
      {menuItems.map((item) => (
        <HorizontalNavigationMenuItem key={item.href} href={item.href}>
          {item.label}
        </HorizontalNavigationMenuItem>
      ))}
    </HorizontalNavigationMenu>
  )
}
