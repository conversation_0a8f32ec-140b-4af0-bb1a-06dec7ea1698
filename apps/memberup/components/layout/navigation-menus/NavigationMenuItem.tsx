'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cloneElement, ReactElement, ReactNode } from 'react'

import { useStore } from '@/hooks/useStore'
import { cn } from '@/lib/utils'

interface NavigationMenuItemProps {
  children: ReactNode
  href: string
  icon?: ReactElement
  notificationIcon?: ReactElement
  disabled?: boolean
}

export function NavigationMenuItem({ children, href, icon, notificationIcon, disabled }: NavigationMenuItemProps) {
  const pathname = usePathname()
  const membership = useStore((state) => state.community.membership)

  const active =
    pathname === href ||
    (membership?.slug && href === `/${membership.slug}` && pathname.startsWith(`/${membership.slug}/post/`))

  const containerClasses = cn(
    'flex items-center w-full rounded-base h-12 text-base font-semibold px-6',
    disabled
      ? 'dark:text-black-100 text-black-200'
      : 'text-black-700 dark:text-white-500 hover:bg-grey-200 dark:hover:bg-black-500',
    active && !disabled && 'bg-grey-200 dark:bg-black-500',
  )

  return (
    <li className="mb-3 w-full last:mb-0">
      {disabled ? (
        <div className={containerClasses} aria-disabled="true">
          {icon && <span className={cn('mr-3', active && 'text-primary-400')}>{icon}</span>}
          {children}
        </div>
      ) : (
        <Link href={href} className={containerClasses}>
          <div className="flex flex-1 items-center">
            {icon && <span className="mr-3">{cloneElement(icon, { gradient: active })}</span>}
            {children}
          </div>
          {notificationIcon}
        </Link>
      )}
    </li>
  )
}
