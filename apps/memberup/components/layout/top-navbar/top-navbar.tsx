import { usePathname } from 'next/navigation'

import { CommunitySwitcher } from '@/components/layout'
import { AccountSettingsHorizontalNavigationMenu } from '@/components/layout/navigation-menus/AccountSettingsHorizontalNavigationMenu'
import { CommunityHorizontalNavigationMenu } from '@/components/layout/navigation-menus/CommunityHorizontalNavigationMenu'
import { LegalHorizontalNavigationMenu } from '@/components/layout/navigation-menus/LegalHorizontalNavigationMenu'
import { TopNavbarMenus } from '@/components/layout/top-navbar'
import { Button } from '@/components/ui'
import { useStore } from '@/hooks/useStore'
import { legalLinks } from '@/lib/constants'
import { AuthForms } from '@/store/authSlice'

const TopNavbarContents = () => {
  const pathname = usePathname()

  if (pathname.startsWith('/settings/account/')) {
    return <AccountSettingsHorizontalNavigationMenu />
  }

  const isLegalPage = legalLinks.some((link) => pathname.startsWith(link.href))
  if (isLegalPage) {
    return <LegalHorizontalNavigationMenu />
  }

  return <CommunityHorizontalNavigationMenu />
}

export function TopNavbar({ height }: { height?: number }) {
  const user = useStore((state) => state.auth.user)
  const setShowForm = useStore((state) => state.auth.setShowForm)

  const showLoginModal = () => {
    setShowForm(AuthForms.login)
  }

  const showSignupModal = () => {
    setShowForm(AuthForms.signup)
  }

  return (
    <div
      id="top-navbar"
      className="fixed left-0 top-0 z-30 w-full border-b border-b-grey-200 bg-white-500 dark:border-b-grey-900 dark:bg-black-500"
      style={
        height !== undefined
          ? {
              transform: `translateY(${-height}px)`,
            }
          : {}
      }
    >
      <div className="flex w-full justify-center">
        <div className="content-container mobile-padded-content-container padded-content-container w-full">
          <div className="flex h-[3rem] w-full justify-between md:h-[3.625rem]">
            <div className="flex h-full flex-1 items-center overflow-hidden">
              <CommunitySwitcher />
            </div>
            {user ? (
              <TopNavbarMenus />
            ) : (
              <>
                <div className="hidden items-center space-x-2 md:flex">
                  <Button size="sm" variant="outline" onClick={showLoginModal}>
                    Login
                  </Button>
                  <Button size="sm" variant="community-primary" onClick={showSignupModal}>
                    Sign Up
                  </Button>
                </div>
                <div className="flex items-center space-x-2 md:hidden">
                  <Button size="xs" variant="outline" onClick={showLoginModal}>
                    Login
                  </Button>
                  <Button size="xs" onClick={showSignupModal}>
                    Sign Up
                  </Button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      <div className="pt-1 md:pt-3 xl:hidden">
        <TopNavbarContents />
      </div>
    </div>
  )
}
