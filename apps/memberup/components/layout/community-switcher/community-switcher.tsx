'use client'

import Image from 'next/image'
import Link from 'next/link'
import { useEffect, useRef, useState } from 'react'
import { useMediaQuery } from 'react-responsive'

import { CommunitySwitcherContent } from './community-switcher-content'
import { Favicon } from '@/components/community/favicon'
import { ChevronDown20Icon, List20Icon } from '@/components/icons'
import { SkeletonBox } from '@/components/ui'
import { Button } from '@/components/ui/button'
import { Popover, PopoverAnchor, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { useStore } from '@/hooks/useStore'
import { smMediaQuery } from '@/lib/client/media-queries'
import memberupLogo from '@/public/assets/default/logos/memberup-logo.png'
import { IUserMembership } from '@/shared-types/interfaces'
import { getCommunityBaseURL } from '@/src/libs/utils'

export function CommunitySwitcher() {
  const [open, setOpen] = useState(false)
  const openRef = useRef(open)
  const membership = useStore((state) => state.community.membership)
  const user = useStore((state) => state.auth.user)
  const userMemberships = user?.user_memberships as IUserMembership[]
  const isMd = useMediaQuery(smMediaQuery)
  const loadingCommunity = useStore((state) => state.community.loadingCommunity)

  const membershipSetting = membership?.membership_setting

  const updateOpen = (value: boolean) => {
    openRef.current = value
    setOpen(value)
  }

  useEffect(() => {
    const appScrollArea = document.querySelector('#app-scroll-area > div[data-radix-scroll-area-viewport]')

    const onScroll = () => {
      if (openRef.current) updateOpen(false)
    }
    if (appScrollArea) appScrollArea.addEventListener('scroll', onScroll)

    return () => {
      if (appScrollArea) appScrollArea.removeEventListener('scroll', onScroll)
    }
  }, [])

  return (
    <div className="relative flex h-full max-w-full items-center" data-testid="community-switcher">
      <div className="block h-full md:hidden" data-testid="community-switcher-mobile">
        <Sheet open={open && !isMd} onOpenChange={(value) => updateOpen(value)}>
          <SheetTrigger asChild>
            <Button variant="inline" className="flex h-full w-[1.75rem] items-center justify-start overflow-hidden">
              <List20Icon className="relative -left-1 text-black-200 transition-colors hover:text-black-100 dark:text-black-100 dark:hover:text-black-200" />
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="flex flex-col">
            <SheetHeader>
              <SheetTitle>Switch Community</SheetTitle>
            </SheetHeader>
            <CommunitySwitcherContent
              membership={membership}
              userMemberships={userMemberships}
              onClose={() => updateOpen(false)}
            />
          </SheetContent>
        </Sheet>
      </div>

      <div className="min-w-0 flex-1" data-testid="community-switcher-main">
        {loadingCommunity ? (
          <div className="flex">
            <SkeletonBox className="h-10 w-10" variant="dialog" />
            <SkeletonBox className="ml-3 h-10 w-40" variant="dialog" />
          </div>
        ) : membership ? (
          <Link
            className="flex h-full shrink items-center truncate text-base font-semibold dark:text-white-500 md:text-lg"
            href={getCommunityBaseURL(membership)}
          >
            <Favicon
              className="mr-2 h-8 w-8 rounded-md text-[0.625rem] md:h-10 md:w-10 md:text-xs"
              communityName={membership.name}
              cropArea={membershipSetting?.favicon_crop_area}
              src={membershipSetting?.favicon}
              width={40}
              height={40}
              variant="inverted"
            />
            <span className="truncate">{membership.name}</span>
          </Link>
        ) : (
          <Link href="/community">
            <Image src={memberupLogo} width={158} height={22} alt="MemberUp" />
          </Link>
        )}
      </div>

      <div className="hidden h-full md:block" data-testid="community-switcher-desktop">
        <Popover open={open && isMd} onOpenChange={(value) => updateOpen(value)}>
          <PopoverAnchor asChild>
            <div className="absolute left-0 top-0 -z-10 h-full w-full" />
          </PopoverAnchor>
          <PopoverTrigger asChild>
            <Button variant="inline" className="h-full w-10">
              <ChevronDown20Icon className="text-black-200 transition-colors hover:text-black-100 dark:text-black-100 dark:hover:text-black-200" />
            </Button>
          </PopoverTrigger>
          <PopoverContent align="start" side="bottom" avoidCollisions={false}>
            <CommunitySwitcherContent
              membership={membership}
              userMemberships={userMemberships}
              onClose={() => updateOpen(false)}
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  )
}
