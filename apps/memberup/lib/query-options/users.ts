import { membershipPublicFields, membershipQueryOptions } from './communities'

export const userPublicFields = {
  id: true,
  first_name: true,
  last_name: true,
  username: true,
  image: true,
  role: true,
  status: true,
  createdAt: true,
  updatedAt: true,
  invite_token: true,
  reset_token: true,
  membership_id: true,
}

export const userProfilePublicFields = {
  bio: true,
  image: true,
  image_crop_area: true,
  cover_image: true,
  cover_image_crop_area: true,
  last_activity_at: true,
  social: true,
  personality_type: true,
  createdAt: true,
}

export const userMembershipPublicFields = {
  user_role: true,
  status: true,
  createdAt: true,
  id: true,
  membership_id: true,
}

export const userPublicQueryOptions = {
  select: {
    ...userPublicFields,
    profile: {
      select: {
        ...userProfilePublicFields,
      },
    },
    user_memberships: {
      where: {
        membership: {
          active: true,
        },
      },
      orderBy: {
        membership: {
          name: 'asc' as const,
        },
      },
      select: {
        ...userMembershipPublicFields,
        membership: {
          ...membershipQueryOptions,
        },
      },
    },
  },
}

export const userSummarizedPublicFields = {
  id: true,
  first_name: true,
  last_name: true,
  status: true,
  username: true,
  profile: {
    select: {
      bio: true,
      image: true,
      image_crop_area: true,
      location: true,
      last_activity_at: true,
    },
  },
}
