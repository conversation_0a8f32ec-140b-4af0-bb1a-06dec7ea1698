import { isNil } from 'lodash'

export function editorStringsEqual(val1: any, val2: any) {
  const str1 = isNil(val1) ? '' : String(val1)
  const str2 = isNil(val2) ? '' : String(val2)

  return str1.replace('<p></p>', '') === str2.replace('<p></p>', '')
}

export function removeWhitespaceAndNewlines(str: string) {
  return str?.replace(/\s+/g, '')
}

export function removeHTMLTags(html: string) {
  return html?.replace(/<[^>]*>/g, '')
}

export function addMentionPrefix(html: string) {
  const pattern = /(<[^>]+data-type\s*=\s*"mention"[^>]*>)([^<]*)(<\/[^>]+>)/g
  const replacement = '$1@$2$3'

  return html?.replace(pattern, replacement)
}

export const removeHtmlAndAtMentions = (html: string) => {
  return removeHTMLTags(addMentionPrefix(html)).replace('&nbsp;', ' ')
}
