import { membershipSettingsPublicFields, ownerQueryOptions } from '../query-options/communities'
import { prisma } from '@memberup/shared/src/libs/prisma/prisma'
import { IAuthenticatedUser } from '@/shared-types/interfaces'

export const activeUserQueryOptions = {
  select: {
    id: true,
    email: true,
    first_name: true,
    last_name: true,
    username: true,
    image: true,
    verification_code: true,
    role: true,
    status: true,
    banned_reason: true,
    is_primary: true,
    createdAt: true,
    updatedAt: true,
    profile: true,
    user_memberships: {
      where: {
        membership: {
          active: true,
        },
      },
      select: {
        membership: {
          select: {
            id: true,
            name: true,
            slug: true,
            createdAt: true,
            updatedAt: true,
            owner: { ...ownerQueryOptions },
            channels: {
              select: {
                id: true,
                name: true,
                description: true,
                slug: true,
                is_private: true,
                visibility: true,
              },
            },
            membership_setting: {
              select: membershipSettingsPublicFields,
            },
          },
        },
        id: true,
        status: true,
        user_role: true,
        createdAt: true,
      },
    },
  },
}

export const fetchUser: (id: string) => Promise<IAuthenticatedUser | null> = async (id: string) => {
  const user = await prisma.user.findUnique({
    where: { id },
    ...activeUserQueryOptions,
  })

  return user
}
