import { cache } from 'react'

import { membershipMemberListingQueryOptions, membershipQueryOptions } from '@/lib/query-options/communities'
import prisma from '@/shared-libs/prisma/prisma'
import { IMembership } from '@/shared-types/interfaces'

export const getCommunityDataBySlug: (slug: string) => Promise<IMembership | null> = async (slug: string) => {
  return (await prisma.membership.findUnique({
    where: { slug: slug },
    ...membershipQueryOptions,
  })) as IMembership
}

export const getCachedCommunityData = getCommunityDataBySlug

// export const getCommunityMembers = async (id: string) => {
//   const members = await prisma.userMembership.findMany({
//     ...membershipMemberListingQueryOptions,
//     where: {
//       ...membershipMemberListingQueryOptions.where,
//       membership_id: id,
//     },
//   })
//
//   if (members && members.length) {
//     return members.map((um: any) => ({ ...um.user, role: um.user_role }))
//   }
//
//   return []
// }
