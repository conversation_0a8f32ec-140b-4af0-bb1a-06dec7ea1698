import { IAuthenticatedUser, IUser } from '@/shared-types/interfaces'

export const getFullName = (user: IUser | IAuthenticatedUser): string =>
  user ? `${user.first_name} ${user.last_name}` : ''

const thousandsFormatter = new Intl.NumberFormat('en', {
  notation: 'compact',
  compactDisplay: 'short',
  minimumFractionDigits: 0,
  maximumFractionDigits: 1,
  roundingMode: 'floor',
})

export const formatThousands = (num: number) => {
  return thousandsFormatter.format(num)
}

export const addAtToUserMentionsHTML = (text: string) => {
  if (!text) return text

  return text.replace(/<span data-type="mention"[^>]*>(.*?)<\/span>/g, (_match, name) => `@${name}`)
}

export function convertDatesToISOStrings(obj: any): any {
  if (!obj) return obj
  if (Array.isArray(obj)) return obj.map((item) => convertDatesToISOStrings(item))
  if (typeof obj !== 'object') return obj

  return Object.entries(obj).reduce((acc, [key, value]) => {
    if (value instanceof Date) {
      acc[key] = value.toISOString()
    } else if (Array.isArray(value)) {
      acc[key] = value.map((item) => convertDatesToISOStrings(item))
    } else if (typeof value === 'object' && value !== null) {
      acc[key] = convertDatesToISOStrings(value)
    } else {
      acc[key] = value
    }
    return acc
  }, {} as any)
}
