import { ValidationResult } from '@/lib/validation/types'

export const validateVideo = (
  file: File,
  minWidth: number,
  minHeight: number,
  maxSizeMB?: number,
): Promise<ValidationResult> => {
  return new Promise((resolve) => {
    const video = document.createElement('video')
    video.preload = 'metadata'
    video.src = URL.createObjectURL(file)

    video.onloadedmetadata = () => {
      URL.revokeObjectURL(video.src)

      const fileSizeMB = file.size / (1024 * 1024)
      if (maxSizeMB && fileSizeMB > maxSizeMB) {
        resolve({ valid: false, error: `Video size exceeds maximum of ${maxSizeMB}MB` })
      }

      if (video.videoWidth < minWidth || video.videoHeight < minHeight) {
        resolve({ valid: false, error: `Video dimensions too small. Minimum: ${minWidth}x${minHeight}px` })
      }

      resolve({ valid: true })
    }

    video.onerror = () => {
      URL.revokeObjectURL(video.src)
      resolve({ valid: false, error: 'Failed to load video for validation' })
    }
  })
}
