import { protectedRoutesConfig } from './protected-routes.config'

export function isProtectedRoute(pathname: string) {
  return protectedRoutesConfig.some((route) => pathname.startsWith(route.path))
}

export function getRedirectPath(pathname: string) {
  const matchingRoute = protectedRoutesConfig.find((route) => pathname.startsWith(route.path))
  if (!matchingRoute) return null

  const redirectTo = matchingRoute.redirectTo || '/login'
  return `${redirectTo}?next=${encodeURIComponent(pathname)}`
}
