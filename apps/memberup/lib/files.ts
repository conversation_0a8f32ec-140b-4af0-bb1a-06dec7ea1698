export const getFileType = (file: File): { isImage: boolean; isVideo: boolean } => {
  const imageTypes = new Set(['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml', 'image/bmp'])

  // Common video MIME types
  const videoTypes = new Set(['video/mp4', 'video/webm', 'video/ogg', 'video/quicktime', 'video/x-msvideo'])

  return {
    isImage: imageTypes.has(file.type),
    isVideo: videoTypes.has(file.type),
  }
}
