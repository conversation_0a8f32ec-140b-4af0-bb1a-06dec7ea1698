import { z } from 'zod'

import { passwordSchema, slugRegex } from '@/lib/validation/zod'

const inviteTokenSchema = z
  .string()
  .optional()
  .transform((val) => (val === 'null' ? undefined : val))

export const nameSchema = z.object({
  first_name: z.string().max(100).min(1),
  last_name: z.string().max(100).min(1),
})

export const userSignUpSchema = z.object({
  first_name: z
    .string()
    .min(1, { message: 'First name is required' })
    .refine((value) => value.trim() === value, {
      message: 'First name cannot have leading or trailing spaces',
    }),
  last_name: z
    .string()
    .min(1, { message: 'Last name is required' })
    .refine((value) => value.trim() === value, {
      message: 'Last name cannot have leading or trailing spaces',
    }),
  email: z.string().email({ message: 'Please enter a valid email.' }),
  password: passwordSchema,
  invite_token: inviteTokenSchema,
})

export const userLoginSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email.' }),
  password: passwordSchema,
  invite_token: inviteTokenSchema,
})

export const usernameSchema = z.object({
  username: z.string().max(100).min(3).regex(slugRegex, {
    message: 'Your URL can only contain letters, numbers, underscores and hyphens',
  }),
})
