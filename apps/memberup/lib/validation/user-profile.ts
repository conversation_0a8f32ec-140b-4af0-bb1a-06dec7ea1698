import { z } from 'zod'

import { PERSONALITY_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { profileSocialLinks } from '@/lib/constants'

export const userProfileSchema = z.object({
  bio: z.string().max(150).optional(),
  location: z.string().max(100).optional(),
  personality_type: z.nativeEnum(PERSONALITY_TYPE_ENUM).optional(),
  social: z.object(
    profileSocialLinks.reduce((acc, link) => {
      const maxMessage = { message: `Must be ${link.maxLength} or fewer characters long` }

      acc[link.value] =
        link.type === 'url'
          ? z.string().max(link.maxLength, maxMessage).url({ message: 'Invalid URL' }).or(z.literal('')).optional()
          : z.string().max(link.maxLength, maxMessage).optional()

      return acc
    }, {}),
  ),
})
