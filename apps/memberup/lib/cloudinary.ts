import { TAppCropArea } from '@/shared-types/types'

export function generateCloudinaryPretransforms(cropArea?: TAppCropArea) {
  // At the time this code was initially written, the official Cloudinary SDK did not support cropping.
  // This is a workaround to achieve the same effect.
  const t = (value: number) => (value / 100).toFixed(4)

  return (
    (cropArea && `c_crop,w_${t(cropArea.width)},h_${t(cropArea.height)},x_${t(cropArea.x)},y_${t(cropArea.y)}`) || null
  )
}

export function cloudinaryLoader({
  loaderOptions,
  preTransforms = '',
}: {
  loaderOptions: {
    src: string
    width: number
    quality?: number
  }
  preTransforms?: string
}): string {
  const parts = loaderOptions.src.split('image/upload')

  return [parts[0], preTransforms, `/w_${Math.ceil(loaderOptions.width)},f_auto,q_80`, parts[1]].join()
}
