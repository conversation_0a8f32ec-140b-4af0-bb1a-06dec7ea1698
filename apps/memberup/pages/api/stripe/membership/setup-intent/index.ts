import authenticationMiddleware from '@/memberup/middlewares/authentication'
import membershipSettingMiddleware from '@/memberup/middlewares/membership-setting'
import { findUserById } from '@memberup/shared/src/libs/prisma/user'
import { updateUserProfile } from '@memberup/shared/src/libs/prisma/user-profile'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { IUser } from '@memberup/shared/src/types/interfaces'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import { stripeCreateSetupIntent, stripeGetOrCreateCustomer } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(membershipSettingMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { payment_method_types } = req.body
      const dbUser: IUser = await findUserById({
        where: { id: user.id },
        include: {
          profile: true,
          membership: {
            include: { membership_setting: true },
          },
        },
      })
      const userProfile = dbUser.profile
      const membership = dbUser.membership
      const membershipSetting = membership.membership_setting
      const connectedStripeAccountInfo = membershipSetting?.stripe_connect_account
      const stripeUserId = membershipSetting?.stripe_connect_account?.stripe_user_id
      let stripeCustomerId = userProfile?.stripe_customer_id
      const userFullName = getFullName(dbUser.first_name, dbUser.last_name, '')

      if (!stripeCustomerId) {
        const params = {
          email: user.email,
          description: `${membership.name}:${userFullName}`,
          name: userFullName,
          metadata: {
            membership_id: membership.id,
            membership_name: membership.name,
          },
        }

        try {
          const stripeCustomer = await stripeGetOrCreateCustomer(connectedStripeAccountInfo, params)
          const updatedProfile = await updateUserProfile({
            where: { user_id: user.id },
            data: { stripe_customer_id: stripeCustomer.id },
          })
          stripeCustomerId = updatedProfile.stripe_customer_id
        } catch (err) {
          return res.status(400).json({
            message: `Error setting up the customer_id.`,
          })
        }
      }

      const setupIntent = await stripeCreateSetupIntent(
        connectedStripeAccountInfo,
        {
          customer: stripeCustomerId,
          payment_method_types,
          usage: 'on_session',
        },
        stripeUserId
      )

      res.status(200).send({
        success: true,
        data: setupIntent,
      })
    } catch (err: any) {
      console.error(err)
      return res.status(500).json({ message: err.message })
    }
  })

export default handler
