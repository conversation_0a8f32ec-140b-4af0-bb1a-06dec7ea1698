import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeCreatePromotionCodeWithDiscount } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const stripeConnectAccount = user.membership_setting?.stripe_connect_account
    const stripeAccountId = user.membership_setting?.stripe_connect_account?.stripe_user_id

    if (!stripeConnectAccount) {
      return res.status(400).json({
        message: 'Membership is not configured properly. Please contact the owner.',
      })
    }

    const {
      code,
      amount_off,
      percent_off,
      name,
      duration = 'once',
      duration_in_months,
      max_redemptions,
      active = true,
    } = req.body

    if (!code) {
      return res.status(400).json({
        message: 'Promotion code is required',
      })
    }

    if (!amount_off && !percent_off) {
      return res.status(400).json({
        message: 'Either amount_off or percent_off is required',
      })
    }

    const result = await stripeCreatePromotionCodeWithDiscount(
      stripeConnectAccount,
      {
        amount_off,
        percent_off,
        name,
        duration,
        duration_in_months,
      },
      {
        code,
        active,
        max_redemptions,
      },
      stripeAccountId,
    )

    return res.status(200).send({
      success: true,
      data: result,
    })
  } catch (err: any) {
    console.log('Error creating promotion code', err)
    res.status(400).json({ message: err.message })
  }
})

export default handler
