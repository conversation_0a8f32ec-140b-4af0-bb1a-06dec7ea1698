import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeGetCoupon } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const user = req['user']
    const { id } = req.query
    const stripeConnectAccount = user.membership_setting?.stripe_connect_account
    const stripeAccountId = user.membership_setting?.stripe_connect_account?.stripe_user_id

    if (!stripeConnectAccount) {
      return res.status(200).send({
        success: true,
        data: null,
      })
    }

    const result = await stripeGetCoupon(stripeConnectAccount, id as string)
    return res.status(200).send({
      success: true,
      data: result,
    })
  } catch (err: any) {
    console.log('err --------', err)
    res.status(400).json({ message: err.message })
  }
})

export default handler
