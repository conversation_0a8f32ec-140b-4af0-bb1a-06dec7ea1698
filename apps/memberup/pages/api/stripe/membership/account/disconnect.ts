import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findMembershipSetting, updateMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { stripeDisconnectAccount } from '@memberup/shared/src/libs/stripe'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    let result = await findMembershipSetting({
      where: { membership_id: user.current_membership_id },
    })
    const disconnected = await stripeDisconnectAccount(
      result.stripe_live_mode,
      result.stripe_connect_account.stripe_user_id,
    )
    if (disconnected?.stripe_user_id) {
      result = await updateMembershipSetting({
        where: { id: result.id },
        data: { stripe_connect_account: null },
      })
    }
    res.status(200).send({ success: true, data: result })
  } catch (err: any) {
    console.log('disconnectError =====', err)
    res.status(400).json({ message: err.message })
  }
})

export default handler
