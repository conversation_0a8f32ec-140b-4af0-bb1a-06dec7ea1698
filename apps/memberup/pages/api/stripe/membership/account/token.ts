//@ts-nocheck
import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findMembership } from '@memberup/shared/src/libs/prisma/membership'
import { updateMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import {
  stripeCreatePrice,
  stripeCreateProduct,
  stripeGetAccountToken,
  stripeGetPrices,
  stripeGetProducts,
} from '@memberup/shared/src/libs/stripe'
import { USER_PLANS } from '@memberup/shared/src/settings/plans'
import { IMembership } from '@memberup/shared/src/types/interfaces'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.post(async (req, res) => {
  try {
    const { code, membership_slug, custom_host } = req.body
    let result: IMembership = null

    if (custom_host) {
      result = await findMembership({
        where: { membership_setting: { custom_host: custom_host as string } },
        include: { membership_setting: true },
      })
    } else {
      result = await findMembership({
        where: { slug: membership_slug as string },
        include: { membership_setting: true },
      })
    }

    const membershipSetting = result?.membership_setting
    if (membershipSetting) {
      const stripeConnectAccountToken = await stripeGetAccountToken(membershipSetting.stripe_live_mode, {
        grant_type: 'authorization_code',
        code: code,
      })
      const updateMembershipSettingPayload = {
        stripe_connect_account: stripeConnectAccountToken,
      } as any

      const connectAccountAccessToken = stripeConnectAccountToken?.access_token
      if (connectAccountAccessToken) {
        const stripeProducts = await stripeGetProducts(connectAccountAccessToken)
        let stripeProduct = stripeProducts.data.find((p) => p.metadata?.membership === result.slug)
        if (!stripeProduct) {
          stripeProduct = await stripeCreateProduct(connectAccountAccessToken, {
            name: result.name,
            metadata: {
              membership: result.slug,
            },
          })
        }
        if (stripeProduct) {
          const existingStripePrices = await stripeGetPrices(connectAccountAccessToken, {
            product: stripeProduct.id,
          })

          const stripePrices = (existingStripePrices?.data || []).map((p) => ({
            id: p.id,
            active: p.active,
            currency: p.currency,
            livemode: p.livemode,
            unit_amount: p.unit_amount,
            recurring: p.recurring,
            metadata: p.metadata,
            type: p.type,
          }))

          for (const userPlan of USER_PLANS) {
            let temp = stripePrices.find(
              (p) =>
                p.metadata?.planType === userPlan.type ||
                (p.recurring?.interval || null) === (userPlan.recurringInterval || null),
            )
            if (!temp) {
              temp = await stripeCreatePrice(connectAccountAccessToken, {
                unit_amount: userPlan.price * 100,
                currency: 'usd',
                active: false,
                recurring: userPlan.type !== 'one_time' ? { interval: userPlan.recurringInterval as any } : undefined,
                product: stripeProduct.id,
                metadata: {
                  name: userPlan.name,
                  title: userPlan.title,
                  planType: userPlan.planType,
                  description: userPlan.description,
                },
              })
              if (temp) {
                stripePrices.push({
                  id: temp.id,
                  active: temp.active,
                  currency: temp.currency,
                  livemode: temp.livemode,
                  unit_amount: temp.unit_amount || 0,
                  recurring: temp.recurring,
                  metadata: temp.metadata,
                  type: temp.type,
                })
              }
            }
          }
          updateMembershipSettingPayload['stripe_prices'] = stripePrices
        }
      }

      await updateMembershipSetting({
        where: { id: membershipSetting.id },
        data: updateMembershipSettingPayload,
      })

      return res.status(200).send({
        success: true,
      })
    }
    res.status(200).send({
      success: false,
    })
  } catch (err: any) {
    sentryCaptureException(err)
    res.status(200).send({
      success: false,
    })
    // res.status(400).json({ message: err.message })
  }
})

export default handler
