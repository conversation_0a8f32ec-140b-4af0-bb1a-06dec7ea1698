import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findUser } from '@memberup/shared/src/libs/prisma/user'
import { IUser } from '@memberup/shared/src/types/interfaces'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeGetPaymentMethod } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const user = req['user']
    const stripeConnectAccount = user.membership_setting?.stripe_connect_account

    if (!stripeConnectAccount) {
      return res.status(200).send({ success: true, data: null })
    }

    const dbUser: IUser = await findUser({ where: { id: user.id }, include: { profile: true } })
    const userProfile = dbUser.profile

    if (!userProfile?.stripe_payment_method_id) {
      return res.status(200).send({ success: true, data: null })
    }

    const result = await stripeGetPaymentMethod(stripeConnectAccount, userProfile.stripe_payment_method_id)
    res.status(200).send({ success: true, data: result })
  } catch (err: any) {
    res.status(400).json({ message: err.message })
  }
})

export default handler
