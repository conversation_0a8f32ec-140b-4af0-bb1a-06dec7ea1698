import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import Stripe from 'stripe'

import {
  STRIPE_APPLICATION_FEE_BASIC,
  STRIPE_APPLICATION_FEE_ENTERPRISE,
  STRIPE_APPLICATION_FEE_PRO,
  STRIPE_SECRET_KEY,
} from '@memberup/shared/src/config/envs'
import { MEMBERUP_PLAN_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status500 } from '@/shared-libs/api-utils'
import { findUserMembershipByIds } from '@/shared-libs/prisma/user-membership'
import { stripeCreatePaymentIntent, stripeGetPrices } from '@/shared-libs/stripe'
import { TStripeConnectAccount } from '@/shared-types/types'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { currency, lifetime_mode, payment_method, selected_price_id } = req.body
    const { membership_id: membershipId } = req.query

    const userMembership = await findUserMembershipByIds(membershipId, user.id)
    if (!userMembership) {
      return status400(res, `You must first start joining procedure.`)
    }

    const membership = userMembership.membership
    const membershipSetting = membership.membership_setting
    const connectedStripeAccountInfo = membershipSetting?.stripe_connect_account as TStripeConnectAccount
    const stripeProductId = membershipSetting?.stripe_product_id

    const stripeCustomerId = userMembership?.stripe_customer_id

    // Find the one time payment price
    const existingStripePricesResult = await stripeGetPrices(connectedStripeAccountInfo, {
      active: true,
      product: stripeProductId,
    })

    const existingStripePrices = existingStripePricesResult.data
    const stripePrice = existingStripePrices.find((p) => p.id === selected_price_id)
    if (!stripePrice) {
      return status400(res, `Price not found`)
    }

    const applicationFeePercent = STRIPE_APPLICATION_FEE_ENTERPRISE / 100

    const paymentIntent = await stripeCreatePaymentIntent(connectedStripeAccountInfo, {
      amount: stripePrice['unit_amount'],
      customer: stripeCustomerId,
      currency: currency || 'usd',
      application_fee_amount: stripePrice['unit_amount'] * applicationFeePercent,
      payment_method,
      confirm: true,
      metadata: {
        membership_id: membership.id,
      },
      automatic_payment_methods: {
        enabled: true,
        allow_redirects: 'never',
      },
    })
    return status200(res, paymentIntent)
  } catch (err: any) {
    console.error(err)
    return status500(res, err.message)
  }
})

export default handler
