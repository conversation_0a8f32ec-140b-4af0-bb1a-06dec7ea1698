import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import { stripeGetInvoiceById, getUserMembershipFromInvoice } from '@memberup/shared/src/libs/stripe'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { findMembershipBySlug } from '@/shared-libs/prisma/membership'
import { status400 } from '@/shared-libs/api-utils'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const { id, membership_slug, expand_subscription } = req.query
    
    // Get membership and connected account info
    const membership = await findMembershipBySlug(membership_slug as string)
    if (!membership) {
      return status400(res, 'The community does not exist')
    }

    const stripeConnectedAccountInfo = membership.membership_setting?.stripe_connect_account
    if (!stripeConnectedAccountInfo) {
      return status400(res, 'The community is not connected to Stripe')
    }

    if (expand_subscription === 'true') {
      // Get invoice with expanded subscription and userMembership
      const result = await getUserMembershipFromInvoice(
        STRIPE_SECRET_KEY, 
        id as string, 
        stripeConnectedAccountInfo.stripe_user_id
      )
      
      return res.status(200).send({
        success: true,
        data: result,
      })
    } else {
      // Original behavior - just get the invoice
      const result = await stripeGetInvoiceById(
        STRIPE_SECRET_KEY, 
        id as string, 
        undefined, 
        stripeConnectedAccountInfo.stripe_user_id
      )

      return res.status(200).send({
        success: true,
        data: result,
      })
    }
  } catch (err: any) {
    console.log('err --------', err)
    res.status(400).json({ message: err.message })
  }
})

export default handler
