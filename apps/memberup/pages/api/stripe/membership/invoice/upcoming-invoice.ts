import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import Stripe from 'stripe'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status500 } from '@/shared-libs/api-utils'
import { prisma } from '@/shared-libs/prisma/prisma'
import { USER_MEMBERSHIP_STATUS_ENUM } from '@/shared-types/enum'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).post(async (req, res) => {
  async function stripeGetUpcomingInvoice(stripeCustomerId, stripeConnectedAccount) {
    const params: Stripe.InvoiceRetrieveUpcomingParams = {
      customer: stripeCustomerId,
    }
    const options = stripeConnectedAccount
      ? {
          stripeAccount: stripeConnectedAccount.stripe_user_id,
        }
      : undefined

    const stripe = new Stripe(STRIPE_SECRET_KEY, {
      apiVersion: '2023-10-16',
      maxNetworkRetries: 2,
    })
    return await stripe.invoices.retrieveUpcoming(params, options)
  }

  try {
    const user = req['user']
    const { membership_id } = req.query as string

    const userMembership = await prisma.userMembership.findFirst({
      where: {
        membership_id: membership_id,
        user_id: user.id,
        status: USER_MEMBERSHIP_STATUS_ENUM.accepted,
      },
      include: {
        membership: {
          include: {
            membership_setting: true,
          },
        },
      },
    })
    if (!userMembership) {
      return status400(res, `User is not part of the community`)
    }

    const stripeConnectedAccount = userMembership.membership.membership_setting?.stripe_connect_account

    const stripeCustomerId = userMembership.stripe_customer_id
    if (!stripeCustomerId) {
      return status500(res, `Stripe user not set properly. Please contact support.`)
    }
    const result = await stripeGetUpcomingInvoice(stripeCustomerId, stripeConnectedAccount)

    return status200(res, result)
  } catch (err: any) {
    console.error(err)
    return status400(res, err.message)
  }
})

export default handler
