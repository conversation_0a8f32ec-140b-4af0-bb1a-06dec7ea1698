import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import Stripe from 'stripe'

import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status500 } from '@/shared-libs/api-utils'
import { findMembershipBySlug } from '@/shared-libs/prisma/membership'
import { stripeGetInvoices } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { created_gte, created_lt, limit, starting_after, membership_slug } = req.body

    const membership = await findMembershipBySlug(membership_slug)
    if (!membership) {
      return status400(res, 'The community does not exist')
    }

    const stripeConnectedAccountInfo = membership.membership_setting?.stripe_connect_account
    if (!stripeConnectedAccountInfo) {
      return status400(res, 'The community is not connected to Stripe')
    }

    const stripe_customer_id = user.user_memberships.find(
      (um) => um.membership_id === membership.id,
    )?.stripe_customer_id

    const params: Stripe.InvoiceListParams = {
      customer: stripe_customer_id,
      limit: limit ? parseInt(limit as string) : 100,
      starting_after: starting_after as string,
      expand: ['data.charge'],
    }
    if (created_gte || created_lt) {
      params['created'] = {
        gte: created_gte,
        lt: created_lt,
      }
    }
    const result = await stripeGetInvoices(params, stripeConnectedAccountInfo)
    return status200(res, result)
  } catch (err: any) {
    console.error(err)
    return status500(res, err.message)
  }
})

export default handler
