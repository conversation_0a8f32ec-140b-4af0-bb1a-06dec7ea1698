import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripegetRewardfulPayoutsMain } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const { limit, created_gte, created_lt, starting_after } = req.query
    const where = {
      limit: limit ? parseInt(limit as string) : 100,
      starting_after: starting_after as string,
    }
    if (created_gte || created_lt) {
      where['created'] = {
        gte: created_gte,
        lt: created_lt,
      }
    }
    const result = await stripegetRewardfulPayoutsMain(STRIPE_SECRET_KEY, where)
    res.status(200).send({ success: true, data: result })
  } catch (err: any) {
    res.status(400).json({ message: err.message })
  }
})

export default handler
