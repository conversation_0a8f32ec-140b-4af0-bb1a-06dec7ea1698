import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeGetCouponList, stripeGetCouponListMain } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const result = await stripeGetCouponListMain(STRIPE_SECRET_KEY)
    return res.status(200).send({
      data: result.data,
    })
  } catch (err: any) {
    console.log('err --------', err)
    res.status(400).json({ message: err.message })
  }
})

export default handler
