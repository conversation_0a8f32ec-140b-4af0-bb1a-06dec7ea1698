import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import {
  stripeCreateCouponMain,
  stripeCreatePromotionCodeMain,
  stripeDeleteCoupon,
  stripeDeleteCouponMain,
} from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .post(async (req, res) => {
    try {
      const { amount_off, code, percent_off, max_redemptions } = req.body
      const stripeCoupon = await stripeCreateCouponMain(STRIPE_SECRET_KEY, {
        amount_off,
        percent_off,
        max_redemptions,
      })
      let stripePromotionCode

      if (code) {
        stripePromotionCode = await stripeCreatePromotionCodeMain(STRIPE_SECRET_KEY, {
          coupon: stripeCoupon.id,
          code,
        })
      }

      return res.status(200).send({
        success: true,
        data: {
          stripe_coupon: stripeCoupon,
          stripe_promotion_code: stripePromotionCode,
        },
      })
    } catch (err: any) {
      console.log('err --------', err)
      res.status(400).json({ message: err.message })
    }
  })
  .delete(async (req, res) => {
    try {
      const user = req['user']
      const { coupon_id } = req.body
      const result = await stripeDeleteCouponMain(STRIPE_SECRET_KEY, coupon_id)
      return res.status(200).send({
        success: result.deleted,
      })
    } catch (err: any) {
      console.log('err --------', err)
      res.status(400).json({ message: err.message })
    }
  })
export default handler
