import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeGetCustomertBalanceTransactionsMain } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const user = req['user']
    const stripeCustomerId = user.membership_setting?.stripe_customer_id
    const { limit, ending_before, starting_after } = req.query

    if (!stripeCustomerId) {
      res.status(200).send({ success: true, data: { data: [], has_more: false } })
    }

    const params = {
      limit: limit ? parseInt(limit as string) : 100,
      starting_after: starting_after as string,
      ending_before: ending_before as string,
    }

    const result = await stripeGetCustomertBalanceTransactionsMain(STRIPE_SECRET_KEY, stripeCustomerId, params)
    res.status(200).send({ success: true, data: result })
  } catch (err: any) {
    res.status(400).json({ message: err.message })
  }
})

export default handler
