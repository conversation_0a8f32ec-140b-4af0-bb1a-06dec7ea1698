import { captureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findUser } from '@memberup/shared/src/libs/prisma/user'
import { IUser } from '@memberup/shared/src/types/interfaces'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { updateUserProfile } from '@/shared-libs/prisma/user-profile'
import { stripeUpdateCustomer, stripeUpdateMembershipSubscription } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']

    const dbUser: IUser = await findUser({ where: { id: user.id }, include: { profile: true } })
    const userProfile = dbUser.profile

    if (!userProfile?.stripe_customer_id) {
      return res.status(200)
    }

    const paymentMethodId = req.query.paymentMethodId as string

    // stripeConnectAccount === null as this isn't a connected account
    await stripeUpdateCustomer(null, userProfile.stripe_customer_id, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    })

    // stripeConnectAccount === null as this isn't a connected account
    if (userProfile.stripe_subscription_id) {
      await stripeUpdateMembershipSubscription(null, userProfile.stripe_subscription_id, {
        default_payment_method: paymentMethodId,
      })
    }

    await updateUserProfile({
      where: {
        id: userProfile.id,
      },
      data: {
        stripe_payment_method_id: paymentMethodId,
      },
    })

    res.status(200)
  } catch (error) {
    console.error(error)
    captureException(error)
    res.status(500).end()
  }
})

export default handler
