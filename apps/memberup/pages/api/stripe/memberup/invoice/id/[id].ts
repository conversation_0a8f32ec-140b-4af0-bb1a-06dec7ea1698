import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import { stripeGetInvoiceById } from '@memberup/shared/src/libs/stripe'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const { id } = req.query
    const result = await stripeGetInvoiceById(STRIPE_SECRET_KEY, id as string)

    return res.status(200).send({
      success: true,
      data: result,
    })
  } catch (err: any) {
    console.log('err --------', err)
    res.status(400).json({ message: err.message })
  }
})

export default handler
