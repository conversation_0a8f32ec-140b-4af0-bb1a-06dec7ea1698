import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import Stripe from 'stripe'

import { STRIPE_PRODUCT_IDS_MAP, STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeGetPricesMain, stripeGetUpcomingInvoiceMain, stripeRetrieveSubscriptionMain } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const { plan, plan_price, interval, coupon } = req.body
    const user = req['user']
    const membershipSetting = user.membership_setting
    const stripeCustomerId = membershipSetting?.stripe_customer_id
    const stripeSubscriptionId = membershipSetting?.stripe_subscription_id

    if (!stripeSubscriptionId) {
      return res.status(400).json({
        message: `Membership doesn't have subscribed any paid plan.`,
      })
    }

    if (!stripeCustomerId) {
      return res.status(400).json({
        message: `Membership doesn't have stripe setup. Please contact the support.`,
      })
    }

    const productId = STRIPE_PRODUCT_IDS_MAP[plan]
    const existingStripePrices = await stripeGetPricesMain(STRIPE_SECRET_KEY, {
      active: true,
      product: productId,
      recurring: {
        interval: interval || membershipSetting.stripe_enable_annual ? 'year' : 'month',
      },
    })
    const stripePrice = existingStripePrices?.data?.find((p) => p.unit_amount === plan_price * 100)
    if (!stripePrice?.id)
      return res.status(400).json({ message: `There is no active stripe price. Please ask admin about this.` })

    const currentSubscription = await stripeRetrieveSubscriptionMain(STRIPE_SECRET_KEY, stripeSubscriptionId)

    const proration_date = Math.floor(Date.now() / 1000)

    let params: Stripe.InvoiceRetrieveUpcomingParams = {}

    if (currentSubscription) {
      // See what the next invoice would look like with a price switch
      // and proration set:
      const items = [
        {
          id: currentSubscription.items.data[0].id,
          price: stripePrice.id, // Switch to new price
        },
      ]

      params = {
        customer: stripeCustomerId,
        subscription: stripeSubscriptionId,
        subscription_items: items,
        subscription_proration_date: proration_date,
        coupon: coupon,
      }
    } else if (coupon) {
      const items = [
        {
          price: stripePrice.id, // Switch to new price
        },
      ]

      params = {
        customer: stripeCustomerId,
        subscription_items: items,
        coupon: coupon,
      }
    }

    const result = await stripeGetUpcomingInvoiceMain(STRIPE_SECRET_KEY, params)
    result['prorated_amount'] = (result.lines.data[0]?.amount || 0) + (result.lines.data[1]?.amount || 0)
    res.status(200).send({ success: true, data: { ...result, proration_date } })
  } catch (err: any) {
    res.status(400).json({ message: err.message })
  }
})

export default handler
