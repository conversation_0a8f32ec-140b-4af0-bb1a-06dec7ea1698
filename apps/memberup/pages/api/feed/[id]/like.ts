import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findFeedById } from '@memberup/shared/src/libs/prisma/feed'
import { createStreamLike, deleteStreamReaction } from '@memberup/shared/src/libs/stream-chat'
import { CHANNEL_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status400, status500 } from '@/shared-libs/api-utils'
import { knockTriggerWorkflow } from '@/shared-libs/knock'
import { prisma } from '@/shared-libs/prisma/prisma'
import { handleUserAction } from '@/shared-libs/prisma/reaction'
import { getFullName } from '@/shared-libs/profile'
import { stripHtml } from '@/shared-libs/string-utils'
import { KNOCK_WORKFLOW_ENUM } from '@/shared-types/enum'

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .use(authenticationMiddleware)
  .put(async (req, res) => {
    try {
      const { id } = req.query
      const user = req['user']

      const post = await prisma.feed.findUnique({
        where: {
          id: id as string,
        },
        include: {
          channel: {
            include: {
              membership: true,
            },
          },
          user: true,
        },
      })

      if (!post) {
        return status400(res, 'Post not found')
      }

      const isComment = post.hierarchy_order ? true : false

      // try to upsert a reaction
      const type = 'like'
      const message_id = post.id
      const user_id = user.id
      const metadata = {}

      const reaction = await prisma.reaction.upsert({
        where: { type_message_user_unique: { type, message_id, user_id } },
        create: {
          type,
          message_id,
          user_id,
          metadata,
        },
        update: {
          metadata,
        },
      })

      const wasCreated = reaction.createdAt.getTime() === reaction.updatedAt.getTime()
      if (wasCreated) {
        const reactionData = {
          id: reaction.id,
          type: 'like',
          user_id: user.id,
        }
        await createStreamLike(CHANNEL_TYPE_ENUM.team, post.channel_id, post.id, reactionData)
        await handleUserAction(user, 'like', post.user_id, 'LIKE', post.id, post.channel.membership_id)

        if (post.user.id !== user.id) {
          let notificationURL = ''
          if (isComment) {
            const parentMessage = await prisma.feed.findUnique({
              where: {
                id: post.parent_id as string,
              },
              include: {
                user: {
                  include: {
                    membership: true,
                  },
                },
              },
            })

            if (parentMessage.permalink) {
              notificationURL = `/post/${parentMessage.permalink}`
            } else {
              notificationURL = `/post/${parentMessage.id}`
            }
          } else {
            if (post.permalink) {
              notificationURL = `/post/${post.permalink}`
            } else {
              notificationURL = `/post/${post.id}`
            }
          }

          const messageType = isComment ? 'comment' : 'post'
          let messageContent = post.title

          if (messageType === 'comment') {
            messageContent = stripHtml(post.text)
          }

          knockTriggerWorkflow(
            KNOCK_WORKFLOW_ENUM.new_like,
            [post.user.id],
            {
              id: reaction.id,
              actor_name: getFullName(user.first_name, user.last_name),
              message_type: messageType,
              message_content: messageContent,
              community_name: post.channel.membership.name,
              url: notificationURL,
            },
            user.id,
            post.channel.membership_id,
          )
        }
      }
      return res.status(200).json(reaction)
    } catch (err) {
      console.log(err)
      Sentry.captureException(err)
      return status500(res, err.message)
    }
  })
  .delete(async (req, res) => {
    try {
      const { id } = req.query
      const user = req['user']
      const post = await findFeedById(id as string, {
        include: {
          channel: true,
        },
      })

      if (!post) {
        return res.status(404).end()
      }
      const type = 'like'
      const result = await prisma.reaction.deleteMany({
        where: {
          user_id: user.id,
          message_id: post.id,
          type: type,
        },
      })

      if (result.count > 0) {
        await deleteStreamReaction(post.id, 'like', user.id)
        await handleUserAction(user, 'unlike', post.user_id, 'LIKE', post.id, post.channel.membership_id)
      }
      return res.status(200).json({ success: true })
    } catch (err) {
      console.error(err)
      Sentry.captureException(err)
      return status500(res, err.message)
    }
  })

export default handler
