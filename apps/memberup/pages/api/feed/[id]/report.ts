import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findFeedById, updateFeed } from '@memberup/shared/src/libs/prisma/feed'
import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).put(async (req, res) => {
  try {
    const user = req['user']
    const {
      query: { id },
      body,
    } = req
    const feed = await findFeedById(id as string, {
      include: {
        user: true,
      },
    })

    if (!feed) {
      return res.status(404).json({
        message: `Post not found.`,
      })
    }
    if (feed.user_id == user.id) {
      return res.status(403).json({
        message: `You cannot report your own post.`,
      })
    }
    const isResourceFromAdmin = [USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner, 'global_admin'].includes(
      feed.user.role as any,
    )
    /* a member user cannot report a creator resource */
    if (user.role === USER_ROLE_ENUM.member && isResourceFromAdmin) {
      return res.status(403).json({
        message: `You cannot report a creator's post.`,
      })
    }

    const result = await updateFeed({
      where: { id: id as string },
      data: body,
    })

    if (result?.result?.id) {
      return res.status(200).send({ success: true })
    }

    res.status(400).json(errorHandler(result, 'Post'))
  } catch (error) {
    console.error(error)
    Sentry.captureException(error)
    res.status(500).end()
  }
})

export default handler
