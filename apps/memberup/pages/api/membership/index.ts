import { Prisma } from '@prisma/client'
import * as Sentry from '@sentry/nextjs'
import _kebabCase from 'lodash/kebabCase'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import {
  activeCampaignGetContactFieldValues,
  activeCampaignGetContacts,
  activeCampaignUpdateContactCustomFieldValue,
} from '@memberup/shared/src/libs/active-campaign'
import { findMembership } from '@memberup/shared/src/libs/prisma/membership'
import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { IMembership } from '@memberup/shared/src/types/interfaces'
import { getCommunityDataBySlug } from '@/lib/server/communities'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import {
  activeCampaignAddContact,
  activeCampaignAddContactToList,
  activeCampaignCreateContactCustomFieldValue,
  activeCampaignUpdateContact,
} from '@/shared-libs/active-campaign'
import { updateAlgoliaMembersIndexForUserId } from '@/shared-libs/algolia'
import { status200 } from '@/shared-libs/api-utils'
import { createMembership } from '@/shared-libs/prisma/membership'
import prisma from '@/shared-libs/prisma/prisma'
import { getRandomStr } from '@/shared-libs/string-utils'
import { DefaultMembershipSetting } from '@/shared-settings/membership'
import { MEMBERUP_PLAN_ENUM } from '@/shared-types/enum'

const ACTIVE_CAMPAIGN_API_URL = process.env.ACTIVE_CAMPAIGN_API_URL
const ACTIVE_CAMPAIGN_API_KEY = process.env.ACTIVE_CAMPAIGN_API_KEY
const ACTIVE_CAMPAIGN_ENABLED = process.env.ACTIVE_CAMPAIGN_ENABLED === 'true'
const ACTIVE_CAMPAIGN_CONTACT_LIST_ID = process.env.ACTIVE_CAMPAIGN_CONTACT_LIST_ID

export const addContactToActiveCampaign = async (email: string, firstName: string, lastName: string, host: string) => {
  if (ACTIVE_CAMPAIGN_ENABLED && ACTIVE_CAMPAIGN_API_URL && ACTIVE_CAMPAIGN_API_KEY) {
    try {
      const temp = await activeCampaignGetContacts(ACTIVE_CAMPAIGN_API_URL, ACTIVE_CAMPAIGN_API_KEY, email)
      let contactId = temp?.contacts?.[0]?.id

      if (contactId) {
        await activeCampaignUpdateContact(
          ACTIVE_CAMPAIGN_API_URL,
          ACTIVE_CAMPAIGN_API_KEY,
          contactId,
          email,
          firstName,
          lastName,
        )
      } else {
        const temp1 = await activeCampaignAddContact(
          ACTIVE_CAMPAIGN_API_URL,
          ACTIVE_CAMPAIGN_API_KEY,
          email,
          firstName,
          lastName,
          [],
        )
        contactId = temp1?.contact?.id
        if (contactId) {
          await activeCampaignAddContactToList(
            ACTIVE_CAMPAIGN_API_URL,
            ACTIVE_CAMPAIGN_API_KEY,
            parseInt(`${ACTIVE_CAMPAIGN_CONTACT_LIST_ID}`),
            parseInt(`${contactId}`),
          )
        }
      }

      if (contactId) {
        const customFieldValues = await activeCampaignGetContactFieldValues(
          ACTIVE_CAMPAIGN_API_URL,
          ACTIVE_CAMPAIGN_API_KEY,
          contactId,
        )

        let membershipFieldValue = customFieldValues?.fieldValues?.find((item) => item.field === '12')
        if (membershipFieldValue) {
          activeCampaignUpdateContactCustomFieldValue(
            ACTIVE_CAMPAIGN_API_URL,
            ACTIVE_CAMPAIGN_API_KEY,
            contactId,
            membershipFieldValue.field,
            membershipFieldValue.id,
            'Incomplete',
          )
        } else {
          activeCampaignCreateContactCustomFieldValue(
            ACTIVE_CAMPAIGN_API_URL,
            ACTIVE_CAMPAIGN_API_KEY,
            contactId,
            '12',
            'Incomplete',
          )
        }

        membershipFieldValue = customFieldValues?.fieldValues?.find((item) => item.field === '4')
        if (membershipFieldValue) {
          activeCampaignUpdateContactCustomFieldValue(
            ACTIVE_CAMPAIGN_API_URL,
            ACTIVE_CAMPAIGN_API_KEY,
            contactId,
            membershipFieldValue.field,
            membershipFieldValue.id,
            `https://${host}`,
          )
        } else {
          activeCampaignCreateContactCustomFieldValue(
            ACTIVE_CAMPAIGN_API_URL,
            ACTIVE_CAMPAIGN_API_KEY,
            contactId,
            '4',
            `https://${host}`,
          )
        }
      }
    } catch (err) {
      console.log('err ====', err)
    }
  }
}

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .get(async (req, res) => {
    try {
      const { slug } = req.query

      const membership = await getCommunityDataBySlug(slug as string)
      if (!membership) {
        return res.status(404).json('Membership not found')
      }

      const owner = membership.owner
      // const membershipSettingUpdatePayload: any = {}
      // const connectedStripeAccountInfo = membershipSetting?.stripe_connect_account
      // let stripeProductId = membershipSetting.stripe_product_id

      // TODO: Remove this once we have a better way to handle this.
      // if (connectedStripeAccountInfo) {
      //     if (!stripeProductId) {
      //         const stripeProducts = await stripeGetProducts(connectedStripeAccountInfo)
      //         let tempProduct = stripeProducts.data.find(
      //             (p) => p.metadata?.membership === membership.slug
      //         )
      //
      //         if (!tempProduct) {
      //             tempProduct = await stripeCreateProduct(connectedStripeAccountInfo, {
      //                 name: `${membership.name}`,
      //                 metadata: {
      //                     membership: membership.slug,
      //                 },
      //             })
      //         }
      //         membershipSettingUpdatePayload.stripe_product_id = tempProduct?.id
      //         stripeProductId = tempProduct?.id
      //     }
      //
      //     if (stripeProductId && !membershipSetting?.stripe_prices?.length) {
      //         const existingStripePrices = await stripeGetPrices(connectedStripeAccountInfo, {
      //             product: stripeProductId,
      //         })
      //
      //         const stripePrices = (existingStripePrices?.data || []).map((p) => ({
      //             id: p.id,
      //             active: p.active,
      //             currency: p.currency,
      //             livemode: p.livemode,
      //             unit_amount: p.unit_amount,
      //             recurring: p.recurring,
      //             metadata: p.metadata,
      //         }))
      //
      //         for (const userPlan of USER_PLANS) {
      //             let temp = stripePrices.find(
      //                 (p) =>
      //                     p.metadata?.planType === userPlan.type ||
      //                     p.recurring?.interval === (userPlan.recurringInterval || null)
      //             )
      //
      //             if (!temp) {
      //                 temp = await stripeCreatePrice(connectedStripeAccountInfo, {
      //                     unit_amount: userPlan.price * 100,
      //                     currency: 'usd',
      //                     recurring: {interval: userPlan.recurringInterval as any},
      //                     product: stripeProductId,
      //                     metadata: {
      //                         name: userPlan.name,
      //                         title: userPlan.title,
      //                         planType: userPlan.planType,
      //                         description: userPlan.description,
      //                     },
      //                 })
      //                 if (temp) {
      //                     stripePrices.push({
      //                         id: temp.id,
      //                         active: temp.active,
      //                         currency: temp.currency,
      //                         livemode: temp.livemode,
      //                         unit_amount: temp.unit_amount || 0,
      //                         recurring: temp.recurring,
      //                         metadata: temp.metadata,
      //                     })
      //                 }
      //             }
      //         }
      //         membershipSettingUpdatePayload.stripe_prices = stripePrices
      //     }
      // }

      // if (Object.keys(membershipSettingUpdatePayload).length) {
      //     membershipSetting = await updateMembershipSetting({
      //         where: {id: membershipSetting.id},
      //         data: membershipSettingUpdatePayload,
      //         include: {active_spark_category: true},
      //     })
      // }

      // if (membershipSetting?.stripe_subscription_id) {
      //     const stripeSubscription = await stripeGetSubscription(
      //         STRIPE_SECRET_KEY,
      //         membershipSetting.stripe_subscription_id
      //     )
      //     membershipSetting['stripe_subscription'] = stripeSubscription
      // }
      return status200(res, {
        membership,
        owner,
      })
    } catch (err: any) {
      console.error(err)
      return res.status(500).json({ message: err.message })
    }
  })
  .use(authenticationMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      let membershipName: string
      let slug: string
      let existingMembership: IMembership

      // Generate a community slug
      do {
        membershipName = `${user.first_name} ${user.last_name} ${getRandomStr(6)}`
        slug = `${_kebabCase(membershipName)}`
        existingMembership = await findMembership({
          where: { slug },
        })
      } while (existingMembership)

      const result = await createMembership({
        data: {
          name: membershipName,
          active: false,
          brand: 'Default',
          slug,
          owner_id: user.id,
          membership_setting: {
            create: {
              ...DefaultMembershipSetting,
              external_links: [{ url: 'https://the-affiliate-link', label: 'Create Your Own Community' }],
              plan: MEMBERUP_PLAN_ENUM.enterprise,
              time_zone: user.time_zone || DefaultMembershipSetting.time_zone,
            } as any,
          },
        },
        include: {
          membership_setting: true,
        },
      })

      await prisma.userMembership.create({
        data: {
          user_id: req['user'].id,
          membership_id: result.id,
          user_role: USER_ROLE_ENUM.owner,
          status: 'accepted',
        },
      })

      // Create the spark categories and questions
      await prisma.$executeRaw(
        Prisma.raw(`
                    INSERT INTO spark_membership_categories
                    (id, name, enabled, createdAt, updatedAt, spark_seed_category_id, membership_id)
                    SELECT UUID(),
                           name,
                           enabled,
                           createdAt,
                           updatedAt,
                           id,
                           '${result.id}'
                    FROM spark_seed_categories
                `),
      )

      await prisma.$executeRaw(
        Prisma.raw(`
                    INSERT INTO spark_membership_questions
                    (id, content, enabled, createdAt, updatedAt, spark_membership_category_id, membership_id)
                    SELECT UUID(),
                           ssq.content,
                           ssq.enabled,
                           ssq.createdAt,
                           ssq.updatedAt,
                           smc.id,
                           '${result.id}'
                    FROM spark_seed_questions ssq
                             JOIN spark_seed_categories ssc ON ssq.spark_seed_category_id = ssc.id
                             JOIN spark_membership_categories smc ON smc.spark_seed_category_id = ssc.id
                `),
      )

      const { users, ...membershipData } = result
      const membershipSetting = membershipData.membership_setting

      // We are just interested on creators for active campaign.
      await addContactToActiveCampaign(user.email, user.first_name, user.last_name, membershipData.slug)
      await updateAlgoliaMembersIndexForUserId(user.id)

      return res.json({
        success: true,
        data: {
          membership: {
            ...membershipData,
            membership_setting: {
              completed_membership: membershipSetting.completed_membership,
              plan: membershipSetting.plan,
              stripe_enable_annual: membershipSetting.stripe_enable_annual,
            },
          },
        },
      })
    } catch (error) {
      console.error(error)
      Sentry.captureException(error)
      return res.status(500).end()
    }
  })

export default handler
