import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { USER_MEMBERSHIP_STATUS_ENUM, USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { membershipSettingsPublicFields } from '@/lib/query-options/communities'
import { userSummarizedPublicFields } from '@/lib/query-options/users'
import { status400 } from '@/shared-libs/api-utils'
import prisma from '@/shared-libs/prisma/prisma'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.get(async (req, res) => {
  try {
    const { id } = req.query

    const membership = await prisma.membership.findUnique({
      where: {
        id: id as string,
      },
      select: {
        id: true,
        membership_setting: {
          select: {
            members_count: true,
          },
        },
      },
    })

    if (!membership) {
      return res.status(404).end('Community not found')
    }

    const activeDate = new Date(Date.now() - 90 * 1000)

    const [totalAdmins, totalOnline] = await Promise.all([
      prisma.userMembership.count({
        where: {
          membership_id: membership.id,
          user_role: {
            not: USER_ROLE_ENUM.member,
          },
          status: 'accepted',
        },
      }),
      prisma.userMembership.count({
        where: {
          membership_id: membership.id,
          status: 'accepted',
          user: {
            profile: {
              last_activity_at: {
                gte: activeDate,
              },
            },
          },
        },
      }),
    ])

    const recentActiveMembers = await prisma.user.findMany({
      where: {
        user_memberships: {
          some: {
            membership_id: membership.id,
            status: USER_MEMBERSHIP_STATUS_ENUM.accepted,
          },
        },
        profile: {
          last_activity_at: {
            gte: activeDate,
          },
          last_session_initialized_at: {
            not: null,
          },
        },
      },
      select: userSummarizedPublicFields,
      orderBy: {
        profile: {
          last_session_initialized_at: 'asc',
        },
      },
      take: 11,
    })

    return res.json({
      data: {
        recentActiveMembers,
        totalMembers: membership.membership_setting.members_count,
        totalAdmins,
        totalOnline,
      },
    })
  } catch (err: any) {
    Sentry.captureException(err)
    console.error(err)
    return res.status(500).end()
  }
})

export default handler
