import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { checkIsPaidMembership } from '@/memberup/libs/utils'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import prisma from '@/shared-libs/prisma/prisma'
import { setupUserOnExternalServices } from '@/shared-libs/user'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  const { membership_id } = req.query
  const userMembership = await prisma.userMembership.findUnique({
    where: {
      user_id_membership_id: {
        user_id: req['user'].id,
        membership_id: membership_id as string,
      },
    },
  })

  if (!userMembership) {
    return res.status(404).json({
      success: false,
      message: 'UserMembership not found',
    })
  }
  return res.json({ success: true, data: userMembership })
})
export default handler
