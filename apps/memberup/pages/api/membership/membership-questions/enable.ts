import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import prisma from '@/shared-libs/prisma/prisma'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { enabled, membership_id } = req.body

    const userMembership = await prisma.userMembership.findFirst({
      where: {
        user_id: user.id,
        membership_id: membership_id as string,
        user_role: 'owner',
      },
      include: {
        membership: {
          include: {
            membership_setting: true,
          },
        },
      },
    })

    if (!userMembership) {
      return res.status(400).json({
        success: false,
        message: 'UserMembership not found',
      })
    }

    const membershipSettings = userMembership.membership.membership_setting
    const updatedMembershipSettings = await prisma.membershipSetting.update({
      where: {
        id: membershipSettings.id,
      },
      data: {
        form_enabled: enabled,
      },
    })
    res.status(200).json({ data: updatedMembershipSettings })
  } catch (err: any) {
    console.error(err)
    res.status(500).json(errorHandler(err, ''))
  }
})

export default handler
