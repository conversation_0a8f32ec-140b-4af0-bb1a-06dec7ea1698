import axios from 'axios'

export default async function handler(req, res) {
  const { url } = req.query

  if (!url) {
    return res.status(400).send('URL is required')
  }

  try {
    const response = await axios.get(url, { responseType: 'arraybuffer' })
    res.setHeader('Content-Type', 'application/pdf')
    res.send(response.data)
  } catch (error) {
    console.error(error)
    res.status(500).send('Error fetching PDF')
  }
}
