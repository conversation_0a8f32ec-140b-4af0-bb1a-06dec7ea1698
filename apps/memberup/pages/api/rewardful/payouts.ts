import _get from 'lodash/get'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { getRewardfulPayouts } from '@memberup/shared/src/libs/rewardful'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const REWARDFUL_API_SECRET = process.env.REWARDFUL_API_SECRET
const STRIPE_LIVE_MODE = process.env.NEXT_PUBLIC_STRIPE_LIVE_MODE

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const user = req['user']
    if (STRIPE_LIVE_MODE !== 'true') {
      return res.json({ success: false })
    }
    const { state, affiliate, commissions } = req.query

    if (!user.profile?.affiliate?.['id']) {
      return res.json({ success: false })
    }

    const params = {
      affiliate_id: user.profile.affiliate['id'],
    }

    if (state) {
      params['state'] = state
    }

    const expand = []
    if (affiliate) {
      expand.push('affiliate')
    }
    if (commissions) {
      expand.push('commissions')
    }
    if (expand.length) {
      params['expand'] = expand
    }

    const result = await getRewardfulPayouts(REWARDFUL_API_SECRET, params)

    res.json({ success: true, data: result })
  } catch (err) {
    res.status(400).end(_get(err, ['response', 'data', 'details', 0], 'Unknow Error'))
  }
})

export default handler
