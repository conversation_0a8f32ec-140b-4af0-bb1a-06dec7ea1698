// TODO: Remove unsafe API endpoint (MEM-4031)
import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { knockTriggerWorkflow } from '@memberup/shared/src/libs/knock'
import { findUsers } from '@memberup/shared/src/libs/prisma/user'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { workflow_key, data } = req.body
    let recipients = req.body.recipients || []

    if (!recipients.length) {
      const members = await findUsers({
        select: {
          id: true,
        },
        where: {
          membership_id: user.current_membership_id,
          id: { not: user.id },
        },
      })
      recipients = members.docs.map((m) => m.id)
    }

    if (recipients.length) {
      knockTriggerWorkflow(workflow_key, recipients, data, user.id, user.current_membership_id)
    }

    res.status(200).send({
      success: true,
    })
  } catch (error) {
    console.error(error)
    Sentry.captureException(error)
    res.status(500).end()
  }
})

export default handler
