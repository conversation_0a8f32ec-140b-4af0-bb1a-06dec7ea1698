import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { error<PERSON><PERSON><PERSON> } from '@memberup/shared/src/libs/prisma/error-handler'
import { VISIBILITY_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import { status200, status400, status403 } from '@/shared-libs/api-utils'
import { createChannel, findChannels, SpaceDuplicateException } from '@/shared-libs/prisma/channel'
import { findMembershipById } from '@/shared-libs/prisma/membership'
import prisma from '@/shared-libs/prisma/prisma'
import { findUserMembershipAsAdminOrCreator } from '@/shared-libs/prisma/user-membership'
import { CHANNEL_TYPE_ENUM } from '@/shared-types/enum'
import userMiddleware from '@/src/middlewares/user'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    console.error('error', err.stack)
    console.log('error', err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(userMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { membershipId } = req.query

      if (!membershipId) {
        return status400(res, `membership_id was not provided.`)
      }

      // If the user is provided and is member of the community then return all the channels
      if (user) {
        if (typeof membershipId !== 'string') {
          return res.status(400)
        }

        const userMembership = await prisma.userMembership.findUnique({
          where: {
            user_id_membership_id: {
              user_id: user.id,
              membership_id: membershipId,
            },
            status: 'accepted',
          },
          select: { id: true },
        })

        if (userMembership) {
          const channels = await findChannels({
            where: {
              active: true,
              membership_id: membershipId as string,
            },
          })
          return status200(res, channels)
        }
      }

      // Otherwise if the community is public then return just public channels.
      const membership = await findMembershipById({
        where: {
          id: membershipId as string,
        },
        include: {
          membership_setting: true,
        },
      })
      if (!membership) {
        return status400(res, `Community not found.`)
      }

      if (membership.membership_setting.visibility === VISIBILITY_ENUM.public) {
        const publicChannels = await findChannels({
          where: {
            //active: true,
            membership_id: membershipId as string,
            //type: CHANNEL_TYPE_ENUM.livestream,
          },
        })
        return status200(res, publicChannels)
      }

      return status403(res, `You don't have the permission.`)
    } catch (err: any) {
      console.error(err)
      return res.status(500).json(errorHandler(err, 'Space'))
    }
  })
  .use(authenticationMiddleware)
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { membership_id } = req.query
      const membershipId = membership_id as string
      const { id, name, ...rest } = req.body

      const userMembership = await findUserMembershipAsAdminOrCreator(user.id, membershipId)
      if (!userMembership) {
        return res.status(403).json({ message: `You don't have permissions to update the channel.` })
      }

      const cleanedName = name?.trim()
      if (!cleanedName) {
        return res.status(400).json({ message: `Space name is required.` })
      }
      rest['name'] = cleanedName

      const channels = await findChannels({
        where: { membership_id: membershipId },
        orderBy: [{ sequence: 'desc' }],
      })

      if (channels?.docs?.length) {
        rest.sequence = channels.docs[0].sequence + 1
      } else {
        rest.sequence = 1
      }

      const createdChannel = await createChannel({
        data: {
          ...rest,
          type: CHANNEL_TYPE_ENUM.livestream,
          membership: {
            connect: {
              id: membershipId,
            },
          },
          created_by: {
            connect: {
              id: user.id,
            },
          },
        },
      })

      if (!createdChannel) {
        return res.status(500).json(errorHandler(createdChannel, 'Space'))
      }
      return res.json({ success: true, data: createdChannel })
    } catch (err: any) {
      if (err instanceof SpaceDuplicateException) {
        return res.status(409).json({
          message: 'Space with that name already exists.',
        })
      }
      return res.status(500).json(errorHandler(err, 'Space'))
    }
  })

export default handler
