import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { createEvent, findEvents } from '@memberup/shared/src/libs/prisma/event'
import { EVENT_LOCATION_TYPE_ENUM, EVENT_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { knockEvent } from '@/memberup/libs/knock'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import { parseQuery } from '@/shared-libs/api-utils'
import { isCommunityEditor } from '@/shared-libs/authorization'
import { findUserMembership } from '@/shared-libs/prisma/user-membership'

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { where, take, select, skip, orderBy } = parseQuery(req.query)
      const { membership_id } = req.query

      const membershipId = membership_id as string
      if (!membershipId) {
        return res.status(400).json({ message: `membership_id was not provided.` })
      }

      const userMembership = await findUserMembership({
        where: {
          user_id: user.id,
          membership_id: membershipId,
        },
      })

      if (!userMembership) {
        return res.status(400).json({ message: `You are not a member of the provided community.` })
      }

      const newWhere = { membership_id: membershipId, ...(where || {}) }

      const userRole = userMembership.user_role

      if (!isCommunityEditor(userRole)) {
        newWhere['status'] = { not: EVENT_STATUS_ENUM.drafts }
      }

      const result = await findEvents({
        where: newWhere,
        take,
        select,
        skip,
        orderBy,
      })
      return res.json({ success: true, data: result })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'Event'))
    }
  })
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { membership_id } = req.body

      // TODO: Validate the data field before persisting.

      // NOTE: The membership_id is already validated at hte checkCreatorRoleMiddleware
      const membershipId = membership_id as string
      const result = await createEvent({
        data: {
          ...req.body.data,
          attendees: {
            create: [
              {
                user: {
                  connect: {
                    id: user.id,
                  },
                },
              },
            ],
          },
          membership: {
            connect: {
              id: membershipId,
            },
          },
          creator: {
            connect: {
              id: user.id,
            },
          },
        },
      })

      if (result?.notify_members && result.status === EVENT_STATUS_ENUM.published) {
        const cancelationKey = `${result.id}_${result.updatedAt.toISOString()}`
        knockEvent({
          id: result.id,
          location:
            result.location_type === EVENT_LOCATION_TYPE_ENUM.tbd
              ? 'TBD'
              : result.location_type === EVENT_LOCATION_TYPE_ENUM.content_release
                ? 'Content Release'
                : result.location_address,
          start_time: result.start_time,
          title: result.title,
          time_zone: result.time_zone,
          membership_id: membershipId,
          user_id: user.id,
          cancelationKey: cancelationKey,
          updated: false,
        })
      }

      if (result?.id) {
        return res.status(201).send({ success: true, data: result })
      }
      res.status(500).json(errorHandler(result, 'Event'))
    } catch (error) {
      console.error(error)
      Sentry.captureException(error)
      return res.status(500).end()
    }
  })

export default handler
