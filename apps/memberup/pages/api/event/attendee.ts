import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findEventById } from '@memberup/shared/src/libs/prisma/event'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const user = req['user']
    const { event_id } = req.query
    const result = await findEventById({
      where: { id: event_id as string },
      include: { attendees: { where: { user_id: user.id } } },
    })
    return res.json({ success: true, data: result })
  } catch (err: any) {
    sentryCaptureException(err)
    return res.status(400).json(errorHandler(err, 'Event'))
  }
})

export default handler
