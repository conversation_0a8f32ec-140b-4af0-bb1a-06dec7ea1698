import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import {
  deleteInviteLinkById,
  findInviteLinkById,
  updateInviteLink,
} from '@memberup/shared/src/libs/prisma/invite-link'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const {
        query: { id },
      } = req
      const result = await findInviteLinkById(id as string)
      return res.json({ success: true, data: result })
    } catch (err: any) {
      res.status(400).json(errorHandler(err, 'InviteLink'))
    }
  })
  .put(async (req, res) => {
    try {
      const {
        query: { id },
        body,
      } = req
      const result = await updateInviteLink({
        where: { id: id as string },
        data: body,
      })
      return res.status(200).send({ success: true, data: result })
    } catch (err: any) {
      return res.status(400).json(errorHandler(err, 'InviteLink'))
    }
  })
  .delete(async (req, res) => {
    try {
      const { id } = req.query
      const result = await deleteInviteLinkById(id as string)
      if (result?.id) {
        return res.status(200).send({ success: true, data: result })
      }
      res.status(400).json(errorHandler(result, 'InviteLink'))
    } catch (err: any) {
      res.status(400).json(errorHandler(err, 'InviteLink'))
    }
  })

export default handler
