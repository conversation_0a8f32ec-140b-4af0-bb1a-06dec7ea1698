import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

// import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { getMuxAsset } from '@/memberup/libs/mux'

const handler = nc<NextApiRequest, NextApiResponse>()

// handler.use(authenticationMiddleware).get(async (req, res) => {
handler.get(async (req, res) => {
  try {
    const { id } = req.query
    const asset = await getMuxAsset(id as string)
    return res.status(200).send({
      success: true,
      data: asset,
    })
  } catch (err: any) {
    console.error(err)
    Sentry.captureException(err)
    return res.status(400).json(errorHandler(err, 'Mux'))
  }
})

export default handler
