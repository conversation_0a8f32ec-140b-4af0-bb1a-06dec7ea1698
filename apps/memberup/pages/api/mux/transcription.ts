import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { getFeedTracksByUserId, upsertFeedTrack } from '@memberup/shared/src/libs/prisma/feed'
import { getMuxTranscript } from '@/memberup/libs/mux'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const {
      body: { file_metadata: fileMetadata },
    } = req
    const transcript = await getMuxTranscript(fileMetadata)
    console.error('transcript', transcript)
    res.json({ success: true, data: transcript })
  } catch (err: any) {
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'Mux transcriptions'))
  }
})

export default handler
