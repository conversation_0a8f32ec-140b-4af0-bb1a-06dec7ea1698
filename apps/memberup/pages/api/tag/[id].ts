import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHand<PERSON> } from '@memberup/shared/src/libs/prisma/error-handler'
import { deleteTagById, findTagById, updateTag } from '@memberup/shared/src/libs/prisma/tag'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    const { id } = req.query
    try {
      const result = await findTagById(id as string)
      if (result?.id) {
        res.json({ success: true, data: result })
      } else {
        res.status(400).json(errorHandler(result, 'Tag'))
      }
    } catch (err: any) {
      res.status(400).json(errorHandler(err, 'Tag'))
    }
  })
  .use(checkCreatorRoleMiddleware)
  .put(async (req, res) => {
    try {
      const {
        query: { id },
        body,
      } = req
      const result = await updateTag({
        where: { id: id as string },
        data: body,
      })
      if (result?.id) {
        res.json({ success: true, data: result })
      } else {
        res.status(400).json(errorHandler(result, 'Tag'))
      }
    } catch (err: any) {
      res.status(400).json(errorHandler(err, 'Tag'))
    }
    const {
      query: { id },
      body,
    } = req
  })
  .delete(async (req, res) => {
    const {
      query: { id },
    } = req
    try {
      const { id } = req.query
      const result = await deleteTagById(id as string)
      if (result?.id) {
        res.status(200).send({ success: true, data: result })
      } else {
        res.status(400).json(errorHandler(result, 'Tag'))
      }
    } catch (err: any) {
      res.status(400).json(errorHandler(err, 'Tag'))
    }
  })

export default handler
