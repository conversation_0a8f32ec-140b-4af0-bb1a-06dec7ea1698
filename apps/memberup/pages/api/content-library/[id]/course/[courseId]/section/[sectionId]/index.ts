import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { updateCourseCompletionPercentage } from '@memberup/shared/src/libs/prisma/content-library'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { prisma } from '@/shared-libs/prisma/prisma'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .put(async (req, res) => {
    try {
      const user = req['user']
      const { sectionId } = req.query
      const { name, sequence, visibility } = req.body

      const updatedSection = await prisma.contentLibraryCourseSection.update({
        where: { id: sectionId as string },
        data: { name, sequence, visibility },
      })

      await updateCourseCompletionPercentage(updatedSection.content_library_course_id)

      res.status(200).json(updatedSection)
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'ContentLibraryCourseSection'))
    }
  })
  .delete(async (req, res) => {
    try {
      const user = req['user']
      const { courseId, sectionId, id: contentLibraryId } = req.query

      // Delete the section and its related lessons
      const deletedSection = await prisma.contentLibraryCourseSection.delete({
        where: {
          id: sectionId as string,
          content_library_course_id: courseId as string,
          content_library_id: contentLibraryId as string,
        },
        include: { ContentLibraryCourseLesson: true },
      })

      await updateCourseCompletionPercentage(courseId as string)

      res.status(200).json(deletedSection)
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'ContentLibraryCourseSection'))
    }
  })

export default handler
