import { LIBRARY_VISIBILITY_ENUM } from '@prisma/client'
import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findContentLibraryById } from '@memberup/shared/src/libs/prisma/content-library'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { prisma } from '@/shared-libs/prisma/prisma'
import { findUserMembership } from '@/shared-libs/prisma/user-membership'

const handler = nc<NextApiRequest, NextApiResponse>()

function findLastPublishedLessonId(course: any) {
  // Find the last published section that has at least one published lesson
  const lastPublishedSection = course.ContentLibraryCourseSection.findLast(
    (section: any) =>
      section.visibility === 'published' &&
      section.ContentLibraryCourseLesson.some((lesson: any) => lesson.visibility === 'published'),
  )

  if (!lastPublishedSection) {
    // Handle the case where no published section is found
    return null
  }

  // Find the last published lesson within the last published section
  const lastPublishedLessonInCourse = lastPublishedSection.ContentLibraryCourseLesson.findLast(
    (lesson: any) => lesson.visibility === 'published',
  )

  if (!lastPublishedLessonInCourse) {
    // Handle the case where no published lesson is found
    return null
  }

  // Return the last published lesson
  return lastPublishedLessonInCourse.id
}

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']

      const { id: contentLibraryId, courseId } = req.query
      const contentLibrary = await findContentLibraryById(contentLibraryId as string)
      const membershipId = contentLibrary.membership_id

      const userMembership = await findUserMembership({
        where: {
          user_id: user.id,
          membership_id: membershipId,
        },
      })

      if (!userMembership) {
        return res.status(400).json({ error: 'User is not a member of this community' })
      }

      // Fetch the course
      let course = await prisma.contentLibraryCourse.findUnique({
        where: {
          id: courseId as string,
          content_library_id: contentLibraryId as string,
          membership_id: membershipId,
        },
        include: {
          ContentLibraryCourseSection: {
            include: {
              ContentLibraryCourseLesson: true,
            },
          },
        },
      })

      if (!course) {
        res.status(404).json({ error: 'Course not found' })
        return
      }

      // Sort the sections in ascending order by their sequence numbers
      course.ContentLibraryCourseSection.sort((a, b) => a.sequence - b.sequence)

      course.ContentLibraryCourseSection.forEach((section) => {
        section.ContentLibraryCourseLesson.sort((a, b) => a.sequence - b.sequence)
      })

      /* search for the last published lesson within the last published section of the course, both sections and lessons have sequences */
      /* filter published sections that has at least 1 published lesson */
      let lastPublishedLessonInCourse = findLastPublishedLessonId(course)
      /*  const publishedSections = course.ContentLibraryCourseSection.filter((section:any) => section.visibility === 'published' && section.ContentLibraryCourseLesson.filter((lesson:any) => lesson.visibility === 'published').length)
      if(publishedSections.length){
      const lastPublishedSection = publishedSections[publishedSections.length - 1]
      const publishedLessons = lastPublishedSection.ContentLibraryCourseLesson.filter((lesson:any) => lesson.visibility === 'published')
       lastPublishedLessonInCourse = publishedLessons[publishedLessons.length - 1].id
      }  */
      // Fetch the lesson statuses
      const lessonStatuses = await prisma.contentLibraryCourseUserProgress.findUnique({
        where: {
          user_id_membership_id_content_library_course_id_content_library_id: {
            user_id: user.id,
            membership_id: membershipId,
            content_library_course_id: courseId as string,
            content_library_id: contentLibraryId as string,
          },
        },
      })

      // Set the done property for each lesson
      course.ContentLibraryCourseSection.forEach((section) => {
        section.ContentLibraryCourseLesson.forEach((lesson: any) => {
          lesson.done = lessonStatuses?.done[lesson.id] || false
        })
      })

      // Transform sections and lessons into maps
      const sectionsMap = course.ContentLibraryCourseSection.reduce((acc, section) => {
        acc[section.id] = {
          ...section,
          ContentLibraryCourseLesson: section.ContentLibraryCourseLesson.reduce((acc, lesson) => {
            acc[lesson.id] = lesson
            return acc
          }, {}),
        }
        return acc
      }, {})

      const updatedCourse = { ...course, ContentLibraryCourseSection: sectionsMap }

      // Calculate the progress
      res.status(200).json({
        ...updatedCourse,
        lesson_statuses: lessonStatuses?.done || {},
        progress: lessonStatuses?.progress_percentage || 0,
        last_lesson_id_in_course_member_view: lastPublishedLessonInCourse,
      })
    } catch (err: any) {
      console.error(err)
      Sentry.captureException(err)
      res.status(400).json(errorHandler(err, 'ContentLibraryCourse'))
    }
  })
  .delete(async (req, res) => {
    try {
      const user = req['user']

      const { id: contentLibraryId, courseId } = req.query

      // TODO: Verify that the user is an admin or owner of the community related to the content library.

      // Check if the course exists
      const course = await prisma.contentLibraryCourse.findUnique({
        where: {
          id: courseId as string,
          content_library_id: contentLibraryId as string,
        },
      })

      if (!course) {
        res.status(404).json({ error: 'Course not found' })
        return
      }

      const deletedCourse = await prisma.contentLibraryCourse.update({
        where: {
          id: courseId as string,
          content_library_id: contentLibraryId as string,
        },
        data: { visibility: 'deleted' as LIBRARY_VISIBILITY_ENUM },
      })

      // TODO: We could fetch as a related record when fetching the course
      // Fetch the ContentLibrary record
      const contentLibrary = await prisma.contentLibrary.findUnique({
        where: {
          id: contentLibraryId as string,
        },
      })
      console.log('contentLibrary', contentLibrary && typeof contentLibrary.metadata === 'object', courseId)
      if (contentLibrary && typeof contentLibrary.metadata === 'object') {
        // Cast metadata to the correct type
        const metadata = contentLibrary.metadata as { course_order: string[] }

        // Remove the course id from the course_order array
        metadata.course_order = metadata.course_order.filter((id) => id !== courseId)
        // Save the updated ContentLibrary record
        await prisma.contentLibrary.update({
          where: { id: contentLibraryId as string },
          data: { metadata },
        })
      }

      res.status(200).json(deletedCourse)
    } catch (err: any) {
      console.error(err)
      Sentry.captureException(err)
      res.status(400).json(errorHandler(err, 'ContentLibraryCourse'))
    }
  })
  .put(async (req, res) => {
    try {
      const user = req['user']

      // TODO: We should verify that the user is an admin or owner of the community related to the content library.
      /*
      select clc.*, um.user_id from content_library_courses clc
        inner join user_memberships um on clc.membership_id = um.membership_id
        where
        um.user_id = '10e33e0c-3430-41a5-a83a-2a2474e19e73' and clc.id = '4e49b3da-abe7-4f36-9e69-a8225fe9d25d'
        and um.user_role != 'member'
       */

      const { id: contentLibraryId, courseId } = req.query

      // Fetch the course
      const course = await prisma.contentLibraryCourse.findUnique({
        where: {
          id: courseId as string,
        },
      })

      if (!course) {
        res.status(404).json({ error: 'Course not found' })
        return
      }

      // Update the course
      const updatedCourse = await prisma.contentLibraryCourse.update({
        where: {
          id: courseId as string,
        },
        data: req.body, // update with the data sent in the request body
      })
      res.status(200).json(updatedCourse)
    } catch (err: any) {
      console.error(err)
      Sentry.captureException(err)
      console.error(err) // This line logs the error to the console
      res.status(400).json(errorHandler(err, 'ContentLibraryCourse'))
    }
  })
export default handler
