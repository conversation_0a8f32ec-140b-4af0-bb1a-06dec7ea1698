import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { updateCourseCompletionPercentage } from '@memberup/shared/src/libs/prisma/content-library'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { deleteRecordsFromAlgoliaIndex, uploadDataToAlgoliaIndex } from '@/shared-libs/algolia'
import { prisma } from '@/shared-libs/prisma/prisma'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})
handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const { lessonId } = req.query

      const lesson = await prisma.contentLibraryCourseLesson.findUnique({
        where: {
          id: lessonId as string,
        },
      })

      if (!lesson) {
        return res.status(404).json({ error: 'Lesson not found' })
      }

      res.status(200).json(lesson)
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'ContentLibraryCourseLesson'))
    }
  })
  .put(async (req, res) => {
    try {
      const user = req['user']
      const { courseId, lessonId } = req.query
      const {
        title,
        type,
        text,
        media_file,
        resource_files,
        sequence,
        media_file_title,
        media_file_subtitle,
        thumbnail_url,
        visibility,
        media_transcript,
      } = req.body

      const updatedLesson = await prisma.contentLibraryCourseLesson.update({
        where: {
          id: lessonId as string,
        },
        data: {
          title,
          type,
          text,
          media_file,
          resource_files,
          sequence,
          media_file_title,
          media_file_subtitle,
          thumbnail_url,
          visibility,
          media_transcript,
        },
      })

      const indexUpdateObj = { ...updatedLesson, user, thumbnail_url }
      await uploadDataToAlgoliaIndex('content-library', indexUpdateObj)

      // select all users that belong to this course in content_library_course_progress

      await updateCourseCompletionPercentage(courseId as string)

      res.status(200).json(updatedLesson)
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'ContentLibraryCourseLesson'))
    }
  })
  .delete(async (req, res) => {
    try {
      const user = req['user']
      const { lessonId } = req.query

      // Start a transaction
      await prisma.$transaction(async (prisma) => {
        // Get the lesson to be deleted
        const lessonToDelete = await prisma.contentLibraryCourseLesson.findUnique({
          where: {
            id: lessonId as string,
          },
        })

        // Delete the lesson
        await prisma.contentLibraryCourseLesson.delete({
          where: {
            id: lessonId as string,
          },
        })

        // Decrement the sequence numbers of the following lessons
        await prisma.contentLibraryCourseLesson.updateMany({
          where: {
            section_id: lessonToDelete.section_id as string,
            sequence: { gt: lessonToDelete.sequence },
          },
          data: {
            sequence: {
              decrement: 1,
            },
          },
        })
        await updateCourseCompletionPercentage(lessonToDelete.content_library_course_id, prisma)
      })
      await deleteRecordsFromAlgoliaIndex('content-library', [lessonId as string])

      res.status(200).json({ message: 'Lesson deleted successfully' })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'ContentLibraryCourseLesson'))
    }
  })

export default handler
