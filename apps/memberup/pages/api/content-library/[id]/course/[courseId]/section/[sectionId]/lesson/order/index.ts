import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { prisma } from '@/shared-libs/prisma/prisma'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).put(async (req, res) => {
  try {
    const user = req['user']
    const { newOrders } = req.body
    newOrders.forEach(async (order) => {
      await prisma.contentLibraryCourseLesson.update({
        where: {
          id: order.lessonId,
        },
        data: {
          sequence: order.sequence,
          section_id: order.sectionId,
        },
      })
    })
    res.status(200).json(newOrders)
  } catch (err: any) {
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'ContentLibraryCourseLesson'))
  }
})

export default handler
