import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findContentLibraryByMembershipId } from '@memberup/shared/src/libs/prisma/content-library'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  const user = req['user']
  const { membership_id } = req.query
  const membershipId = membership_id as string

  try {
    const contentLibrary = await findContentLibraryByMembershipId(membershipId)
    res.status(200).json({
      success: true,
      data: {
        content_library: contentLibrary,
      },
    })
  } catch (err: any) {
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'ContentLibrary'))
  }
})

export default handler
