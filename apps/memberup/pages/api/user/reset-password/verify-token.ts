import * as Sentry from '@sentry/nextjs'
import jwt from 'jsonwebtoken'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { auth } from '@/auth'
import { ResetPasswordErrorCode } from '@/shared-types/enum'

const RESET_PASSWORD_TOKEN_SECRET = process.env.RESET_PASSWORD_TOKEN_SECRET

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.post(async (req, res) => {
  const { token } = req.body
  try {
    const decodedToken = jwt.verify(token, RESET_PASSWORD_TOKEN_SECRET)
    const tokenUserId = decodedToken['user_id']

    // Check if there's a logged-in user
    const session = await auth(req, res)
    if (session?.user?.id) {
      if (session.user.id !== tokenUserId) {
        return res.status(401).json({
          error: { code: ResetPasswordErrorCode.INVALID_USER },
        })
      }
    }

    res.json({
      success: Boolean(tokenUserId),
    })
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: { code: ResetPasswordErrorCode.TOKEN_EXPIRED },
      })
    } else if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: { code: ResetPasswordErrorCode.INVALID_TOKEN },
      })
    } else {
      Sentry.captureException(error)
      res.status(500).end()
    }
  }
})

export default handler
