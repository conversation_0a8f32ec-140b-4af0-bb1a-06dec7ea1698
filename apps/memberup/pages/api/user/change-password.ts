import jwt from 'jsonwebtoken'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { comparePassword, hashPassword } from '@memberup/shared/src/libs/bcrypt'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findUserById, updateUser } from '@memberup/shared/src/libs/prisma/user'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const NEXTAUTH_SECRET = process.env.NEXTAUTH_SECRET

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { current_password, new_password } = req.body
    const dbUser = await findUserById({
      where: {
        id: user.id,
      },
    })
    const checkPassword = await comparePassword(current_password, dbUser.password)
    if (!checkPassword) {
      return res.status(400).json({
        message: 'Invalid Password.',
      })
    }
    const hashedPassword = await hashPassword(new_password)
    const result = await updateUser({
      where: { id: user.id },
      data: { password: hashedPassword },
    })
    const token = jwt.sign({ email: result.email, password: new_password, redirectTo: 'community' }, NEXTAUTH_SECRET)

    res.json({
      success: true,
      token,
    })
  } catch (err) {
    console.error(err)
    if (err.response) {
      console.error(err.response.body)
    }
    res.status(400).json(errorHandler(err, 'Email'))
  }
})

export default handler
