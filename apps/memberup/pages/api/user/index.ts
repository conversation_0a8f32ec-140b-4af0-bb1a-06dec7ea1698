import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { status200, status500 } from '@/shared-libs/api-utils'
import prisma from '@/shared-libs/prisma/prisma'
import { findUserMembership, findUserMemberships } from '@/shared-libs/prisma/user-membership'
import { USER_MEMBERSHIP_STATUS_ENUM, USER_ROLE_ENUM } from '@/shared-types/enum'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.get(async (req, res) => {
  try {
    const user = req['user']
    const { membership_id, getProgress } = req.query
    const membershipId = membership_id as string

    if (!membershipId) {
      return res.status(400).json({
        success: false,
        message: 'membership_id was not provided',
      })
    }

    let userRole: USER_ROLE_ENUM = USER_ROLE_ENUM.member
    if (user) {
      const userMembership = await findUserMembership({
        where: {
          user_id: user.id,
          membership_id: membershipId,
        },
      })
      userRole = userMembership.user_role
    }

    // NOTE: The members for a community are in user_memberships
    const result = await findUserMemberships({
      where: {
        membership_id: membershipId,
        status: {
          notIn: [USER_MEMBERSHIP_STATUS_ENUM.pending, USER_MEMBERSHIP_STATUS_ENUM.payment_pending],
        },
      },
      select: {
        status: true,
        user_role: true,
        user: {
          select: {
            id: true,
            email: userRole === USER_ROLE_ENUM.admin || userRole === USER_ROLE_ENUM.owner || undefined,
            first_name: true,
            last_name: true,
            membership_id: true,
            createdAt: true,
            invite_token: true,
            username: true,
            profile: {
              select: {
                image: true,
                image_crop_area: true,
                phone_number: true,
                getting_started: true,
                bio: true,
                location: true,
                last_activity_at: true,
              },
            },
          },
        },
      },
    })

    const users = result.map((um: any) => {
      return { ...um.user, role: um.user_role, status: um.status }
    })

    // TODO 3023: The course Progress should be pre-computed.
    /* for each user, get their courses, and progress for each course and build a response object */
    if (getProgress === 'true') {
      // Parallelize fetching courses with progress for each user
      const coursesPromises = users.map((user) =>
        prisma.contentLibraryCourse.findMany({
          where: {
            visibility: 'published',
            membership_id: membershipId as string,
          },
          select: {
            id: true,
            title: true,
            description: true,
            ContentLibraryCourseUserProgress: {
              where: {
                user_id: user.id,
              },
              select: {
                progress_percentage: true,
                // Add other fields from ContentLibraryCourseUserProgress as needed
              },
            },
          },
        }),
      )

      // Await all promises simultaneously
      const coursesResults = await Promise.all(coursesPromises)

      // Assign the fetched courses with their progress to the corresponding user in the result.docs array
      for (let i = 0; i < result.length; i++) {
        ;(result[i] as any).courses = coursesResults[i]
      }
    }

    return status200(res, {
      docs: users,
      length: users.length,
    })
  } catch (err: any) {
    Sentry.captureException(err)
    console.error('err ============', err)
    return status500(res, err.message)
  }
})

export default handler
