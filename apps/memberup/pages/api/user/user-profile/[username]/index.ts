import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findUser } from '@memberup/shared/src/libs/prisma/user'
import { userSummarizedPublicFields } from '@/lib/query-options/users'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).get(async (req, res) => {
  const summarized = Boolean(req.query.summarized)

  try {
    const { username } = req.query

    const select = summarized
      ? userSummarizedPublicFields
      : {
          id: true,
          first_name: true,
          last_name: true,
          membership_id: true,
          role: true,
          status: true,
          createdAt: true,
          profile: true,
          email: true,
          username: true,
        }

    const result = await findUser({
      select,
      where: { username: username as string },
    })

    return res.status(200).send({
      success: true,
      data: result,
    })
  } catch (err: any) {
    sentryCaptureException(err)
    console.error(err)
    return res.status(500).end()
  }
})

export default handler
