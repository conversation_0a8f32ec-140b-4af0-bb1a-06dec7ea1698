import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findUser } from '@memberup/shared/src/libs/prisma/user'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status500 } from '@/shared-libs/api-utils'
import prisma from '@/shared-libs/prisma/prisma'
import { findUserMembership } from '@/shared-libs/prisma/user-membership'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Internal server error')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const { username, membership_id: membershipId } = req.query
    const user = await findUser({
      select: {
        id: true,
      },
      where: {
        username: username as string,
      },
    })
    const userMembership = await findUserMembership({
      where: {
        user_id: user.id,
        membership_id: membershipId,
      },
    })
    if (!userMembership) {
      return status400(res, 'User is not a member of this community')
    }
    return status200(res, { user_membership: userMembership })
  } catch (err: any) {
    sentryCaptureException(err)
    console.error(err)
    return status500(res, 'Failed to fetch user membership.')
  }
})

export default handler
