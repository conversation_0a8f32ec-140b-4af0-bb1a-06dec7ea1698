import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findUser } from '@memberup/shared/src/libs/prisma/user'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import prisma from '@/shared-libs/prisma/prisma'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const { username } = req.query

    const select = {
      id: true,
    }

    const result = await findUser({
      select,
      where: { username: username as string },
    })

    const actionsPerDay = await prisma.actionsPerDay.findMany({
      select: {
        id: true,
        day: true,
        count: true,
      },
      where: {
        user_id: result.id,
        day: {
          gte: new Date(new Date().setFullYear(new Date().getFullYear() - 1)),
        },
      },
      orderBy: {
        day: 'asc',
      },
    })

    return res.status(200).send({
      success: true,
      data: actionsPerDay,
    })
  } catch (err: any) {
    console.error(err)
    Sentry.captureException(err)
    return res.status(500).end()
  }
})

export default handler
