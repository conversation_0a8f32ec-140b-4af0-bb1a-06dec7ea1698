import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { sendCancelMembershipEmail } from '@/shared-libs/user'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    if (!user.membership_id) {
      return res.status(400).json({
        message: 'Membership is not configured properly. Please contact the owner.',
      })
    }

    await sendCancelMembershipEmail(user.id)
    return res.status(200).send({ success: true })
  } catch (err: any) {
    console.log('cancelMembershipError ====', err)
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'User'))
  }
})

export default handler
