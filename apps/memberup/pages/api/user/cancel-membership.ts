import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200 } from '@/shared-libs/api-utils'
import prisma from '@/shared-libs/prisma/prisma'
import { stripeCancelSubscription } from '@/shared-libs/stripe'
import { USER_MEMBERSHIP_STATUS_ENUM } from '@/shared-types/enum'
import { TStripeConnectAccount } from '@/shared-types/types'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { membership_id: membershipId } = req.query

    const userMembership = await prisma.userMembership.findFirst({
      where: {
        user_id: user.id,
        membership_id: membershipId as string,
      },
      include: {
        membership: {
          include: {
            membership_setting: true,
          },
        },
      },
    })

    if (!userMembership) {
      return res.status(400).json({
        message: 'User is not part of the community.',
      })
    }

    const updatedUserMembership = await prisma.userMembership.update({
      where: {
        id: userMembership.id,
      },
      data: {
        status: USER_MEMBERSHIP_STATUS_ENUM.cancelled,
      },
      include: {
        membership: true,
      },
    })

    // Check if the user has an active subscription and cancel it
    if (userMembership?.stripe_subscription_id) {
      const membershipSetting = userMembership.membership?.membership_setting
      const connectedStripeAccountInfo = membershipSetting?.stripe_connect_account

      if (connectedStripeAccountInfo) {
        try {
          // @TODO: Add a subscription_id history (MEM-4133)
          await stripeCancelSubscription(
            connectedStripeAccountInfo as TStripeConnectAccount,
            userMembership.stripe_subscription_id,
            true, // cancel_at_period_end: true to keep access until period ends
          )
        } catch (error) {
          console.error(error)
          Sentry.captureException(error)
          // Continue execution - we don't want to fail the membership cancellation if Stripe fails
        }
      }
    }

    // TODO UA: Keep access to the community until the current period ends.

    return status200(res, updatedUserMembership)
  } catch (error) {
    console.error(error)
    Sentry.captureException(error)
    res.status(500).json(errorHandler(error, 'UserMembership'))
  }
})

export default handler
