import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findUserById } from '@memberup/shared/src/libs/prisma/user'
import { updateUserProfile } from '@memberup/shared/src/libs/prisma/user-profile'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { findUser } from '@/shared-libs/prisma/user'
import { findUserProfile } from '@/shared-libs/prisma/user-profile'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.get(async (req, res) => {
  try {
    const { username } = req.query
    const result = await findUser({
      select: {
        id: true,
        first_name: true,
        last_name: true,
        status: true,
        createdAt: true,
        profile: true,
      },
      where: { username: username as string },
    })
    if (!result) {
      return res.status(404).end('User not found.')
    }
    return res.status(200).send({
      success: true,
      data: result,
    })
  } catch (err: any) {
    sentryCaptureException(err)
    return res.status(500).end('Failed to get the user profile.')
  }
})

export default handler
