import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { cancelMember } from '@/shared-libs/user'

const handler = nc<NextApiRequest, NextApiResponse>({})

handler.use(authenticationMiddleware).post(async (req, res) => {
  const { id, reason } = req.body
  try {
    const result = await cancelMember(id, USER_STATUS_ENUM.banned, true, reason)
    if (result?.id) {
      return res.status(200).send({ success: true, data: result })
    }
    throw new Error(`Can not ban the user.`)
  } catch (err: any) {
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'User'))
  }
})

export default handler
