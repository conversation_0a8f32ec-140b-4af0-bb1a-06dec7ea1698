import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { comparePassword, hashPassword } from '@memberup/shared/src/libs/bcrypt'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findUser, findUserById, findUsers, updateUser } from '@memberup/shared/src/libs/prisma/user'
import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { updateMembersCountAsync } from '@/lib/server/communities'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import prisma from '@/shared-libs/prisma/prisma'
import { setupUser } from '@/shared-libs/user'
import { USER_MEMBERSHIP_STATUS_ENUM } from '@/shared-types/enum'

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { id } = req.query
      const select = {
        id: true,
        first_name: true,
        last_name: true,
        membership_id: true,
        role: true,
        status: true,
        createdAt: true,
        profile: true,
        email: true,
      }

      const result = await findUserById({
        select,
        where: { id: id as string },
      })
      return res.status(200).send({
        success: true,
        data: result,
      })
    } catch (err: any) {
      sentryCaptureException(err)
      return res.status(400).end('Failed to create the Stream User.')
    }
  })
  .put(async (req, res) => {
    try {
      const user = req['user']
      const {
        query: { id },
        body: { membership_id, password, new_password, email: inputEmail, ...data },
      } = req

      const targetUser = await findUserById({
        where: { id: id as string },
      })

      // TODO UA: user.role is not used anymore
      if ([USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner].includes(user?.role || (USER_ROLE_ENUM.member as any))) {
        if (targetUser.membership_id !== user.membership_id) {
          return res.status(400).end(`You don't have permission to update this user's information.`)
        }
      } else {
        if (user.id !== id) {
          return res.status(400).end(`You don't have permission to update other user's information.`)
        }
        if (data.role || data.status) {
          return res
            .status(400)
            .end(
              `You are trying to update data that you don't have premission. Please ask admin or creator to update those information.`,
            )
        }
      }

      if (inputEmail) {
        const email = inputEmail.toLowerCase()
        const temp = await findUsers({
          where: {
            membership_id: user.membership_id,
            email,
            id: { not: user.id },
          },
        })
        if (temp.total) {
          return res.status(400).json({
            message: 'There is already another user with the email.',
          })
        }
        data.email = email
      }

      if (new_password) {
        const checkPassword = await comparePassword(password || '', targetUser.password)
        if (!checkPassword) {
          return res.status(400).json({
            message: 'Invalid password.',
          })
        }

        const hashedPassword = await hashPassword(new_password)
        data.password = hashedPassword
      }

      if (targetUser.role === USER_ROLE_ENUM.member && data.role === USER_ROLE_ENUM.admin) {
        const owner = await findUser({
          where: {
            membership_id: user.membership_id,
            role: USER_ROLE_ENUM.owner,
          },
          include: {
            profile: true,
          },
        })
        if (owner?.profile) {
          data.profile = {
            update: {
              ...(data.profile?.update || {}),
              getting_started: owner.profile.getting_started,
            },
          }
        }
      }

      const result = await updateUser({
        where: { id: id as string },
        data: data,
        select: {
          id: true,
          first_name: true,
          last_name: true,
          email: true,
          membership_id: true,
          role: true,
          status: true,
          createdAt: true,
          profile: {
            select: {
              image: true,
              image_crop_area: true,
              phone_number: true,
            },
          },
          membership:
            data.first_name || data.last_name || data.email || data.role
              ? {
                  select: {
                    id: true,
                    active: true,
                    brand: true,
                    domain: true,
                    deactive_reason: true,
                    name: true,
                    slug: true,
                    membership_setting: true,
                  },
                }
              : undefined,
        },
      })

      if (result?.id) {
        const { profile, membership, ...userData } = result
        if (data.first_name || data.last_name || data.email || data.role) {
          await setupUser(result, membership, membership.membership_setting, false, true, true, false)
        }

        return res.status(200).send({
          success: true,
          data: userData,
        })
      } else {
        return res.status(500).json(errorHandler(result, 'User'))
      }
    } catch (err: any) {
      sentryCaptureException(err)
      return res.status(500).json(errorHandler(err, 'User'))
    }
  })
  .delete(async (req, res) => {
    const { id, ban, membership_id } = req.query
    // TODO: Verify that the requester is an admin in the community
    try {
      // Update the user membership to set the user as deleted
      const updatedUserMembership = await prisma.userMembership.update({
        where: {
          user_id_membership_id: {
            user_id: id as string,
            membership_id: membership_id as string,
          },
        },
        data: {
          status: USER_MEMBERSHIP_STATUS_ENUM.cancelled,
        },
      })
      await updateMembersCountAsync(membership_id)

      return res.json({ success: true, data: updatedUserMembership })
    } catch (err: any) {
      console.error(err)
      sentryCaptureException(err)
      return res.status(500).end()
    }
  })

// NOTE: I leave this here if we think about deleting a user globally instead of dealing with the user membership.
// .delete(async (req, res) => {
//   const { id, ban } = req.query
//   console.log('PARAMS', id, ban)
//   try {
//     const result = await cancelMember(
//       id as string,
//       ban === 'true' ? USER_STATUS_ENUM.banned : USER_STATUS_ENUM.deleted,
//       true
//     )
//     console.log('result', result)
//     if (result?.id) {
//       return res.status(200).send({ success: true, data: result })
//     }
//     throw new Error(`Can not delete the user.`)
//   } catch (err: any) {
//     sentryCaptureException(err)
//     res.status(400).json(errorHandler(err, 'User'))
//   }
// })

export default handler
