import jwt from 'jsonwebtoken'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findUserById, updateUser } from '@memberup/shared/src/libs/prisma/user'
import { setupUser } from '@memberup/shared/src/libs/user'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const RESET_PASSWORD_TOKEN_SECRET = process.env.RESET_PASSWORD_TOKEN_SECRET
const NEXTAUTH_SECRET = process.env.NEXTAUTH_SECRET

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { code } = req.body
    const dbUser = await findUserById({
      where: { id: user.id },
    })
    if (!dbUser?.reset_token) {
      return res.status(400).json({ message: 'Invalid code' })
    }

    const decodedToken = jwt.verify(dbUser.reset_token, RESET_PASSWORD_TOKEN_SECRET)
    if (!decodedToken) {
      return res.status(400).json({ message: 'The code expired.' })
    }
    if (!decodedToken['email'] || decodedToken['code'] !== code) {
      return res.status(400).json({ message: 'Invalid code' })
    }

    const result = await updateUser({
      where: { id: user.id },
      data: { email: decodedToken['email'], reset_token: null },
      select: {
        id: true,
        first_name: true,
        last_name: true,
        email: true,
        password: true,
        membership_id: true,
        role: true,
        status: true,
        createdAt: true,
        profile: {
          select: {
            image: true,
            image_crop_area: true,
            phone_number: true,
          },
        },
        membership: {
          select: {
            id: true,
            active: true,
            brand: true,
            domain: true,
            deactive_reason: true,
            name: true,
            slug: true,
            membership_setting: true,
          },
        },
      },
    })

    const { profile, membership, password, ...userData } = result
    await setupUser(result, membership, membership.membership_setting, false, true, true, false)

    const token = jwt.sign({ email: userData.email, password, redirectTo: 'community' }, NEXTAUTH_SECRET)

    res.json({
      success: true,
      data: userData,
      token,
    })
  } catch (err) {
    console.error(err)
    res.json({
      success: false,
    })
  }
})

export default handler
