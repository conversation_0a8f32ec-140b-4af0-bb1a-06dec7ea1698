import jwt from 'jsonwebtoken'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import { v4 as uuidv4 } from 'uuid'

import { comparePassword } from '@memberup/shared/src/libs/bcrypt'
import { knockBulkDeleteUsers, knockTriggerWorkflow } from '@memberup/shared/src/libs/knock'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findUserById, findUsers, updateUser } from '@memberup/shared/src/libs/prisma/user'
import { getRandomStr } from '@memberup/shared/src/libs/string-utils'
import { KNOCK_WORKFLOW_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const RESET_PASSWORD_TOKEN_SECRET = process.env.RESET_PASSWORD_TOKEN_SECRET

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { email: inputEmail, password } = req.body
    const email = inputEmail.toLowerCase()
    const temp = await findUsers({
      where: {
        membership_id: user.membership_id,
        email,
        id: { not: user.id },
      },
    })
    if (temp.total) {
      return res.status(400).json({
        message: 'There is already another user with the email.',
        code: 'EMAIL_IN_USE',
      })
    }

    const dbUser = await findUserById({
      where: {
        id: user.id,
      },
    })
    const checkPassword = await comparePassword(password, dbUser.password)
    if (!checkPassword) {
      return res.status(400).json({
        message: 'Invalid Password.',
        code: 'INVALID_PASSWORD',
      })
    }
    const code = getRandomStr(6)
    const token = jwt.sign({ email, code }, RESET_PASSWORD_TOKEN_SECRET, {
      expiresIn: 60 * 30,
    })
    await updateUser({ where: { id: user.id }, data: { reset_token: token } })

    const tempKnockUser = {
      id: uuidv4(),
      email,
      tenant_id: user.current_membership_id,
      name: user.first_name,
    }
    await knockTriggerWorkflow(user.current_membership_id, KNOCK_WORKFLOW_ENUM.reset_email, user.id, [tempKnockUser], {
      code,
    })

    await knockBulkDeleteUsers([tempKnockUser.id])

    res.json({
      success: true,
    })
  } catch (err) {
    console.error(err)
    if (err.response) {
      console.error(err.response.body)
    }
    res.status(400).json(errorHandler(err, 'Reset Email'))
  }
})

export default handler
