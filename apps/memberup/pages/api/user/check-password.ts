import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { comparePassword } from '@memberup/shared/src/libs/bcrypt'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findUser } from '@memberup/shared/src/libs/prisma/user'
import { IUser } from '@memberup/shared/src/types/interfaces'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { password } = req.body
    const dbUser: IUser = await findUser({ where: { id: user.id } })
    const checkPassword = await comparePassword(password, dbUser.password)
    return res.json({
      success: checkPassword,
    })
  } catch (err: any) {
    res.status(400).json(errorHandler(err, 'User'))
  }
})

export default handler
