import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import { prisma } from '@/shared-libs/prisma/prisma'
import { findSparkMembershipCategory } from '@/shared-libs/prisma/spark-m-category'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const membershipId = req.query.membership_id as string
      const sparkMembershipCategoryId = req.query.spark_membership_category_id as string
      if (!membershipId) {
        return res.status(400).json({ message: `membership_id was not provided.` })
      }
      const result = await prisma.sparkMembershipQuestion.findMany({
        where: {
          membership_id: membershipId,
          spark_membership_category_id: sparkMembershipCategoryId,
        },
      })
      return res.json({ success: true, data: result })
    } catch (err: any) {
      console.error(err)
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'SparkMembershipQuestion'))
    }
  })
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const membershipId = req.query.membership_id as string
      const { spark_membership_category_id, content } = req.body

      if (!content) {
        return res.status(400).json({ message: 'You must provide content for a Spark Question.' })
      }

      const category = await findSparkMembershipCategory({
        where: { id: spark_membership_category_id },
      })
      if (!category) {
        return res.status(400).json({ message: 'You must provide the Spark Category.' })
      }

      const result = await prisma.sparkMembershipQuestion.create({
        data: {
          content: content,
          spark_membership_category_id: spark_membership_category_id,
          membership_id: membershipId,
        },
      })
      return res.status(201).send({ success: true, data: result })
    } catch (err: any) {
      sentryCaptureException(err)
      return res.status(400).json(errorHandler(err, 'SparkMembershipCategory'))
    }
  })

export default handler
