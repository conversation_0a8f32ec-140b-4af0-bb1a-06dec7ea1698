import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { updateMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { findSparkMembershipCategory } from '@memberup/shared/src/libs/prisma/spark-m-category'
import { membershipQueryOptions } from '@/lib/query-options/communities'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import { status200 } from '@/shared-libs/api-utils'
import prisma from '@/shared-libs/prisma/prisma'
import { IMembership } from '@/shared-types/interfaces'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const enable = req.body['enable'] || false
      // With the passed in category, look for the m_category
      const { category_id: categoryId, m_category_id } = req.body

      const membershipId = req.query.membership_id as string
      if (!membershipId) {
        return res.status(400).json({ message: `membership_id was not provided.` })
      }

      const membership = await prisma.membership.findFirst({
        where: {
          id: membershipId,
        },
        include: {
          membership_setting: true,
        },
      })

      let selectedCategoryId = membership.membership_setting.spark_current_membership_category_id
      if (enable) {
        if (!selectedCategoryId) {
          const selectedSparkCategory = await findSparkMembershipCategory({
            where: {
              membership_id: membershipId,
            },
          })
          console.error(selectedSparkCategory)
          selectedCategoryId = selectedSparkCategory.id
        }
      }

      await updateMembershipSetting({
        where: { id: membership.membership_setting.id },
        data: {
          spark_current_membership_category_id: selectedCategoryId,
          spark_enabled: enable,
        },
      })

      const latestMembership = (await prisma.membership.findUnique({
        where: { id: membership.id },
        ...membershipQueryOptions,
      })) as IMembership

      return status200(res, latestMembership)
    } catch (err: any) {
      console.log('err =======', err)
      sentryCaptureException(err)
      return res.status(400).json(errorHandler(err, 'SparkMembershipCategory'))
    }
  })

export default handler
