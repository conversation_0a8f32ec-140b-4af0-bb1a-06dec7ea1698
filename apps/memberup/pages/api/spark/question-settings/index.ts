//@ts-nocheck
import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { parseQuery } from '@memberup/shared/src/libs/api-utils'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findSparkCategoryById } from '@memberup/shared/src/libs/prisma/spark-category'
import { findSparkCompletedQuestions } from '@memberup/shared/src/libs/prisma/spark-m-completed-question'
import { createSparkQuestion, findSparkQuestions } from '@memberup/shared/src/libs/prisma/spark-question'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import {
  createSparkQuestionSettings,
  findSparkQuestionById,
  updateSparkQuestionSettings,
  upsertSparkQuestionSettings,
} from '@/shared-libs/prisma/spark-m-question'
import { findSparkQuestionWithAppliedSettings } from '@/shared-libs/prisma/spark-question'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const membershipId = user.current_membership_id
      const { question_id: questionId, content, answer, active } = req.body
      if (!questionId) {
        return res.status(400).json({ message: 'question_id is required.' })
      }

      await createSparkQuestionSettings({
        data: {
          membership_id: membershipId,
          question_id: questionId,
          content,
          answer,
          active,
        },
      })
      const result = await findSparkQuestionWithAppliedSettings(membershipId, questionId)
      return res.status(200).send({ success: true, data: result })
    } catch (err) {
      sentryCaptureException(err)
      return res.status(500).json(errorHandler(err, 'SparkQuestionSettings'))
    }
  })

export default handler
