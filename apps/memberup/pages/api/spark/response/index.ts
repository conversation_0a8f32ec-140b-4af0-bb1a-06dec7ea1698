import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { parseQuery } from '@memberup/shared/src/libs/api-utils'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { findMembershipSparkSettings } from '@/shared-libs/prisma/spark-question'
import { createSparkResponse, findSparkResponses, getSparkCurrentStreak } from '@/shared-libs/prisma/spark-response'

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { where, take, select, skip, orderBy } = parseQuery(req.query)
      const result = await findSparkResponses({
        where: { membership_id: user.current_membership_id, ...(where || {}) },
        take,
        select,
        skip,
        orderBy: [{ createdAt: 'desc' }],
      })
      return res.json({ success: true, data: result })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'SparkResponse'))
    }
  })
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { question_id, m_question_id, content } = req.body

      const sparkSettings = await findMembershipSparkSettings(user.current_membership_id)

      let streakCount = await getSparkCurrentStreak(user.id, user.current_membership_id)
      streakCount += 1

      const channelId = sparkSettings.m_category_id

      const result = await createSparkResponse(
        {
          data: {
            content,
            membership_id: user.current_membership_id,
            question: {
              connect: {
                id: question_id,
              },
            },
            streak_count: streakCount,
          },
        },
        channelId,
      )
      return res.status(201).send({ success: true, data: result, streak: streakCount })
    } catch (err: any) {
      sentryCaptureException(err)
      return res.status(500).json(errorHandler(err, 'SparkResponse'))
    }
  })

export default handler
