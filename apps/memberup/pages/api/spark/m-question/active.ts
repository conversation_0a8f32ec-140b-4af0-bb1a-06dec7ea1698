import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status500 } from '@/shared-libs/api-utils'
import prisma from '@/shared-libs/prisma/prisma'
import { upsertSparkQuestionSettings } from '@/shared-libs/prisma/spark-m-question'
import {
  // findCurrentSparkMembershipQuestionInstance,
  findSparkQuestionWithAppliedSettings,
} from '@/shared-libs/prisma/spark-question'
import { getSparkCurrentStreak } from '@/shared-libs/prisma/spark-response'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const membershipId = user.current_membership_id
      console.log('MEMBERSHIP_ID', membershipId)

      console.log('MEMBERSHIP ID', membershipId)

      const membershipQuestion = false
      // const membershipQuestion = await findCurrentSparkMembershipQuestionInstance(membershipId)
      if (!membershipQuestion) {
        return status200(res, {})
      }

      // TODO UA: FIX THIS
      const responses = null
      const streak = await getSparkCurrentStreak(user.id, user.current_membership_id)
      return status200(res, {
        question: {
          ...membershipQuestion,
          spark_responses: responses,
        },
        streak: streak,
      })
    } catch (err: any) {
      sentryCaptureException(err)
      return status500(res, err.message)
    }
  })
  .put(async (req, res) => {
    try {
      const user = req['user']
      const membershipId = user.current_membership_id
      const { question_id: questionId, active } = req.body
      await upsertSparkQuestionSettings(questionId, membershipId, { active }, { active })
      const result = await findSparkQuestionWithAppliedSettings(membershipId, questionId)
      return res.status(200).send({ success: true, data: result })
    } catch (err: any) {
      sentryCaptureException(err)
      return res.status(400).json(errorHandler(err, 'SparkQuestion'))
    }
  })

export default handler
