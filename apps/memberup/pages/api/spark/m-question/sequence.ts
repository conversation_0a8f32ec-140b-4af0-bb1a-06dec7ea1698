import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { updateSparkMembershipCategory } from '@memberup/shared/src/libs/prisma/spark-m-category'
import { moveArrayItem } from '@/memberup/libs/utils'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import { findSparkCategoryById } from '@/shared-libs/prisma/spark-category'
import { findSparkMembershipQuestions } from '@/shared-libs/prisma/spark-question'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { category_id, question_id, old_sequence, new_sequence } = req.body

      if (!category_id || !question_id || typeof old_sequence === 'undefined' || typeof new_sequence === 'undefined') {
        return res.status(400).json({ message: 'You should provide required fields.' })
      }

      const category = await findSparkCategoryById({
        where: { id: category_id },
      })

      if (!category?.spark_m_categories?.[0]) {
        return res.status(400).json({ message: 'category_id not valid.' })
      }

      const membershipCategory = category?.['spark_m_categories'][0]
      let sequences = membershipCategory?.question_sequences
      if (!sequences || sequences.length === 0) {
        const sparkQuestions = await findSparkMembershipQuestions(user.current_membership_id, category.id)
        sequences = sparkQuestions.map((q) => q.id)
      }

      const currentIndex = sequences.findIndex((s) => s === question_id)

      moveArrayItem(sequences, currentIndex, new_sequence)

      await updateSparkMembershipCategory({
        where: { id: membershipCategory.id },
        data: {
          question_sequences: sequences,
        },
      })
      return res.status(200).send({ success: true })
    } catch (err: any) {
      sentryCaptureException(err)
      return res.status(500).json(errorHandler(err, 'SparkQuestion'))
    }
  })

export default handler
