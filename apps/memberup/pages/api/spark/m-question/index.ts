import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { createSparkMembershipCategory } from '@memberup/shared/src/libs/prisma/spark-m-category'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import { errorHandler } from '@/shared-libs/prisma/error-handler'
import { findSparkCategoryById } from '@/shared-libs/prisma/spark-category'
import { findSparkCompletedQuestions } from '@/shared-libs/prisma/spark-m-completed-question'
import { findSparkQuestionById, upsertSparkQuestionSettings } from '@/shared-libs/prisma/spark-m-question'
import {
  createSparkQuestion,
  findSparkMembershipQuestions,
  findSparkQuestionWithAppliedSettings,
} from '@/shared-libs/prisma/spark-question'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(checkCreatorRoleMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { category_id, question_type } = req.query

      const categoryId = category_id as string
      const questionType = question_type as string

      if (!categoryId) {
        res.status(400).json({ message: 'category_id is required' })
      }

      const membershipId = user.current_user_membership.membership_id

      if (questionType === 'Completed') {
        const result = await findSparkCompletedQuestions(categoryId, membershipId)
        return res.json({ success: true, data: result })
      }

      if (questionType === 'Upcoming') {
        const category = await findSparkCategoryById({
          where: { id: categoryId },
        })

        let questions = await findSparkMembershipQuestions(membershipId, categoryId)
        let defaultQuestionSequences = questions.map((q) => q.id as string)
        if (!category?.spark_m_categories?.[0]) {
          // Create the membership category to store the question ordering.
          const data = {
            membership_id: membershipId,
            category_id: categoryId,
            active: true,
            started: false,
            question_sequences: defaultQuestionSequences,
          }
          // TODO UA: Fix this
          // await createSparkMembershipCategory(
          //   {
          //     data,
          //     include: {
          //       category: true,
          //     },
          //   },
          //   user.id
          // )
        } else {
          // Sort the questions using the existing ordering
          const questionSequences = category.spark_m_categories?.[0]?.question_sequences || []
          if (questionSequences.length === questions.length) {
            questions.sort((a, b) => {
              return questionSequences.indexOf(a.id) - questionSequences.indexOf(b.id)
            })
          }
        }

        const result = {
          docs: questions,
          total: questions.length,
        }
        return res.json({ success: true, data: result })
      }

      return res.status(400).json({ message: 'Question type not valid' })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'SparkQuestion'))
    }
  })
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const membershipId = user.current_user_membership.membership_id
      const { category_id, question_id, content, answer } = req.body

      const categoryId = category_id as string
      const questionId = question_id as string

      if (!categoryId) {
        return res.status(400).json({ message: 'category_id is required.' })
      }

      const category = await findSparkCategoryById({
        where: { id: categoryId },
      })

      if (!category) {
        return res.status(400).json({ message: 'Invalid category_id.' })
      }

      if (questionId) {
        // Create or update question settings for an existing question.
        const question = findSparkQuestionById(questionId)
        if (!question) {
          return res.status(400).json({ message: 'Invalid question_id.' })
        }
        await upsertSparkQuestionSettings(questionId, membershipId, { content, answer }, { content, answer })
        const result = await findSparkQuestionWithAppliedSettings(membershipId, questionId)
        return res.status(200).send({ success: true, data: result })
      } else {
        // Create a new question for the user membership
        if (!content) {
          return res.status(400).json({ message: 'content is required.' })
        }
        const result = await createSparkQuestion({
          data: {
            category: {
              connect: {
                id: categoryId,
              },
            },
            membership_id: membershipId,
            content: content,
            answer: answer,
          },
        })

        if (category.spark_m_categories?.[0].question_sequences) {
          const questionSequences = category.spark_m_categories?.[0].question_sequences
          if (questionSequences.length > 0) {
            questionSequences.push(result.id)
          }
        }
        return res.status(201).send({ success: true, data: result })
      }
    } catch (err: any) {
      sentryCaptureException(err)
      return res.status(400).json(errorHandler(err, 'SparkQuestion'))
    }
  })

export default handler
