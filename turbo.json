{"pipeline": {"build": {"outputs": ["dist/**", ".next/**"], "dependsOn": ["^build"], "env": ["NEXTAUTH_SECRET", "BUILD_ID", "SIGNUP_BUILD_ID", "DATABASE_URL", "GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET", "FACEBOOK_CLIENT_ID", "GET_STREAM_APP_SECRET", "STRIPE_APPLICATION_FEE_BASIC", "STRIPE_APPLICATION_FEE_PRO", "STRIPE_APPLICATION_FEE_ENTERPRISE", "STRIPE_PRODUCT_ID_INSPIRED", "STRIPE_PRODUCT_ID_INMOTION", "STRIPE_PRODUCT_ID_INFINITE", "STRIPE_PRODUCT_ID_INSPIRED_TEST", "STRIPE_PRODUCT_ID_INMOTION_TEST", "STRIPE_PRODUCT_ID_INFINITE_TEST", "STRIPE_SECRET_KEY", "STRIPE_SECRET_KEY_TEST", "STRIPE_WEBHOOK_MEMBERUP_SECRET", "STRIPE_WEBHOOK_MEMBERUP_SECRET_TEST", "STRIPE_WEBHOOK_MEMBERSHIP_SECRET", "STRIPE_WEBHOOK_MEMBERSHIP_SECRET_TEST", "STRIPE_INTERNAL_DISCOUNT_ID", "CLOUD_API_SECRET", "VERCEL_PROJECT_ID", "VERCEL_TEAM_ID", "VERCEL_MEMBERUP_TOKEN", "CYPRESS_RECORD_KEY_MEMBERUP", "CYPRESS_RECORD_KEY_MEMBERUP_SIGNUP", "CYPRESS_PROJECT_ID_MEMBERUP", "CYPRESS_PROJECT_ID_MEMBERUP_SIGNUP", "SPARK_CRON_SECRET_KEY", "MERGENT_API_KEY", "GEEKFLARE_API_KEY"]}, "build-instrumented": {"outputs": ["dist/**", ".next/**"], "dependsOn": ["^build"], "env": ["NEXTAUTH_SECRET", "BUILD_ID", "SIGNUP_BUILD_ID", "DATABASE_URL", "GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET", "FACEBOOK_CLIENT_ID", "GET_STREAM_APP_SECRET", "STRIPE_APPLICATION_FEE_BASIC", "STRIPE_APPLICATION_FEE_PRO", "STRIPE_APPLICATION_FEE_ENTERPRISE", "STRIPE_PRODUCT_ID_INSPIRED", "STRIPE_PRODUCT_ID_INMOTION", "STRIPE_PRODUCT_ID_INFINITE", "STRIPE_PRODUCT_ID_INSPIRED_TEST", "STRIPE_PRODUCT_ID_INMOTION_TEST", "STRIPE_PRODUCT_ID_INFINITE_TEST", "STRIPE_SECRET_KEY", "STRIPE_SECRET_KEY_TEST", "STRIPE_WEBHOOK_MEMBERUP_SECRET", "STRIPE_WEBHOOK_MEMBERUP_SECRET_TEST", "STRIPE_WEBHOOK_MEMBERSHIP_SECRET", "STRIPE_WEBHOOK_MEMBERSHIP_SECRET_TEST", "STRIPE_INTERNAL_DISCOUNT_ID", "CLOUD_API_SECRET", "VERCEL_PROJECT_ID", "VERCEL_TEAM_ID", "VERCEL_MEMBERUP_TOKEN", "CYPRESS_RECORD_KEY_MEMBERUP", "CYPRESS_RECORD_KEY_MEMBERUP_SIGNUP", "CYPRESS_PROJECT_ID_MEMBERUP", "CYPRESS_PROJECT_ID_MEMBERUP_SIGNUP", "SPARK_CRON_SECRET_KEY", "GEEKFLARE_API_KEY"]}, "lint": {"outputs": []}, "lint:fix": {"outputs": []}, "ts-lint": {"outputs": []}, "dev": {"cache": false}, "analyze": {"cache": false}, "clean": {"cache": false}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "seed": {"cache": false}, "stream-chat-seed": {"cache": false}, "dev:instrumented": {"cache": false}, "test": {"cache": false}, "test:memberup": {"cache": false}, "test:memberup-signup": {"cache": false}, "test:non-instrumented": {"cache": false}, "test:memberup-non-instrumented": {"cache": false}, "test:memberup-signup-non-instrumented": {"cache": false}, "test-open": {"cache": false}, "test-open:memberup": {"cache": false}, "test-open:memberup-signup": {"cache": false}, "test-jest:ci": {"cache": false}, "storybook": {"cache": false}}, "globalDependencies": [".env"]}