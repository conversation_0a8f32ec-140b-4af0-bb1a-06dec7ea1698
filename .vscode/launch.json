{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "yarn run dev"}, {"name": "Next.js: debug client-side", "type": "pwa-chrome", "request": "launch", "url": "http://localhost:3000, http://localhost:3001"}, {"name": "Next.js: debug full stack", "type": "node-terminal", "request": "launch", "command": "doppler run yarn run dev", "console": "integratedTerminal", "serverReadyAction": {"pattern": "started server on .+, url: (https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}}], "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}