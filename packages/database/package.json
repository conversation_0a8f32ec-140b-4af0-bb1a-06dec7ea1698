{"name": "@memberup/database", "version": "0.0.0", "scripts": {"db:status": "prisma migrate status", "db:generate": "prisma generate", "db:push": "prisma db push --skip-generate"}, "main": "./index.ts", "types": "./index.ts", "dependencies": {"@planetscale/database": "^1.19.0", "@prisma/adapter-planetscale": "^5.20.0", "@prisma/client": "5.20.0", "prisma": "5.20.0"}, "devDependencies": {"prisma": "5.20.0"}}