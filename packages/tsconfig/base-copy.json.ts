const test = {
  $schema: 'https://json.schemastore.org/tsconfig',
  display: 'Default',
  compilerOptions: {
    // "composite": false,
    // "declaration": true,
    // "declarationMap": true,
    // "inlineSources": false,
    // "noUnusedLocals": false,
    // "noUnusedParameters": false,
    // "preserveWatchOutput": true,

    // "target": "es5",
    // "lib": ["dom", "dom.iterable", "esnext"],
    // "allowJs": true,
    // "noEmit": true,
    // "module": "esnext",
    // "resolveJsonModule": true,
    // "incremental": true,
    // "jsx": "preserve",

    esModuleInterop: true,
    forceConsistentCasingInFileNames: true,
    moduleResolution: 'node',
    isolatedModules: true,
    skipLibCheck: true,
    strict: false,
    paths: {
      '@/memberup/components/*': ['./packages/memberup/src/components/*'],
      '@/memberup/layouts/*': ['./packages/memberup/src/layouts/*'],
      '@/memberup/libs/*': ['./packages/memberup/src/libs/*'],
      '@/memberup/pages/*': ['./packages/memberup/pages/*'],
      '@/memberup/middlewares/*': ['./packages/memberup/src/middlewares/*'],
      '@/memberup/settings/*': ['./packages/memberup/src/settings/*'],
      '@/memberup/store/*': ['./packages/memberup/src/store/*'],
      '@/memberup/test/*': ['./packages/memberup/__test__/*'],
      '@/memberup-signup/config/*': ['./packages/memberup-signup_old/src/config/*'],
      '@/memberup-signup/components/*': ['./packages/memberup-signup_old/src/components/*'],
      '@/memberup-signup/layouts/*': ['./packages/memberup-signup_old/src/layouts/*'],
      '@/memberup-signup/middlewares/*': ['./packages/memberup-signup_old/src/middlewares/*'],
      '@/memberup-signup/settings/*': ['./packages/memberup-signup_old/settings/*'],
      '@/memberup-signup/shared-components/*': ['./packages/shared/src/components/*'],
      '@/shared-config/*': ['./packages/shared/src/config/*'],
      '@/shared-libs/*': ['./packages/shared/src/libs/*'],
      '@/shared-models/*': ['./packages/shared/src/models/*'],
      '@/shared-services/*': ['./packages/shared/src/services/*'],
      '@/shared-settings/*': ['./packages/shared/src/settings/*'],
      '@/shared-styles/*': ['./packages/shared/src/styles/*'],
      '@/shared-types/*': ['./packages/shared/src/types/*'],
    },
  },
  exclude: ['node_modules'],
}
