import { Theme } from '@mui/material/styles'
import useTheme from '@mui/material/styles/useTheme'
import { makeStyles } from '@mui/styles'
import { useMemo } from 'react'

const useStyles = (props) =>
  makeStyles((theme: Theme) => ({
    cardElementWrapper: {
      padding: props.size === 'small' ? '8.5px 14px' : '16px',
      backgroundColor:
        props.variant === 'outlined'
          ? 'none'
          : theme.components.MuiCssBaseline.styleOverrides['body']['& .background-color07'][
              'backgroundColor'
            ],
      border: props.variant === 'outlined' ? '1px solid' : 'none',
      borderRadius: props.size === 'small' ? '4px' : '8px',
      borderColor: theme.palette.text.disabled,
      '&:hover, &:focus': {
        borderColor: theme.palette.text.primary,
      },
    },
  }))

export const useStripeElementStyle = (props: {
  variant: 'contained' | 'outlined'
  size: 'small' | 'medium' | 'large'
}) => {
  const theme: Theme = useTheme()
  const classes = useStyles(props)()
  const cardElementStyle = useMemo(
    () => ({
      base: {
        color: theme.palette.text.primary,
        ':-webkit-autofill': {
          color: theme.palette.text.primary,
        },
      },
    }),
    [theme]
  )

  return { cardElementWrapperClass: classes.cardElementWrapper, cardElementStyle }
}
