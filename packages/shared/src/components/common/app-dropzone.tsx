// TODO: We also use an app dropzone in create-post.tsx, so can we reuse to consolidate?
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { Accept, useDropzone } from 'react-dropzone'

import { getFileType } from '../../libs/file'
import { useMounted } from '../hooks/use-mounted'
import { AppImg } from './media/image'
import SVGPhoto from '@/shared-components/svgs/photo'
import { showToast } from '@/shared-libs/toast'
import { TAppCropArea } from '@/shared-types/types'

const IMG_FILE_MAX_SIZE = parseInt(process.env.NEXT_PUBLIC_IMG_FILE_MAX_SIZE)
const VIDEO_FILE_MAX_SIZE = parseInt(process.env.NEXT_PUBLIC_VIDEO_FILE_MAX_SIZE)

export const AppDropzone: React.FC<{
  file: File
  accept?: Accept
  cropArea?: TAppCropArea
  cropAreaAspect?: number
  defaultImageUrl?: string
  defaultImageUrlOnly?: boolean
  height?: number
  maxSize?: number
  placeholderHeight?: number
  noLimit?: boolean
  placeholder?: React.ReactNode
  visibleReplaceButton?: boolean
  visibleRemoveButton?: boolean
  width?: number
  imageWrapperStyle?: any
  imageStyle?: any
  disabled?: boolean
  replaceTrigger?: boolean
  onDropFile: (file: any) => void
  onRemove?: () => void
  hideImage?: boolean
}> = ({
  file,
  accept,
  cropArea,
  cropAreaAspect,
  defaultImageUrl,
  placeholderHeight,
  defaultImageUrlOnly,
  height,
  maxSize = IMG_FILE_MAX_SIZE,
  noLimit,
  placeholder,
  visibleReplaceButton = false,
  visibleRemoveButton = false,
  width,
  imageWrapperStyle = {},
  imageStyle,
  disabled = false,
  replaceTrigger = false,
  onDropFile,
  onRemove,
  hideImage = false,
}) => {
  const fileInputRef = useRef(null)
  const mountedRef = useMounted(true)
  const [imageUrl, setImageUrl] = useState<string>(null)
  const handleDropFile = (acceptedFiles) => {
    const acceptedFile = acceptedFiles?.[0]
    if (!acceptedFile) return
    const fileType = getFileType(acceptedFile)

    if (!noLimit && acceptedFile.size > maxSize) {
      showToast(
        fileType === 'video'
          ? `Video file size needs to be ${VIDEO_FILE_MAX_SIZE / 1024 / 1024 / 1024} GB or less.`
          : `${fileType === 'image' ? 'Image file' : 'File'} size needs to be ${
              IMG_FILE_MAX_SIZE / 1024 / 1024
            } MB or less.`,
        'error',
      )
      return
    }

    onDropFile(acceptedFile)
  }
  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDrop: handleDropFile,
    maxFiles: 1,
    accept,
    disabled,
    // accept: accept || '.doc, .docx, .pdf, .ppt, .key, video/*',
  })

  useEffect(() => {
    if (!mountedRef.current) return
    if (!defaultImageUrlOnly && file?.type?.indexOf('image') >= 0) {
      setImageUrl(URL.createObjectURL(file))
    } else if (!defaultImageUrlOnly && file?.type === 'text/csv') {
      setImageUrl(URL.createObjectURL(file))
    } else if (!defaultImageUrlOnly && (file as any)?.includes('http')) {
      setImageUrl(file as any)
    } else {
      setImageUrl(defaultImageUrl || '')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultImageUrl, file])

  useEffect(() => {
    if (!isDragActive) {
      // Perform actions to revert to the default state
    }
  }, [isDragActive])

  useEffect(() => {
    if (!replaceTrigger || !open) return

    open()
  }, [replaceTrigger])

  const rootProps = useMemo(() => getRootProps?.(), [getRootProps])
  const inputProps = useMemo(() => getInputProps?.(), [getInputProps])

  return (
    <Box
      className="d-flex align-center justify-center"
      sx={{
        position: 'relative',
        color: 'inherit',
        width: '100%',
        height: '100%',
      }}
      {...rootProps}
    >
      {Boolean(imageUrl) && !hideImage && (
        <Box
          className="d-flex align-center justify-center"
          sx={{ position: 'absolute', left: 0, top: 0, height: '100%' }}
        >
          <Box
            className="app-image-wrapper"
            sx={{
              position: 'relative',
              width: '100%',
              height: '100%',
              maxWidth: width || undefined,
              maxHeight: height || undefined,
              overflow: 'hidden',
              textAlign: 'center',
              ...imageWrapperStyle,
            }}
          >
            <AppImg
              src={imageUrl}
              cropArea={cropArea}
              cropAreaAspect={cropAreaAspect || 3.65}
              height={height || 52}
              width={width || 208}
              style={{ ...imageStyle, display: 'inline-block' }}
              alt="dropzone"
            />
          </Box>
        </Box>
      )}

      <input ref={fileInputRef} {...inputProps} />
      {Boolean(file) ? (
        <>
          {/* <Typography variant="body1" color="primary">
            <b>Selected file: {file.name}</b>
          </Typography> */}
        </>
      ) : (
        <>
          {isDragActive ? (
            <Typography variant="body1" style={{ zIndex: 1, height: placeholderHeight ? placeholderHeight : '100%' }}>
              Drop the file here ...
            </Typography>
          ) : (
            <>
              {placeholder !== undefined ? (
                placeholder
              ) : (
                <Typography variant="body1" style={{ zIndex: 1 }}>
                  Drag &amp; drop some file here, or click to select file.
                </Typography>
              )}
            </>
          )}
        </>
      )}
      {(visibleReplaceButton || visibleRemoveButton) && (
        <Box sx={{ position: 'absolute', right: '5px', top: '5px', zIndex: 1 }}>
          {visibleReplaceButton && (
            <Button
              className="round-small"
              color="info"
              variant="outlined"
              size="small"
              sx={{ color: 'white', borderColor: 'white !important', fontSize: '11px' }}
              startIcon={<SVGPhoto width={12} height={12} />}
            >
              Replace
            </Button>
          )}
          {visibleRemoveButton && (
            <Button
              color="error"
              variant="outlined"
              size="small"
              sx={{ borderRadius: '16px', p: 1, marginLeft: 0, minWidth: 0, width: 32, height: 32 }}
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                setImageUrl(defaultImageUrl || '')
                onRemove()
              }}
            >
              <DeleteOutlineOutlinedIcon />
            </Button>
          )}
        </Box>
      )}
    </Box>
  )
}
