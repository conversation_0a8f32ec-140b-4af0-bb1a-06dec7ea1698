/* eslint-disable @next/next/no-img-element */
import Box from '@mui/material/Box'
import Image from 'next/image'
import React, { useMemo } from 'react'

import { mapCroppedAreaToStyle } from '@/shared-libs/image'
import { ICloudinaryImage } from '@/shared-types/interfaces'
import { TAppCropArea } from '@/shared-types/types'

export const AppImg: React.FC<{
  src?: string | ICloudinaryImage
  alt?: string
  className?: any
  cropArea?: TAppCropArea
  cropAreaAspect?: number
  fill?: boolean
  sizes?: string
  style?: any
  id?: string
  width?: number
  height?: number
  priority?: boolean
  quality?: number
}> = ({ alt, className, cropArea, cropAreaAspect, fill, width, height, priority, quality, sizes, src, style, id }) => {
  const styles = useMemo(() => {
    if (cropArea && cropAreaAspect) {
      return mapCroppedAreaToStyle(cropArea, cropAreaAspect)
    }
    return null
  }, [cropArea, cropAreaAspect])

  const newUrl = useMemo(() => {
    let tempUrl = src as string
    if (!tempUrl || tempUrl === 'null' || tempUrl === 'undefined') return null
    //This is Cloudinary optimization after the image is uploaded and lazyloaded to screen. If this is slow do it in cloudinary.api.ts upon image upload
    if (width) {
      return tempUrl.replace('/image/upload/', `/image/upload/w_${width * 2},f_auto,q_80/`)
    }

    return tempUrl.replace('/image/upload/', `/image/upload/f_auto,q_70/`)
  }, [src])

  if (styles) {
    return (
      <Box
        style={{
          position: 'relative',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          ...(styles?.containerStyle || {}),
        }}
      >
        {Boolean(newUrl) && (
          <img
            alt={alt || ''}
            className={className}
            src={newUrl}
            style={{
              ...(styles?.imageStyle || {}),
              position: 'absolute',
              top: 0,
              left: 0,
              transformOrigin: 'top left',
              ...(style || {}),
              width: style?.width || width || '100%',
              maxWidth: '100%',
            }}
          />
        )}
      </Box>
    )
  }

  return newUrl ? (
    <Image
      alt={alt || ''}
      id={id}
      className={className}
      fill={fill || undefined}
      priority={priority || undefined}
      sizes={sizes}
      src={newUrl}
      style={style}
      width={width}
      height={height}
      quality={quality}
    />
  ) : null
}

AppImg.displayName = 'AppImg'
