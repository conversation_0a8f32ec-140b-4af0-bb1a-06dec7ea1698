import AddIcon from '@mui/icons-material/Add'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Chip from '@mui/material/Chip'
import CircularProgress from '@mui/material/CircularProgress'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import InputAdornment from '@mui/material/InputAdornment'
import Skeleton from '@mui/material/Skeleton'
import Stack from '@mui/material/Stack'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import { Theme } from '@mui/material/styles'
import { makeStyles } from '@mui/styles'
import { Elements, PaymentElement, useElements, useStripe } from '@stripe/react-stripe-js'
import { loadStripe } from '@stripe/stripe-js'
import clsx from 'clsx'
import React, { useEffect, useRef, useState } from 'react'
import { toast } from 'react-toastify'
import { useClickAway } from 'react-use'
import { STRIPE_PUBLISH_KEY } from '../../config/envs'
import { numberToCurrency } from '../../libs/numeric-utils'
import {
  createStripeSetupIntentApi,
  createStripeSubscriptionApi,
  getStripeUpcomingInvoiceApi,
} from '../../services/apis/stripe.api'
import { MEMBERUP_PLANS } from '../../settings/plans'
import { RECURRING_INTERVAL_ENUM, THEME_MODE_ENUM } from '../../types/enum'
import { showToast } from '@/shared-libs/toast'

const useStyles = makeStyles((theme: Theme) => ({
  root: {
    width: '100%',
    maxWidth: 1136,
    margin: 'auto',
    '& .MuiInputBase-root': {
      color: 'inherit',
    },
    '& .MuiInputAdornment-root': {
      color: 'inherit',
    },
    '& .MuiOutlinedInput-root': {
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none',
        borderRadius: 4,
        backgroundColor:
          theme.components.MuiCssBaseline.styleOverrides['body']['& .background-color07'][
            'backgroundColor'
          ],
      },
      '&.Mui-focused': {
        '& .MuiOutlinedInput-notchedOutline': {
          border: 'none',
          borderRadius: 4,
          backgroundColor:
            theme.components.MuiCssBaseline.styleOverrides['body']['& .background-color07'][
              'backgroundColor'
            ],
        },
      },
    },
    '& .MuiFormHelperText-root.Mui-error': {
      fontSize: 14,
    },
  },
  title: {
    letterSpacing: 'normal',
    marginBottom: 40,
  },
  description: {
    color: theme.palette.background.paper,
    fontSize: 11,
  },
  label: {
    fontSize: 12,
    marginTop: 6,
    marginBottom: 8,
  },
  button: {
    position: 'relative',
    padding: 8,
    minHeight: 48,
  },
  checkoutWrapper: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  formControlLabel: {
    paddingTop: 14,
    paddingBottom: 14,
    paddingRight: 14,
    borderRadius: 12,
    borderWidth: 1,
    borderStyle: 'solid',
    marginBottom: 8,
    marginLeft: 0,
    marginRight: 0,
    backgroundColor: theme.palette.action.active,
    borderColor: theme.palette.text.disabled,
    '&.active': {
      backgroundColor: theme.palette.action.active,
      borderColor: theme.palette.primary.main,
    },
  },
  membershipPrice: {
    fontSize: 16,
  },
}))

const stripePromise = loadStripe(STRIPE_PUBLISH_KEY)

const BillingDetailsForm = (props: { onSuccess: (methodId: string) => void }) => {
  const { onSuccess } = props
  const classes = useStyles()
  const stripe = useStripe()
  const elements = useElements()
  const [loading, setLoading] = useState(false)

  const handleFormSubmit = async (e: React.FormEvent) => {
    try {
      e.preventDefault()
      setLoading(true)

      const { error, setupIntent } = await stripe.confirmSetup({
        elements,
        redirect: 'if_required',
      })

      if (error) {
        showToast(error.message || 'An unexpected error occurred.', 'warning')
        setLoading(false)
      } else {
        onSuccess(setupIntent.payment_method as string)
      }
    } catch (err: any) {
      console.log(err.message)
      setLoading(false)
      if (err?.message) {
        showToast(err.message, 'error')
      }
    }
  }

  return (
    <form autoComplete="off" onSubmit={handleFormSubmit}>
      <Box sx={{ padding: 2, maxWidth: 454, margin: 'auto' }}>
        <Grid container spacing={5}>
          <Grid item xs={12} sx={{ mb: 3 }}>
            <PaymentElement />
          </Grid>
          <Grid item xs={12}>
            <Button
              className={clsx(classes.button, 'round-small')}
              variant="contained"
              color="primary"
              type="submit"
              fullWidth
              disabled={loading || !stripe || !elements}
            >
              {loading ? <CircularProgress size={16} /> : 'Confirm and Pay'}
            </Button>
          </Grid>
        </Grid>
      </Box>
    </form>
  )
}

export const BillingDetails = (props: {
  membershipSlug?: string
  title: string
  description: string
  planName: string
  recurringInterval: RECURRING_INTERVAL_ENUM
  shouldRedirect?: boolean
  theme?: THEME_MODE_ENUM
  onSuccess?: () => void
  onError?: (e: string) => void
}) => {
  const {
    membershipSlug,
    title,
    description,
    planName,
    recurringInterval,
    shouldRedirect,
    theme,
    onSuccess,
    onError,
  } = props
  const classes = useStyles()
  const plan = MEMBERUP_PLANS.find((p) => p.name === planName)
  const [subscriptionIntentClientSecret, setSubscriptionIntentClientSecret] = useState('')
  const [billingDetails, setBillingDetails] = useState({ amountDue: 0, subTotal: 0, total: 0 })
  const [loadingDetails, setLoadingDetails] = useState(false)
  const [coupon, setCoupon] = useState('')
  const [couponInvalid, setCouponInvalid] = useState(false)
  const [activeCoupon, setActiveCoupon] = useState(false)
  const [couponDetails, setCouponDetails] = useState({
    name: '',
    percent_off: 0,
    amount_off: 0,
    amount_discount: 0,
  })
  const inputRef = useRef<HTMLInputElement>(null)

  useClickAway(inputRef, () => {
    setActiveCoupon(false)
  })

  const setupPaymentIntent = async () => {
    try {
      setSubscriptionIntentClientSecret('')
      const res = await createStripeSetupIntentApi(false, {
        slug: membershipSlug,
        payment_method_types: ['card'],
      })
      setSubscriptionIntentClientSecret(res.data.data['client_secret'])
    } catch (err) {
      console.log('err =======', err.response?.message)
    }
  }

  useEffect(() => {
    setupPaymentIntent()
  }, [])

  const handleChangeCoupon = (evt: React.ChangeEvent<HTMLInputElement>) => {
    setCoupon(evt.target.value)
  }

  const validateCoupon = async () => {
    try {
      setCouponInvalid(false)
      setLoadingDetails(true)
      if (coupon) {
        const res = await getStripeUpcomingInvoiceApi(false, {
          plan: planName,
          interval: recurringInterval,
          coupon,
          membership_slug: membershipSlug,
        })

        setBillingDetails({
          amountDue: (res.data.data['amount_due'] || 0) / 100,
          subTotal: (res.data.data['subtotal'] || 0) / 100,
          total: (res.data.data['total'] || 0) / 100,
        })

        const discountDetails = res.data.data['discount']

        if (discountDetails) {
          setCouponDetails({
            name: discountDetails.coupon.name,
            percent_off: discountDetails.coupon.percent_off,
            amount_off: discountDetails.coupon.amount_off,
            amount_discount: res.data.data['lines'].data?.[0].discount_amounts[0].amount,
          })
        }
      }
    } catch (err: any) {
      setCouponInvalid(true)
    } finally {
      setLoadingDetails(false)
    }
  }

  const handleAddCoupon = () => {
    setActiveCoupon(true)
  }

  const handleSuccessFormSubmission = async (pmId: string) => {
    try {
      await createStripeSubscriptionApi(false, {
        membership_slug: membershipSlug,
        plan: planName,
        collection_method: 'charge_automatically',
        stripe_enable_annual: recurringInterval === RECURRING_INTERVAL_ENUM.year,
        default_payment_method: pmId,
        coupon: couponDetails.name ? coupon : null,
      })

      if (shouldRedirect) {
        window.location.replace(window.location.origin + '/billing-confirmed')
      } else {
        onSuccess()
      }
    } catch (err) {
      console.log('Error creating payment intent', err.response.data.message)
      showToast('Failed payment method', 'error')
      setupPaymentIntent()
    }
  }

  const price =
    recurringInterval === RECURRING_INTERVAL_ENUM.month ? plan.monthlyPrice : plan.annualPrice

  return (
    <Box className={classes.root} sx={{ padding: 3 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography variant="h3" align="center" gutterBottom>
            {title}
          </Typography>
        </Grid>
        <Grid item xs={12}>
          <Typography color="text.disabled" variant="body1" align="center" gutterBottom>
            {description}
          </Typography>
          <br />
        </Grid>
        <Grid item xs={12}>
          <Grid container spacing={4}>
            <Grid item xs={12} sm={6}>
              <Box sx={{ maxWidth: 454, margin: 'auto', padding: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="h5" gutterBottom>
                      Summary
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Box
                      className="background-color07"
                      sx={{ borderRadius: 2, padding: 4, maxWidth: 454 }}
                    >
                      <Grid container>
                        <Grid item xs={12}>
                          <Typography color="text.disabled" variant="body1" align="right">
                            Secured with SSL
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <Typography variant="body1" gutterBottom>
                            Membership Plan
                          </Typography>
                          <Typography variant="h6">
                            {numberToCurrency(price)}/{recurringInterval}
                          </Typography>
                          <br />
                        </Grid>
                        <Grid item xs={12}>
                          <br />
                          <Divider />
                          <br />
                        </Grid>
                        <Grid item xs={12}>
                          <Typography variant="body1" gutterBottom>
                            Subtotal
                          </Typography>
                          <Typography variant="h6">
                            {numberToCurrency(couponDetails.name ? billingDetails.subTotal : price)}
                          </Typography>
                          <br />
                        </Grid>
                        <Grid item xs={12}>
                          <br />
                          <Divider />
                          <br />
                        </Grid>
                        <Grid item xs={12} sx={{ position: 'relative' }}>
                          {couponDetails.name ? (
                            <Stack
                              direction="row"
                              alignItems="center"
                              justifyContent="space-between"
                            >
                              <Stack>
                                <Chip label={couponDetails.name} />
                                <Typography variant="caption">
                                  {couponDetails.percent_off
                                    ? `${couponDetails.percent_off}%`
                                    : couponDetails.amount_off &&
                                      numberToCurrency(couponDetails.amount_off / 100)}
                                  &nbsp;Off
                                </Typography>
                              </Stack>
                              <Typography variant="body1">
                                {numberToCurrency(couponDetails.amount_discount / 100)}
                              </Typography>
                            </Stack>
                          ) : (
                            <>
                              {activeCoupon ? (
                                <TextField
                                  size="small"
                                  variant="outlined"
                                  value={coupon}
                                  fullWidth
                                  onChange={handleChangeCoupon}
                                  error={couponInvalid}
                                  helperText={couponInvalid && 'The code is invalid'}
                                  ref={inputRef}
                                  InputProps={{
                                    endAdornment: (
                                      <InputAdornment position="end">
                                        <Button
                                          type="button"
                                          onClick={validateCoupon}
                                          disabled={loadingDetails}
                                        >
                                          Apply
                                        </Button>
                                      </InputAdornment>
                                    ),
                                    sx: {
                                      paddingRight: 0,
                                      width: activeCoupon ? '100%' : '40%',
                                    },
                                  }}
                                />
                              ) : (
                                <Button
                                  sx={{
                                    py: 1.5,
                                    px: 3,
                                  }}
                                  startIcon={<AddIcon />}
                                  onClick={handleAddCoupon}
                                >
                                  Coupon
                                </Button>
                              )}
                            </>
                          )}
                        </Grid>
                        <Grid item xs={12}>
                          <br />
                          <Divider />
                          <br />
                        </Grid>
                        <Grid item xs={12}>
                          <Typography variant="body1" gutterBottom>
                            Total Billed Today
                          </Typography>

                          {!loadingDetails ? (
                            <Typography variant="h4">
                              {numberToCurrency(couponDetails.name ? billingDetails.total : price)}
                            </Typography>
                          ) : (
                            <Skeleton height={40} sx={{ width: '100%' }} />
                          )}
                        </Grid>
                      </Grid>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6}>
              {Boolean(stripePromise) && Boolean(plan) && !!subscriptionIntentClientSecret ? (
                <Elements
                  stripe={stripePromise}
                  options={{
                    clientSecret: subscriptionIntentClientSecret,
                    appearance: {
                      theme: theme === THEME_MODE_ENUM.dark ? 'night' : 'stripe',
                    },
                    loader: 'always',
                  }}
                >
                  <BillingDetailsForm onSuccess={handleSuccessFormSubmission} />
                </Elements>
              ) : (
                <Skeleton
                  variant="rounded"
                  height="100%"
                  sx={{ width: '100%', minHeight: '70vh', margin: '0 auto', maxWidth: 400 }}
                />
              )}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  )
}

BillingDetails.displayName = 'BillingDetails'
