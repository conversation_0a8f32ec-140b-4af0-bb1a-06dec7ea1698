'use client'

import Fade from '@mui/material/Fade'
import { ThemeOptions } from '@mui/material/styles'

import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'

declare module '@mui/material/styles' {
  interface BreakpointOverrides {
    xs: true
    xsMobile: true
    sm: true
    twMd: true
    md: true
    lg: true
    xl: true
    containerWidth: true
  }
}

const backgroundColor00 = '#da7ce8'
const backgroundColor01 = '#000000' // main
const backgroundColor02 = '#17171a' // paper
const backgroundColor03 = 'rgba(149,157,173,0.16)'
const backgroundColor04 = 'rgba(141,148,163,0.4)'
const backgroundColor05 = 'rgba(141,148,163,0.24)'
const backgroundColor06 = 'rgba(141,148,163,0.08)'
const backgroundColor07 = 'rgba(255,255,255,0.04)'
const backgroundColor08 = 'hsl(var(--black-500))'
const backgroundColor09 = '#7C68E0'
const backgroundColor10 = '#DB8BE7'
const backgroundColor11 = '#585D66'
const backgroundColor12 = '#635BFF'
const backgroundColor13 = 'rgba(255,255,255,0.16)' // hover, new community
const backgroundColor14 = '#8D94A3'
const backgroundColor15 = 'rgba(141,148,163,0.12)'
const backgroundColor16 = '#eefae8'
const backgroundColor17 = 'rgba(219,139,231,0.12)'
const backgroundColor18 = 'rgba(149,157,173,0.08)'
const backgroundColor19 = '#212124'
const backgroundColor20 = '#FAFBFB'
const backgroundColor21 = 'rgba(111, 118, 133, 0.08)'
const backgroundGradient01 = `linear-gradient(225deg, #DDD6F3 0%, #FAACA8 33.04%, #DB8BE7 100%)`
const backgroundGradient02 = `linear-gradient(225deg, #DB8BE7 0%, #FAACA8 33.04%, #DDD6F3 100%)`
const borderColor01 = 'rgba(50,49,52,0.6)'
const borderColor02 = 'rgba(141,148,163,0.4)'
const borderColor03 = 'rgba(255,255,255,0.6)'
const borderColor04 = 'rgba(255,255,255,0.16)'
const borderColor05 = '#a9e087'
const borderColor06 = 'rgba(141,148,163,0.12)'
const borderColor07 = '#262629'
const borderColor08 = '#DB8BE7'
const borderColor09 = 'rgba(141,148,163,0.12)'
const borderColor10 = 'rgba(141,148,163,0.16)'
const color01 = '#FFFFFF'
const color02 = 'rgba(255,255,255,0.87)'
const color03 = '#8D94A3' // diabled, placeholder, Tab
const color04 = '#7B828F'
const color05 = 'rgba(255,255,255,0.4)'
const color06 = '#a9e087'
const color07 = '#585D66'
const color08 = '#1D1D1F'
const color09 = '#4E713C'
const color10 = 'rgba(0,0,0,0.4)'
const color11 = 'rgba(216,216,216)'
const opacity01 = 0.12
const opacity02 = 0.38
const opacity03 = 0.08

const lightBackgroundColor02 = 'rgba(243,245,245,1.0)'
const lightBackgroundColor03 = 'rgba(0, 0, 0, 0.08)'

// export const colorOptions = [
//   {
//     primary: '#7B51E0',
//     secondary: 'linear-gradient(90deg, #7553D8 0%, #B553D8 50%, #D8539D 100%)',
//   },
//   {
//     primary: '#3A86FF',
//     secondary: 'linear-gradient(90deg, #4C80EF 0%, #5845D8 50%, #9545D8 100%)',
//   },
//   {
//     primary: '#8EB6D3',
//     secondary: 'linear-gradient(90deg, #8EACC5 0%, #ACACEF 50%, #B59CD8 100%)',
//   },
//   {
//     primary: '#848484',
//     secondary: 'linear-gradient(90deg, #7E7E7E 0%, #C5C5C5 50%, #EFEFEF 100%)',
//   },
//   {
//     primary: '#58B560',
//     secondary: 'linear-gradient(90deg, #6BAB65 0%, #4A7E69 50%, #74A9C5 100%)',
//   },
//   {
//     primary: '#53A241',
//     secondary: 'linear-gradient(90deg, #62984A 0%, #53AB70 50%, #3D797E 100%)',
//   },
//   {
//     primary: '#B7954D',
//     secondary: 'linear-gradient(90deg, #A88E53 0%, #98904B 50%, #8EAB54 100%)',
//   },
//   {
//     primary: '#FF5457',
//     secondary: 'linear-gradient(90deg, #E05C58 0%, #A86342 50%, #98733C 100%)',
//   },
//   {
//     primary: '#F3722C',
//     secondary: 'linear-gradient(90deg, #D7733C 0%, #E0A43F 50%, #A8942F 100%)',
//   },
//   {
//     primary: '#F8961E',
//     secondary: 'linear-gradient(90deg, #E2963D 0%, #D7B13A 50%, #E0DB3C 100%)',
//   },
//   {
//     primary: '#FFBE0B',
//     secondary: 'linear-gradient(90deg, #E9B741 0%, #E2D33F 50%, #A0D73C 100%)',
//   },
//   {
//     primary: '#FF5D8F',
//     secondary: 'linear-gradient(90deg, #E16388 0%, #E97D67 50%, #E29E63 100%)',
//   },
//   {
//     primary: '#FF206E',
//     secondary: 'linear-gradient(90deg, #DE3A69 0%, #E1583B 50%, #E98D3D 100%)',
//   },
//   {
//     primary: '#DB8BE7',
//     secondary: 'linear-gradient(90deg, #C487D5 0%, #E18FB9 50%, #E99B94 100%)',
//   },
//   {
//     primary: '#F5CBFF',
//     secondary: 'linear-gradient(90deg, #F5CBFF 0%, #F5CBFF 50%, #F5CBFF 100%)',
//   },
//   {
//     primary: '#FFD3A5',
//     secondary: 'linear-gradient(90deg, #FFD3A5 0%, #FFD3A5 50%, #FFD3A5 100%)',
//   },
//   {
//     primary: '#D8D8D8',
//     secondary: 'linear-gradient(90deg, #D8D8D8 0%, #D8D8D8 50%, #D8D8D8 100%)',
//   },
//   {
//     primary: '#E7F0FD',
//     secondary: 'linear-gradient(90deg, #E7F0FD 0%, #E7F0FD 50%, #E7F0FD 100%)',
//   },
// ]

export const colorOptions = [
  {
    primary: 'rgba(123,81,224,1)',
    secondary: 'linear-gradient(90deg, rgba(117,83,216,1) 0%, rgba(181,83,216,1) 50%, rgba(216,83,157,1) 100%)',
  },
  {
    primary: 'rgba(58,134,255,1)',
    secondary: 'linear-gradient(90deg, rgba(76,128,239,1) 0%, rgba(88,69,216,1) 50%, rgba(149,69,216,1) 100%)',
  },
  {
    primary: 'rgba(142,182,211,1)',
    secondary: 'linear-gradient(90deg, rgba(142,172,197,1) 0%, rgba(172,172,239,1) 50%, rgba(181,156,216,1) 100%)',
  },
  {
    primary: 'rgba(132,132,132,1)',
    secondary: 'linear-gradient(90deg, rgba(126,126,126,1) 0%, rgba(197,197,197,1) 50%, rgba(239,239,239,1) 100%)',
  },
  {
    primary: 'rgba(88,181,96,1)',
    secondary: 'linear-gradient(90deg, rgba(107,171,101,1) 0%, rgba(74,126,105,1) 50%, rgba(116,169,197,1) 100%)',
  },
  {
    primary: 'rgba(83,162,65,1)',

    secondary: 'linear-gradient(90deg, rgba(98,152,74,1) 0%, rgba(83,171,112,1) 50%, rgba(61,121,126,1) 100%)',
  },
  {
    primary: 'rgba(183,149,77,1)',
    secondary: 'linear-gradient(90deg, rgba(168,142,83,1) 0%, rgba(152,144,75,1) 50%, rgba(142,171,84,1) 100%)',
  },
  {
    primary: 'rgba(255,84,87,1)',
    secondary: 'linear-gradient(90deg, rgba(224,92,88,1) 0%, rgba(168,99,66,1) 50%, rgba(152,115,60,1) 100%)',
  },
  {
    primary: 'rgba(243,114,44,1)',
    secondary: 'linear-gradient(90deg, rgba(215,115,60,1) 0%, rgba(224,164,63,1) 50%, rgba(168,148,47,1) 100%)',
  },
  {
    primary: 'rgba(248,150,30,1)',
    secondary: 'linear-gradient(90deg, rgba(226,150,61,1) 0%, rgba(215,177,58,1) 50%, rgba(224,219,60,1) 100%)',
  },
  {
    primary: 'rgba(255,190,11,1)',
    secondary: 'linear-gradient(90deg, rgba(233,183,65,1) 0%, rgba(226,211,63,1) 50%, rgba(160,215,60,1) 100%)',
  },
  {
    primary: 'rgba(255,93,143,1)',
    secondary: 'linear-gradient(90deg, rgba(225,99,136,1) 0%, rgba(233,125,103,1) 50%, rgba(226,158,99,1) 100%)',
  },
  {
    primary: 'rgba(255,32,110,1)',
    secondary: 'linear-gradient(90deg, rgba(222,58,105,1) 0%, rgba(225,88,59,1) 50%, rgba(233,141,61,1) 100%)',
  },
  {
    primary: 'rgba(219,139,231,1)',
    secondary: 'linear-gradient(90deg, rgba(196,135,213,1) 0%, rgba(225,143,185,1) 50%, rgba(233,155,148,1) 100%)',
  },
  {
    primary: 'rgba(245,203,255,1)',
    secondary: 'linear-gradient(90deg, rgba(245,203,255,1) 0%, rgba(245,203,255,1) 50%, rgba(245,203,255,1) 100%)',
  },
  {
    primary: 'rgba(255,211,165,1)',

    secondary: 'linear-gradient(90deg, rgba(255,211,165,1) 0%, rgba(255,211,165,1) 50%, rgba(255,211,165,1) 100%)',
  },
  {
    primary: 'rgba(216,216,216,1)',
    secondary: 'linear-gradient(90deg, rgba(216,216,216,1) 0%, rgba(216,216,216,1) 50%, rgba(216,216,216,1) 100%)',
  },
  {
    primary: 'rgba(231,240,253,1)',
    secondary: 'linear-gradient(90deg, rgba(231,240,253,1) 0%, rgba(231,240,253,1) 50%, rgba(231,240,253,1) 100%)',
  },
]

export const DefaultThemeOptions: ThemeOptions = {
  breakpoints: {
    values: {
      xs: 0,
      xsMobile: 450,
      twMd: 768,
      sm: 968,
      md: 1024,
      lg: 1280,
      xl: 1536,
      containerWidth: 1440,
    },
  },
  palette: {
    action: {
      activatedOpacity: opacity01,
      active: color01,
      disabled: color03,
      disabledBackground: backgroundColor03,
      disabledOpacity: opacity02,
      focus: '#313131',
      focusOpacity: opacity01,
      hover: '#313131',
      hoverOpacity: opacity03,
      selected: '#313131',
      selectedOpacity: opacity03,
    },
    background: {
      default: backgroundColor01,
      paper: backgroundColor02,
    },
    divider: borderColor06,
    error: {
      contrastText: '#fff',
      dark: '#d32f2f',
      light: '#e57373',
      main: '#f44336',
    },
    info: {
      contrastText: color01,
      dark: color01,
      light: color01,
      main: color01,
    },
    primary: {
      contrastText: color01,
      dark: colorOptions[0].primary,
      light: backgroundColor01,
      main: colorOptions[0].primary,
    },
    secondary: {
      // dark: will be calculated from palette.secondary.main,
      contrastText: color01,
      dark: backgroundColor01, // #c51162
      light: color03,
      main: color11,
    },
    success: {
      contrastText: 'rgba(0, 0, 0, 1)',
      dark: '#388e3c',
      light: '#81c784',
      main: '#4caf50',
    },
    text: {
      primary: color01,
      secondary: color02,
      disabled: color05,
    },
    mode: 'dark',
  },
  components: {
    MuiGrid: {
      styleOverrides: {
        root: {
          '&:focusVisible': {
            outline: 'none',
          },
          '&:focus': {
            outline: 'none',
          },
        },
        item: {
          '&:focusVisible': {
            outline: 'none',
          },
          '&:focus': {
            outline: 'none',
          },
        },
      },
    },
    MuiAvatar: {
      styleOverrides: {
        root: {
          width: 32,
          height: 32,
        },
      },
    },
    MuiAccordion: {
      styleOverrides: {
        root: {
          boxShadow: 'none',
        },
      },
    },
    MuiAccordionSummary: {
      styleOverrides: {
        root: {
          padding: 0,
          minHeight: 24,
          '&.Mui-expanded': {
            minHeight: 24,
          },
        },
        content: {
          margin: 0,
          '&.Mui-expanded': {
            margin: 0,
          },
        },
      },
      // expandIcon: {
      //   padding: 8,
      //   marginRight: -8,
      // },
    },
    MuiAccordionDetails: {
      styleOverrides: {
        root: {
          display: 'block',
          padding: 0,
        },
      },
    },
    MuiAutocomplete: {
      styleOverrides: {
        popper: {
          zIndex: 3006,
        },
      },
    },
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          fontSize: '1rem',
          '& .background-gradient01': {
            background: backgroundGradient01,
          },
          '& .background-gradient02': {
            background: backgroundGradient02,
          },
          '& .background-color00': {
            backgroundColor: backgroundColor00,
          },
          '& .background-color01': {
            backgroundColor: backgroundColor01,
          },
          '& .background-color02': {
            backgroundColor: backgroundColor02,
          },
          '& .background-color03': {
            backgroundColor: backgroundColor03,
          },
          '& .background-color04': {
            backgroundColor: backgroundColor04,
          },
          '& .background-color05': {
            backgroundColor: backgroundColor05,
          },
          '& .background-color06': {
            backgroundColor: backgroundColor06,
          },
          '& .background-color07': {
            backgroundColor: backgroundColor07,
          },
          '& .background-color08': {
            backgroundColor: backgroundColor08,
          },
          '& .background-color09': {
            backgroundColor: backgroundColor09,
          },
          '& .background-color10': {
            backgroundColor: backgroundColor10,
          },
          '& .background-color11': {
            backgroundColor: backgroundColor11,
          },
          '& .background-color12': {
            backgroundColor: backgroundColor12,
          },
          '& .background-color13': {
            backgroundColor: backgroundColor13,
          },
          '& .background-color14': {
            backgroundColor: backgroundColor14,
          },
          '& .background-color15': {
            backgroundColor: backgroundColor15,
          },
          '& .background-color16': {
            backgroundColor: backgroundColor16,
          },
          '& .background-color17': {
            backgroundColor: backgroundColor17,
          },
          '& .background-color18': {
            backgroundColor: backgroundColor18,
          },
          '& .background-color19': {
            backgroundColor: backgroundColor19,
          },
          '& .background-color21': {
            backgroundColor: backgroundColor18,
          },
          '& .background-d-dark-background-3-l-pure-white': {
            backgroundColor: 'var(--dark-background-3)',
          },
          '& .border01': {
            border: `1px solid ${borderColor10}`,
          },
          '& .border-color01': {
            borderColor: borderColor01,
          },
          '& .border-color02': {
            borderColor: borderColor02,
          },
          '& .border-color03': {
            borderColor: borderColor03,
          },
          '& .border-color04': {
            borderColor: borderColor04,
          },
          '& .border-color05': {
            borderColor: borderColor05,
          },
          '& .border-color06': {
            borderColor: borderColor06,
          },
          '& .border-color07': {
            borderColor: borderColor07,
          },
          '& .border-color08': {
            borderColor: borderColor08,
          },
          '& .border-color09': {
            borderColor: borderColor09,
          },
          '& .border-color10': {
            borderColor: borderColor10,
          },
          '& .color01': {
            color: color01,
          },
          '& .color02': {
            color: color02,
          },
          '& .color03': {
            color: color03,
          },
          '& .color04': {
            color: color04,
          },
          '& .color05': {
            color: color05,
          },
          '& .color06': {
            color: color06,
          },
          '& .color07': {
            color: color07,
          },
          '& .color08': {
            color: color08,
          },
          '& .color09': {
            color: color09,
          },
          '& .hidden': {
            display: 'none',
          },
          '& .font-x-large': {
            fontSize: '28px!important',
            fontWeight: 'bold!important',
          },
          '& .font-large': {
            fontSize: '18px!important',
            fontWeight: 'bold!important',
          },
          '& .font-bold': {
            fontWeight: 'bold!important',
          },
          '& .font-family-graphik-bold': {
            fontFamily: 'Graphik Bold!important',
          },
          '& .font-family-graphik-medium': {
            fontFamily: 'Graphik Medium!important',
          },
          '& .font-family-graphik-semibold': {
            fontFamily: 'Graphik Semibold!important',
          },
        },
      },
    },
    MuiStepIcon: {
      styleOverrides: {
        root: {
          color: '#faeffb',
          '&.Mui-active .MuiStepIcon-text': {
            fill: 'white',
          },
          '&.Mui-active .MuiStepLabel-label': {
            color: color03,
          },
        },
        text: {
          fill: backgroundColor10,
        },
      },
    },
    MuiStepLabel: {
      styleOverrides: {
        label: {
          color: color03,
          '&.Mui-active': {
            color: color01,
          },
        },
      },
    },
    MuiSvgIcon: {
      styleOverrides: {
        root: {
          display: 'inline-block',
          fontSize: '21px',
          lineHeight: 'normal',
        },
      },
    },
    MuiTooltip: {
      defaultProps: {
        TransitionComponent: Fade,
        TransitionProps: {
          timeout: 150,
        },
      },
      styleOverrides: {
        popper: {
          '&.styled-tooltip .MuiTooltip-tooltip': {
            borderWidth: '1px',
            borderStyle: 'solid',
            fontSize: 13,
            fontWeight: 400,
            padding: '9px 18px',
          },
          '&.styled-tooltip .MuiTooltip-tooltip b': {
            fontFamily: 'Graphik Semibold',
            fontWeight: 400,
          },
        },
        tooltip: {
          color: 'white',
          fontSize: 14,
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: {
          minHeight: 24,
        },
        indicator: {
          backgroundColor: colorOptions[0].primary,
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          minWidth: 0,
          minHeight: 24,
          paddingLeft: 0,
          paddingRight: 0,
          color: color03,
          fontSize: '1rem',
          fontFamily: 'Graphik Medium',
          marginRight: 24,
          '&:last-of-type': {
            marginRight: 0,
          },
          '&.MuiTab-textColorPrimary.Mui-selected': {
            color: colorOptions[0].primary,
          },
          '@media (min-width: 0px)': {
            minWidth: 0,
          },
          '@media (min-width: 600px)': {
            minWidth: 0,
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          backgroundColor: 'transparent',
        },
      },
    },
    MuiBackdrop: {
      styleOverrides: {
        root: {
          backgroundColor: 'rgb(58, 59, 61, 0.92)',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundColor: backgroundColor08,
          backgroundImage: 'none',
          borderColor: borderColor01,
          borderRadius: 12,
          boxShadow: 'none',
          '&.opposite': {
            color: backgroundColor02,
            backgroundColor: color02,
          },
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: 0,
        },
      },
    },
    MuiCardActions: {
      styleOverrides: {
        root: {
          color: color03,
        },
      },
    },
    MuiCheckbox: {
      styleOverrides: {
        root: {
          lineHeight: 1,
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        root: {
          zIndex: 1000,
        },
        paper: {
          backgroundImage: 'none',
        },
      },
    },
    MuiDialogTitle: {
      styleOverrides: {
        root: {
          borderBottomWidth: 1,
          borderBottomStyle: 'solid',
          borderColor: borderColor06,
          fontSize: 18,
          paddingTop: 24,
          paddingBottom: 16,
          paddingLeft: 16,
          paddingRight: 16,
          '& .MuiTypography-h6': {
            fontSize: 20,
            fontWeight: 500,
            letterSpacing: 0,
            lineHeight: '20px',
          },
        },
      },
    },
    MuiDialogContent: {
      styleOverrides: {
        root: {
          position: 'relative',
          width: '100%',
          padding: 16,
        },
      },
    },
    MuiDialogActions: {
      styleOverrides: {
        root: {
          backgroundColor: borderColor06,
          padding: 16,
        },
      },
    },
    MuiButtonBase: {
      styleOverrides: {
        root: {
          '& svg': {
            display: 'inline',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 2,
          boxShadow: 'none',
          padding: '12px 16px',
          margin: '0 8px',
          fontFamily: 'Graphik Medium', // Graphik Regular
          fontSize: '14px',
          letterSpacing: 'normal',
          '&:hover': {
            boxShadow: 'none',
          },
          '&.round-small': {
            borderRadius: '10px !important',
          },
          '&.round-large': {
            borderRadius: 32,
          },
          '&.text-small': {
            fontSize: 12,
          },
          '&.text-medium': {
            fontSize: 14,
          },
          '&.text-large': {
            fontSize: 16,
          },
          '&.demi': {
            fontFamily: 'Graphik Medium',
          },
          '&.bold': {
            fontFamily: 'Graphik SemiBold',
          },
          '&.icon-left': {
            '& .MuiButton-startIcon': {
              position: 'absolute',
              left: 19,
            },
          },
          '&:first-of-type': {
            marginLeft: 0,
          },
          '&:last-of-type': {
            marginRight: 0,
          },
          '&.app-button': {
            minWidth: 128,
          },
          '&.app-menu-button': {
            minWidth: 50,
          },
          '&.app-button-xxs': {
            minHeight: 24,
          },
          '&.no-padding': {
            padding: 0,
          },
          '&.padding-17': {
            paddingTop: 17,
            paddingBottom: 17,
          },
          '&.no-shadow': {
            boxShadow: 'none',
          },
        },
        contained: {
          padding: '8px 16px',
          minHeight: 38,
        },
        containedPrimary: {
          '&:hover': {
            filter: 'saturate(150%)',
          },
          '&:focus': {
            filter: 'saturate(150%)',
          },
          '&.Mui-disabled': {
            filter: 'saturate(70%)',
          },
        },
        containedSizeSmall: {
          padding: '4px 10px',
          minHeight: 32,
        },
        containedSizeLarge: {
          padding: '11px 16px',
          minHeight: 48,
        },
        text: {
          '&.no-padding': {
            minWidth: 0,
          },
        },
        outlined: {
          padding: '7px 15px',
          minHeight: 38,
          '&:hover': {
            borderWidth: 1,
          },
          '&:focus': {
            borderWidth: 1,
          },
        },
        outlinedPrimary: {
          '&:hover': {
            borderWidth: 1,
          },
          '&:focus': {
            borderWidth: 1,
          },
        },
        outlinedSizeSmall: {
          padding: '4px 9px',
          minHeight: 32,
        },
        outlinedSizeLarge: {
          padding: '10px 15px',
          minHeight: 48,
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          '&.close': {
            position: 'absolute',
            fontSize: 24,
            right: 16,
            top: 12,
            zIndex: 1,
            '&.large': {
              fontSize: 24,
              right: 8,
              top: 8,
            },
            '&.x-large': {
              fontSize: 32,
            },
          },
        },
        sizeSmall: {
          height: 28,
          width: 28,
        },
      },
    },
    MuiListSubheader: {
      styleOverrides: {
        root: {
          backgroundColor: 'transparent',
          fontFamily: 'Graphik Medium',
          fontSize: '1rem',
          fontWeight: 400,
          lineHeight: '16px',
          paddingLeft: 8,
          paddingRight: 8,
          color: color03,
          '&.font-family-semibold': {
            fontFamily: 'Graphik Semibold',
          },
          '&.font-family-graphik-medium': {
            fontFamily: 'Graphik Medium',
          },
        },
      },
    },
    MuiListItem: {
      styleOverrides: {
        root: {
          paddingTop: 4,
          paddingBottom: 4,
          marginTop: 2,
          marginBottom: 2,
          fontFamily: 'Graphik Medium',
          fontSize: 14,
          lineHeight: '24px',
          letterSpacing: '0.4px',
          '&.list-subheader': {
            fontSize: 16,
            lineHeight: '20px',
            letterSpacing: '0.35px',
          },
          '&.font-family-semibold': {
            fontFamily: 'Graphik Semibold',
          },
          '&.font-family-graphik-medium': {
            fontFamily: 'Graphik Medium',
          },
          '&.creator-menu-item': {
            letterSpacing: 0,
            lineHeight: '16px',
          },
          '&.Mui-selected': {
            backgroundColor: borderColor04,
          },
          '&.background-gradient01': {
            borderRadius: 20,
            height: 40,
          },
          '&.background-gradient.Mui-selected, &.background-gradient:hover': {
            background: backgroundGradient01,
          },
          '&.text-only': {
            '&.Mui-selected': {
              backgroundColor: 'transparent',
              color: backgroundColor00,
            },
            '& .MuiTypography-root': {
              fontSize: 'inherit',
              fontWeight: 'inherit',
              color: 'inherit',
            },
          },
        },
        // button: {
        //   '&:hover': {
        //     '&.homeListItem': {
        //       background: `linear-gradient(225deg, #DDD6F3 0%, #FAACA8 33.04%, #DB8BE7 100%)`,
        //     },
        //   },
        // },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          paddingTop: 4,
          paddingBottom: 4,
          marginTop: 2,
          marginBottom: 2,
          fontFamily: 'Graphik Medium',
          fontSize: 14,
          lineHeight: '24px',
          letterSpacing: '0.4px',
          '&.list-subheader': {
            fontSize: 16,
            lineHeight: '20px',
            letterSpacing: '0.35px',
          },
          '&.font-family-semibold': {
            fontFamily: 'Graphik Semibold',
          },
          '&.font-family-graphik-medium': {
            fontFamily: 'Graphik Medium',
          },
          '&.creator-menu-item': {
            letterSpacing: 0,
            lineHeight: '16px',
          },
          '&.Mui-selected': {
            backgroundColor: borderColor04,
          },
          '&.background-gradient01': {
            borderRadius: 20,
            height: 40,
          },
          '&.background-gradient.Mui-selected, &.background-gradient:hover': {
            background: backgroundGradient01,
          },
          '&.text-only': {
            '&.Mui-selected': {
              backgroundColor: 'transparent',
              color: backgroundColor00,
            },
            '& .MuiTypography-root': {
              fontSize: 'inherit',
              fontWeight: 'inherit',
              color: 'inherit',
            },
          },
        },
        // button: {
        //   '&:hover': {
        //     '&.homeListItem': {
        //       background: `linear-gradient(225deg, #DDD6F3 0%, #FAACA8 33.04%, #DB8BE7 100%)`,
        //     },
        //   },
        // },
      },
    },
    MuiListItemIcon: {
      styleOverrides: {
        root: {
          minWidth: 26,
        },
      },
    },
    MuiFormControlLabel: {
      styleOverrides: {
        label: {
          fontSize: 14,
        },
      },
    },
    MuiFormHelperText: {
      styleOverrides: {
        root: {
          fontSize: 12,
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          '&.opposite': {
            color: backgroundColor01,
            backgroundColor: color01,
          },
          '&.menu': {
            minWidth: 240,
            borderWidth: 1,
            borderStyle: 'solid',
            borderColor: borderColor06,
          },
          '&:focusVisible': {
            outline: 'none',
          },
          '&:focus': {
            outline: 'none',
          },
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          '&.demi': {
            fontFamily: 'Graphik Medium',
          },
          '& img': {
            display: 'inline',
          },
          '& span, & svg': {
            display: 'inline-block',
          },
          '& ul': {
            marginTop: '1em',
            marginBottom: '1em',
            paddingInlineStart: '40px',
            listStyleType: 'disc',
          },
          '& h1': {
            fontSize: '2em',
            marginBlockStart: '0.67em',
            marginBlockEnd: '0.67em',
          },
        },
        h1: {
          fontFamily: 'Graphik SemiBold',
        },
        h2: {
          fontFamily: 'Graphik SemiBold',
          '&.demi': {
            fontFamily: 'Graphik Medium',
          },
        },
        h3: {
          fontFamily: 'Graphik SemiBold',
        },
        h4: {
          fontFamily: 'Graphik SemiBold',
        },
        h5: {
          fontFamily: 'Graphik Medium',
          '&.bold': {
            fontFamily: 'Graphik SemiBold',
          },
        },
        h6: {
          fontFamily: 'Graphik Medium',
          '&.bold': {
            fontFamily: 'Graphik SemiBold',
          },
        },
      },
    },
    MuiInputBase: {
      styleOverrides: {
        root: {
          fontSize: '14px',
        },
      },
    },
    MuiInputAdornment: {
      // positionEnd: {
      //   marginRight: -4,
      // },
    },
    MuiOutlinedInput: {
      // adornedEnd: {
      //   paddingRight: -4,
      // },
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: color03,
          },
          '&.Mui-focused': {
            borderColor: color03,
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: color01,
              borderWidth: 1,
            },
          },
        },
      },
    },
    MuiPopover: {
      styleOverrides: {
        root: {
          zIndex: 1,
        },
      },
    },
    MuiMenu: {
      styleOverrides: {
        root: {
          zIndex: 1400,
          '& .MuiBackdrop-root': {
            backgroundColor: 'transparent',
          },
        },
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          minHeight: 32,
          paddingLeft: 6,
          paddingRight: 6,
          '&.no-background:hover': {
            background: 'transparent',
          },
          '&.background-gradient:hover': {
            background: backgroundGradient01,
          },
        },
      },
    },
    MuiSelect: {
      styleOverrides: {
        select: {
          padding: 10,
        },
      },
    },
    MuiLinearProgress: {
      styleOverrides: {
        colorPrimary: {
          backgroundColor: borderColor06,
        },
        barColorPrimary: {
          background: backgroundGradient02,
        },
        root: {
          borderRadius: 4,
        },
        bar: {
          borderRadius: 4,
        },
      },
    },
  },
  shape: {
    borderRadius: 4,
  },
  spacing: [0, 4, 8, 16, 24, 32, 64],
  typography: {
    fontFamily: ['Graphik Regular', 'Larsseit', 'Roboto', 'Helvetica', 'Arial', 'sans-serif'].join(','),
    fontSize: 16,
    htmlFontSize: 16,
    allVariants: {
      color: color01,
    },
    h1: {
      fontSize: 48,
      fontWeight: 500,
      letterSpacing: -1.5,
    },
    h2: {
      fontSize: 40,
      fontWeight: 500,
      lineHeight: 1.18,
      letterSpacing: -1.5,
    },
    h3: {
      fontSize: 32,
      fontWeight: 500,
    },
    h4: {
      fontSize: 22,
      fontWeight: 500,
    },
    h5: {
      fontSize: 18,
      lineHeight: 1.4,
    },
    h6: {
      fontSize: 16,
      lineHeight: 1.2,
    },
    body1: {
      fontSize: '0.875rem',
      fontWeight: 400,
      letterSpacing: '0em',
      lineHeight: 1.2,
    },
    body2: {
      fontSize: '0.805rem',
      fontWeight: 400,
      letterSpacing: '0.01071em',
      lineHeight: 1.2,
    },
    button: {
      fontSize: '0.875rem',
      fontWeight: 500,
      letterSpacing: '0.02857em',
      lineHeight: 1,
      textTransform: 'none',
    },
    subtitle1: {
      fontSize: '0.875rem',
      fontWeight: 500,
      letterSpacing: '0.00938em',
      lineHeight: 1.2,
    },
    subtitle2: {
      fontSize: '0.75rem',
      fontWeight: 500,
      letterSpacing: '0.00714em',
      lineHeight: 1.2,
    },
  },
  mixins: {
    toolbar: {
      '@media (min-width:0px) and (orientation: landscape)': {
        minHeight: 48,
      },
      '@media (min-width:600px)': {
        minHeight: 72,
      },
      minHeight: 72,
    },
  },
}

export const DefaultDarkThemeOptions: Partial<ThemeOptions> = {
  palette: {
    mode: 'dark',
  },
  components: {
    MuiTooltip: {
      styleOverrides: {
        popper: {
          [`&.styled-tooltip .MuiTooltip-tooltip`]: {
            backgroundColor: 'var(--grey-300)',
            borderColor: 'var(--grey-800)',
            color: 'var(--spark-gray)',
          },
        },
      },
    },
  },
}

export const DefaultLightThemeOptions: Partial<ThemeOptions> = {
  palette: {
    action: {
      activatedOpacity: opacity01,
      active: backgroundColor01,
      disabled: 'rgba(0, 0, 0, 0.08)',
      disabledBackground: 'rgba(0, 0, 0, 0.08)',
      disabledOpacity: opacity02,
      focus: '#F2F2F2',
      focusOpacity: opacity01,
      hover: '#F2F2F2',
      hoverOpacity: opacity03,
      selected: '#F2F2F2',
      selectedOpacity: opacity03,
    },
    background: {
      default: color01,
      paper: backgroundColor20,
    },
    info: {
      contrastText: backgroundColor01,
      dark: backgroundColor01,
      light: backgroundColor01,
      main: backgroundColor01,
    },
    primary: {
      contrastText: backgroundColor01,
      dark: backgroundColor01,
      light: backgroundColor01,
      main: backgroundColor01,
    },
    secondary: {
      // dark: will be calculated from palette.secondary.main,
      contrastText: backgroundColor01,
      dark: backgroundColor01,
      light: backgroundColor01,
      main: backgroundColor02,
    },
    text: {
      primary: backgroundColor01,
      secondary: backgroundColor02,
      disabled: color10,
    },
    mode: 'light',
  },
  components: {
    MuiAutocomplete: {
      styleOverrides: {
        popper: {
          color: backgroundColor01,
          zIndex: 3006,
        },
      },
    },
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          '& .background-gradient01': {
            background: backgroundGradient01,
          },
          '& .background-gradient02': {
            background: backgroundGradient02,
          },
          '& .background-color00': {
            backgroundColor: backgroundColor00,
          },
          '& .background-color01': {
            backgroundColor: color01,
          },
          '& .background-color02': {
            backgroundColor: lightBackgroundColor02,
          },
          '& .background-color03': {
            backgroundColor: lightBackgroundColor03,
          },
          '& .background-color04': {
            backgroundColor: backgroundColor04,
          },
          '& .background-color05': {
            backgroundColor: backgroundColor05,
          },
          '& .background-color06': {
            backgroundColor: backgroundColor06,
          },
          '& .background-color07': {
            backgroundColor: backgroundColor07,
          },
          '& .background-color08': {
            backgroundColor: color01,
          },
          '& .background-color09': {
            backgroundColor: backgroundColor09,
          },
          '& .background-color10': {
            backgroundColor: backgroundColor10,
          },
          '& .background-color11': {
            backgroundColor: backgroundColor11,
          },
          '& .background-color12': {
            backgroundColor: backgroundColor12,
          },
          '& .background-color13': {
            backgroundColor: backgroundColor13,
          },
          '& .background-color14': {
            backgroundColor: backgroundColor14,
          },
          '& .background-color15': {
            backgroundColor: backgroundColor15,
          },
          '& .background-color16': {
            backgroundColor: backgroundColor16,
          },
          '& .background-color17': {
            backgroundColor: backgroundColor17,
          },
          '& .background-color18': {
            backgroundColor: backgroundColor18,
          },
          '& .background-color19': {
            backgroundColor: color01,
          },
          '& .background-color21': {
            backgroundColor: backgroundColor21,
          },
          '& .background-d-dark-background-3-l-pure-white': {
            backgroundColor: 'var(--pure-white)',
          },
          '& .border01': {
            // boxShadow: '2px 2px 0px rgba(0, 0, 0, 0.07)',
            boxShadow:
              '0px 1px 2px rgba(0, 0, 0, 0.01), 0px 1px 6px rgba(0, 0, 0, 0.01), 0px 2px 18px rgba(0, 0, 0, 0.02), 0px 4px 32px rgba(0, 0, 0, 0.02), 0px 8px 80px rgba(0, 0, 0, 0.03)',
          },
          '& .border-color01': {
            borderColor: borderColor01,
          },
          '& .border-color02': {
            borderColor: borderColor02,
          },
          '& .border-color03': {
            borderColor: borderColor03,
          },
          '& .border-color04': {
            borderColor: borderColor04,
          },
          '& .border-color05': {
            borderColor: borderColor05,
          },
          '& .border-color06': {
            borderColor: borderColor06,
          },
          '& .border-color07': {
            borderColor: borderColor07,
          },
          '& .border-color08': {
            borderColor: borderColor08,
          },
          '& .border-color09': {
            borderColor: borderColor09,
          },
          '& .border-color10': {
            borderColor: borderColor10,
          },
          '& .color01': {
            color: backgroundColor01,
          },
          '& .color02': {
            color: backgroundColor02,
          },
          '& .color03': {
            color: color03,
          },
          '& .color04': {
            color: color04,
          },
          '& .color05': {
            color: color05,
          },
          '& .color06': {
            color: color06,
          },
          '& .color07': {
            color: color07,
          },
          '& .color08': {
            color: color08,
          },
          '& .color09': {
            color: color09,
          },
          '& .hidden': {
            display: 'none',
          },
          '& .font-x-large': {
            fontSize: '28px!important',
            fontWeight: 'bold!important',
          },
          '& .font-large': {
            fontSize: '18px!important',
            fontWeight: 'bold!important',
          },
          '& .font-bold': {
            fontWeight: 'bold!important',
          },
          '& .font-family-graphik-bold': {
            fontFamily: 'Graphik Bold!important',
          },
          '& .font-family-graphik-medium': {
            fontFamily: 'Graphik Medium!important',
          },
          '& .font-family-graphik-semibold': {
            fontFamily: 'Graphik Semibold!important',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundColor: color02,
          backgroundImage: 'none',
          borderColor: borderColor01,
          borderRadius: 12,
          boxShadow: 'none',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          '&.opposite': {
            color: backgroundColor01,
            backgroundColor: color01,
          },
          '&.menu': {
            minWidth: 240,
            borderWidth: 1,
            borderStyle: 'solid',
            borderColor: borderColor06,
          },
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          color: backgroundColor01,
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: borderColor01,
          },
          '&.Mui-focused': {
            borderColor: backgroundColor01,
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: backgroundColor01,
              borderWidth: 1,
            },
          },
        },
      },
    },
    MuiListItem: {
      styleOverrides: {
        root: {
          '&.Mui-selected': {
            backgroundColor: borderColor04,
          },
          '&.background-gradient.Mui-selected, &.background-gradient:hover': {
            background: backgroundGradient01,
          },
          '&:hover': {
            background: 'none',
            backgroundColor: `rgba(0, 0, 0, 0.08)`,
          },
          '&.text-only': {
            '&.Mui-selected': {
              backgroundColor: 'transparent',
              color: backgroundColor00,
            },
          },
        },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          '&.Mui-selected': {
            backgroundColor: borderColor04,
          },
          '&.background-gradient.Mui-selected, &.background-gradient:hover': {
            background: backgroundGradient01,
          },
          '&:hover': {
            background: 'none',
            backgroundColor: `rgba(0, 0, 0, 0.08)`,
          },
          '&.text-only': {
            '&.Mui-selected': {
              backgroundColor: 'transparent',
              color: backgroundColor00,
            },
          },
        },
      },
    },
    MuiMenu: {
      styleOverrides: {
        root: {},
        paper: {
          backgroundColor: color01,
          '&.MuiPopover-paper': {
            backgroundColor: color01,
          },
        },
      },
    },
    MuiPopover: {
      styleOverrides: {
        root: {},
        paper: {
          backgroundColor: color01,
        },
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          color: backgroundColor01,
        },
      },
    },
    MuiStepLabel: {
      styleOverrides: {
        label: {
          color: color03,
          '&.Mui-active': {
            color: backgroundColor01,
          },
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          color: '#696F7A',
        },
      },
    },
    MuiTooltip: {
      styleOverrides: {
        popper: {
          [`&.styled-tooltip .MuiTooltip-tooltip`]: {
            backgroundColor: 'var(--white-400)',
            borderColor: 'var(--ui-dark-700)',
            color: 'var(--font-light-ui-gray)',
          },
          [`&.styled-tooltip .MuiTooltip-tooltip b`]: {
            color: 'var(--font-light-ui-black)',
          },
        },
      },
    },
  },
}

export const AppThemeOptions = {
  [THEME_MODE_ENUM.dark]: DefaultDarkThemeOptions,
  [THEME_MODE_ENUM.light]: DefaultLightThemeOptions,
}
