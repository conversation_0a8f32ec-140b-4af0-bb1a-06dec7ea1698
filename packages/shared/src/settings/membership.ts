'use client'

import { colorOptions } from './theme'
import { ADMIN_EMAIL_NOTIFICATION_SETTINGS, MEMBER_EMAIL_NOTIFICATION_SETTINGS } from '@/shared-settings/notifications'
import { BACKGROUND_TYPE_ENUM, THEME_MODE_ENUM, VISIBILITY_ENUM } from '@/shared-types/enum'
import { IMembership, IMembershipSetting } from '@/shared-types/interfaces'

export const DefaultMembership: Partial<IMembership> = {
  name: 'MemberUp Membership',
  brand: 'Default',
  active: false,
  slug: '',
}

export const DefaultMembershipSetting: Partial<IMembershipSetting> = {
  visibility: VISIBILITY_ENUM.private,
  active_campaign: {
    api_key: '',
    api_url: '',
    contact_list: 1,
  },
  assets_path: '/assets/default',
  emails: [
    ...MEMBER_EMAIL_NOTIFICATION_SETTINGS.map((item) => ({
      name: item.name,
      enable: item.enable,
      is_admin: false,
    })),
    ...ADMIN_EMAIL_NOTIFICATION_SETTINGS.map((item) => ({
      name: item.name,
      enable: item.enable,
      is_admin: true,
    })),
  ],
  logo: undefined,
  onboarding: {
    show_onboarding_popup: false,
    welcome_title: '',
    welcome_message: '',
    welcome_description: '',
    welcome_asset: '',
    bio_questions: [],
  },
  plan: undefined,
  signin: {
    background_color: undefined,
    background_type: BACKGROUND_TYPE_ENUM.html,
    button_background_color: undefined,
    button_color: undefined,
    color: undefined,
    cover_image: '',
    logo_width: 208,
    logo_height: 52,
    title: 'Welcome back',
    subtitle: '',
  },
  signup: {
    background_color: undefined,
    background_type: BACKGROUND_TYPE_ENUM.html,
    button_background_color: undefined,
    button_color: undefined,
    color: undefined,
    cover_image: '',
    logo_width: 208,
    logo_height: 52,
    subtitle: '',
    title: 'Your community is waiting',
    testimonial:
      'My income has now more than doubled as a result of applying what I have learnt from this community. I am generating thousands in extra income per month.',
    testimonial_name: 'Ashley Tran',
    testimonial_image: '/assets/default/images/landing-03.png',
  },
  signup_payment: {
    background_color: undefined,
    background_type: BACKGROUND_TYPE_ENUM.html,
    button_background_color: undefined,
    button_color: undefined,
    color: undefined,
    cover_image: '',
    logo_width: 208,
    logo_height: 52,
    title: '',
    testimonial:
      'My income has now more than doubled as a result of applying what I have learnt from this community. I am generating thousands in extra income per month.',
    testimonial_name: 'Ashley Tran',
    testimonial_image: '/assets/default/images/landing-03.png',
  },
  stripe_enable_annual: true,
  theme_main_color: null,
  theme_secondary_color: null,
  theme_mode: THEME_MODE_ENUM.dark,
  time_zone: 'America/Los_Angeles',
}
