'use client'

import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'

export const NOTIFICATION_SETTINGS: any[] = [
  {
    id: 'mention-notification',
    title: 'New Mention',
    name: 'new-mention-notification',
    email: true,
    email_disabled: false,
    in_app_feed: true,
  },
  {
    id: 'event',
    title: 'Event reminders',
    name: 'event',
    email: true,
    in_app_feed: true,
    email_disabled: false,
  },
  {
    id: 'new-everyone-mention-notification',
    title: 'Everyone Mention',
    name: 'new-everyone-mention-notification',
    email: true,
    email_disabled: false,
    in_app_feed: true,
  },
  {
    id: 'new-comment-notification',
    title: 'Comments on my posts',
    name: 'new-comment-notification',
    email: true,
    in_app_feed: true,
    email_disabled: false,
  },
]

export const MEMBER_EMAIL_NOTIFICATION_SETTINGS = [
  {
    title: 'Send Welcome Email',
    description: `Send a welcome email as soon as a new member signs up so they’re instantly introduced to who you are and what they can expect from your community.`,
    name: 'welcome_email',
    enable: true,
    emailPreview: (
      <Box>
        <Typography variant="body1" sx={{ mb: '18px' }}>
          Welcome&nbsp;
          <span>
            <b>[recipient_name]</b>
          </span>
          !
        </Typography>
        <Typography variant="body1" sx={{ mb: '18px' }}>
          Welcome to&nbsp;
          <span>
            <b>[community_name]</b>
          </span>
          ! We&apos;re thrilled that you&apos;ve decided to join us.
        </Typography>
        <Typography variant="body1" sx={{ mb: '18px' }}>
          Here are some important links for you to keep handy:
        </Typography>
        <Typography variant="body1" sx={{ mb: '18px' }}>
          <a style={{ textDecoration: 'underline' }}>Login page</a>&nbsp;- this is where you can log in to access all
          the good stuff. Simply use the username and password you created when you registered.
        </Typography>
        <Typography variant="body1" sx={{ mb: '18px' }}>
          We&apos;re really excited to connect with you.
        </Typography>
        <Typography variant="body1" sx={{ mb: '18px' }}>
          See you on the inside,
        </Typography>
        <Typography variant="body1" sx={{ textDecoration: 'underline' }}>
          <b>[creator_name]</b>
        </Typography>
      </Box>
    ),
  },
]

export const ADMIN_EMAIL_NOTIFICATION_SETTINGS = [
  {
    title: 'New Member Signup',
    description: `Send a welcome email as soon as your audience signs up so they’re instantly introduced to who you are, what they can expect from your company.`,
    name: 'new_member_signup_email',
    enable: true,
    is_admin: true,
    emailPreview: (
      <Box>
        <Typography align="center" variant="h5" sx={{ mb: '18px' }}>
          New Community Announcement 🚀
        </Typography>
        <Typography align="center" variant="body1" sx={{ mb: '18px' }}>
          <span>
            <b>[member_name]</b>
          </span>
          &nbsp;just joined your community!.
        </Typography>
        <Typography align="center" variant="body1" sx={{ textDecoration: 'underline' }}>
          Welcome!
        </Typography>
      </Box>
    ),
  },
]
