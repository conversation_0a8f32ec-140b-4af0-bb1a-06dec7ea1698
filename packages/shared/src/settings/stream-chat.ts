import memoizeOne from 'memoize-one'

export const getCustomStreamChatStyles = memoizeOne((membershipTheme: any) => ({
  '--primary-color': membershipTheme?.palette?.primary['main'] || '#FFFFFF',
  // '--main-font',
  // '--second-font',
  // '--font-weight-regular',
  // '--font-weight-semi-bold',
  // '--font-weight-bold',
  // '--primary-color',
  // '--primary-color-faded',
  // '--magenta',
  // '--red',
  // '--faded-red',
  // '--dt-bg-team',
  // '--border-color',
  // '--lighten-black',
  // '--lighten-grey',
  // '--light-grey',
  // '--grey',
  // '--dark-grey',
  // '--green',
  // '--faded-green',
  // '--border-radius'
  // '--border-radius-sm',
  // '--border-radius-md',
  // '--border-radius-round',
  // '--spacing-unit',
  '--xs-font': '12px',
  '--sm-font': '14px',
  '--md-font': '14px',
  '--lg-font': '14px',
  '--xl-font': '14px',
  '--xxl-font': '14px',
  '--xxxl-font': '14px',
  '--xxs-m': '4px',
  '--xs-m': '4px',
  '--sm-m': '8px',
  '--md-m': '12px',
  '--lg-m': '14px',
  '--xl-m': '14px',
  '--xxl-m': '14px',
  '--xxs-p': '4px',
  '--xs-p': '4px',
  '--sm-p': '8px',
  '--md-p': '12px',
  '--lg-p': '14px',
  '--xl-p': '14px',
  '--xxl-p': '14px',
  '--bg-gradient-end': '#101214',
  '--bg-gradient-start': '#070a0d',
  '--black': '#ffffff',
  '--blue-alice': '#00193d',
  '--border': membershipTheme?.palette?.divider || '#FFFFFF',
  '--button-background': '#ffffff',
  '--button-text': '#005fff',
  '--grey': '#7a7a7a',
  '--grey-gainsboro': '#2d2f2f',
  '--grey-whisper': '#1c1e22',
  '--modal-shadow': '#000000',
  '--overlay': membershipTheme?.palette?.text?.disabled || '#FFFFFF',
  '--overlay-dark': membershipTheme?.palette?.text?.secondary || '#FFFFFF',
  '--shadow-icon': '#00000080',
  '--targetedMessageBackground': '#302d22',
  '--transparent': 'transparent',
  '--white': membershipTheme?.palette?.background.paper || '#FFFFFF',
  '--white-smoke': '#13151b',
  '--white-snow':
    membershipTheme?.components?.MuiListItem?.styleOverrides?.root['&.Mui-selected']['backgroundColor'] || '#FFFFFF',
}))
