import { captureException as sentryCaptureException } from '@sentry/nextjs'
import _uniq from 'lodash/uniq'
import { getCommunityBaseURL } from 'memberup/src/libs/utils'

import { getFullName } from '@memberup/shared/src/libs/profile'
import { updateAlgoliaMembersIndexForUserId, uploadDataToAlgoliaIndex } from '@/shared-libs/algolia'
import {
  KNOCK_OBJECT_IDS,
  KNOCK_OBJECTS,
  knockAddObjects,
  knockBulkAddSubscriptions,
  knockIdentifyUser,
  knockSetTenant,
  knockSetUserPreferences,
  knockTriggerWorkflow,
} from '@/shared-libs/knock'
import { updateUser } from '@/shared-libs/prisma/user'
import { streamChatUpsertUser } from '@/shared-libs/stream-chat'
import { NOTIFICATION_SETTINGS } from '@/shared-settings/notifications'
import { KNOCK_WORKFLOW_ENUM, USER_STATUS_ENUM } from '@/shared-types/enum'
import { IMembership, IUser } from '@/shared-types/interfaces'

const MU_ID = process.env.NEXT_PUBLIC_MU_ID

export const setupMembership = async (membership: IMembership) => {
  try {
    const membershipId = membership.id
    const membershipSlug = membership.slug
    const owner = membership.owner

    await knockSetTenant(membershipId, { name: membership.name })

    if (owner?.status === USER_STATUS_ENUM.inactive) {
      const userFullName = getFullName(owner.first_name, owner.last_name, '')
      await updateUser({
        where: { id: owner.id },
        data: {
          status: USER_STATUS_ENUM.active,
          profile: {
            update: {
              active: true,
            },
          },
        },
      })
      // TODO: is this necessary?
      await knockIdentifyUser(owner)

      await knockAddObjects(membershipSlug, KNOCK_OBJECTS)
      await knockBulkAddSubscriptions(membership.slug, KNOCK_OBJECT_IDS, [owner.id])

      const preferenceSet = {
        workflows: {},
      }
      for (const notificationSetting of NOTIFICATION_SETTINGS) {
        preferenceSet.workflows[notificationSetting.name] = {
          channel_types: {
            email: notificationSetting.email,
            in_app_feed: notificationSetting.in_app_feed,
          },
        }
      }
      knockSetUserPreferences(owner.id, preferenceSet, {
        preferenceSet: membershipId,
      })

      await updateAlgoliaMembersIndexForUserId(owner.id)

      const params = {
        email: owner.email,
        description: membership.name,
        metadata: {
          membership_id: membershipId,
          membership_name: membership.name,
        },
      }

      if (userFullName) {
        params['name'] = userFullName
      }

      await streamChatUpsertUser(
        {
          id: owner.id,
          first_name: owner.first_name || '',
          last_name: owner.last_name || '',
          image: owner.image,
          image_crop_area: null,
          role: owner.role,
          status: USER_STATUS_ENUM.active,
          username: owner.username,
        },
        _uniq([MU_ID, membershipId]),
      )

      await knockTriggerWorkflow(
        KNOCK_WORKFLOW_ENUM.creator_account_confirmation,
        [owner.id],
        {
          community_name: membership.name,
          community_url: `${getCommunityBaseURL(membership)}`,
          reset_pass_url: `${getCommunityBaseURL(membership)}/auth/reset-password`,
        },
        owner.id,
        membership.id,
      )
    }
    return true
  } catch (err) {
    console.log('membershipSetup =======', err)
    sentryCaptureException(err)
    return false
  }
}
