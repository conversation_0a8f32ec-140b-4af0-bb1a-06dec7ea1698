import _get from 'lodash/get'
import _keys from 'lodash/keys'

export function errorHandler(err: any, modelName: string): { message: string } {
  let errorMessage = ''
  const errorCode = _get(err, ['error', 'code'])
  if (errorCode === 11000) {
    errorMessage = `${modelName} already exists`
    const fields = _keys(_get(err, ['error', 'keyPattern']))
    for (let i = 0; i < fields.length; i++) {
      if (i === 0) {
        errorMessage += ` with ${fields[i]}`
      } else {
        errorMessage += `, ${fields[i]}`
      }
    }
  } else if (typeof err === 'string') {
    errorMessage = err
  } else if (err?.message) {
    errorMessage = err.message
  }

  return { message: errorMessage || 'Unknow Error' }
}
