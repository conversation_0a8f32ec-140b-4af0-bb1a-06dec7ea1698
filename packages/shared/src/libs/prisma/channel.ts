import { Channel, Prisma } from '@prisma/client'

import prisma, { mapNullToJsonNull } from './prisma'
import { findMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { hasSpaceLimitBeenReached } from '@/shared-libs/membership-settings'
import { createStreamChannel, deleteStreamChannel } from '@/shared-libs/stream-chat'
import { CHANNEL_TYPE_ENUM } from '@/shared-types/enum'
import { IChannel } from '@/shared-types/interfaces'

const jsonFields = ['details', 'banner_image_crop_area']

export class SpaceDuplicateException extends Error {
  constructor(name: string) {
    super(`Space with name ${name} already exists.`)
    this.name = 'CustomException'
  }
}

export async function hasCommunityReachedSpaceLimit(membershipId: string, currentChannelCount: number) {
  const membershipSetting = await findMembershipSetting({
    where: { membership_id: membershipId },
  })
  return hasSpaceLimitBeenReached(membershipSetting.plan, currentChannelCount)
}

export async function createChannel(payload: Prisma.ChannelCreateArgs) {
  try {
    const result = await prisma.channel.create(payload)
    await createStreamChannel(result.type, result.id, {
      created_by_id: result.created_by_id,
      name: result.name,
      team: result.membership_id,
    })
    return result as IChannel
  } catch (err) {
    if (err.code === 'P2002') {
      throw new SpaceDuplicateException(payload.data.name)
    }
    // Handle or rethrow other exceptions
    throw err
  }
}

export async function findChannels(payload: Prisma.ChannelFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }

  const result = await prisma.channel.findMany(args)
  const total = await prisma.channel.count({ where })
  return { docs: result as IChannel[], total }
}

export async function findChannelById(id: string) {
  const result = await prisma.channel.findUnique({
    where: {
      id,
    },
  })
  return result as IChannel
}

export async function findChannel(id: string, membership_id: string) {
  const result = await prisma.channel.findUnique({
    where: {
      id,
      membership_id,
    },
  })
  return result as IChannel
}

export async function updateChannel(payload: Prisma.ChannelUpdateArgs) {
  mapNullToJsonNull(jsonFields, payload.data)
  const result = await prisma.channel.update(payload)
  return result as IChannel
}

export async function updateChannels(payload: Prisma.ChannelUpdateManyArgs) {
  mapNullToJsonNull(jsonFields, payload.data)
  const result = await prisma.channel.updateMany(payload)
  return result
}

export async function bulkWriteChannels(payload: {
  inserts?: Channel[]
  updates?: { where: { id: string }; data: Partial<Channel> }[]
  deletes?: string[]
}) {
  const { updates } = payload
  return prisma.$transaction(updates.map((item) => prisma.channel.update(item)))
}

export async function deleteChannel(id: string) {
  await prisma.$executeRaw`DELETE FROM feeds WHERE channel_id = ${id}`

  const result = await prisma.channel.delete({
    where: {
      id,
    },
  })
  deleteStreamChannel(`${CHANNEL_TYPE_ENUM.team}:${id}`)
  return result as IChannel
}
