import { Prisma } from '@prisma/client'

import prisma from './prisma'
import { createStreamMessage, deleteStreamMessage } from '@/shared-libs/stream-chat'
import { CHANNEL_TYPE_ENUM } from '@/shared-types/enum'
import { ISparkResponse } from '@/shared-types/interfaces'

const createSparkStreamMessage = async (data: ISparkResponse, channelId: string) => {
  const messageData = {
    text: data.content,
  }

  // NOTE: We match GetStream ids with spark response ids
  const messageId = data.id
  const res = await createStreamMessage(CHANNEL_TYPE_ENUM.team, channelId, {
    ...messageData,
    id: messageId,
    user_id: data.user_id,
    m_question_id: data.m_question_id,
  })
  console.log(res)
}

export async function createSparkResponse(payload: Prisma.SparkResponseCreateArgs, channelId: string) {
  const result = await prisma.sparkResponse.create(payload)
  if (result?.id) {
    await createSparkStreamMessage(result, channelId)
  }
  return result as ISparkResponse
}

export async function findSparkResponseById(id: string) {
  const result = await prisma.sparkResponse.findUnique({
    where: {
      id,
    },
  })
  return result as ISparkResponse
}

export async function findSparkResponses(payload: Prisma.SparkResponseFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }
  const result = await prisma.sparkResponse.findMany(args)
  const total = await prisma.sparkResponse.count({ where })
  return { docs: result, total }
}

export async function createSparkMembershipQuestionInstanceResponse(payload: any, channelId: string) {
  const result = await prisma.sparkMembershipQuestionInstanceResponse.create(payload)
  if (result?.id) {
    await createSparkStreamMessage(result, channelId)
  }
  return result as ISparkResponse
}

export async function getSparkCurrentStreak(userId: string, membershipId: string) {
  const currentDate = new Date()
  const oneDayAgo = new Date(currentDate.getTime() - 48 * 60 * 60 * 1000)
  const response = await findFirstSparkResponse({
    where: {
      user_id: userId,
      membership_id: membershipId,
      createdAt: {
        gte: oneDayAgo, // less than or equal to 24 hours ago
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
    select: {
      streak_count: true,
      createdAt: true,
    },
  })
  if (response) {
    return response.streak_count
  }
  return 0
}

export async function findFirstSparkResponse(args: Prisma.SparkResponseFindManyArgs) {
  const result = await prisma.sparkMembershipQuestionInstanceResponse.findFirst(args)
  return result
}

export async function deleteSparkResponseById(id: string) {
  deleteStreamMessage(id)
  const result = await prisma.sparkMembershipQuestionInstanceResponse.delete({
    where: {
      id,
    },
  })
  return result as ISparkResponse
}
