import { Prisma } from '@prisma/client'

import prisma from './prisma'
import { ISparkCategory } from '@/shared-types/interfaces'

export async function createSparkCategory(payload: Prisma.SparkCategoryCreateArgs) {
  const result = await prisma.sparkCategory.create(payload)
  return result as ISparkCategory
}

export async function findSparkCategoryById(payload: Prisma.SparkCategoryFindUniqueArgs) {
  const result = await prisma.sparkCategory.findUnique(payload)
  return result as ISparkCategory
}

export async function findSparkCategories(payload: Prisma.SparkCategoryFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }

  const result = await prisma.sparkCategory.findMany({
    where,
    ...rest,
  })
  const total = await prisma.sparkCategory.count({ where })
  return { docs: result as ISparkCategory[], total }
}
