import { Prisma } from '@prisma/client'

import prisma from './prisma'
import { IEventAttendee } from '@/shared-types/interfaces'

export async function createEventAttendee(payload: Prisma.EventAttendeeCreateArgs) {
  const result = await prisma.eventAttendee.create(payload)
  return result as IEventAttendee
}

export async function findEventAttendeeById(payload: Prisma.EventAttendeeFindUniqueArgs) {
  const result = await prisma.eventAttendee.findUnique(payload)
  return result as IEventAttendee
}

export async function findEventAttendee(payload: Prisma.EventAttendeeFindFirstArgs) {
  const result = await prisma.eventAttendee.findFirst(payload)
  return result as IEventAttendee
}

export async function findEventAttendees(payload: Prisma.EventAttendeeFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }

  const result = await prisma.eventAttendee.findMany(args)
  const total = await prisma.eventAttendee.count({ where })
  return { docs: result as IEventAttendee[], total }
}

export async function updateEventAttendee(payload: Prisma.EventAttendeeUpdateArgs) {
  const result = await prisma.eventAttendee.update(payload)
  return result as IEventAttendee
}

export async function updateEventAttendees(payload: Prisma.EventAttendeeUpdateManyArgs) {
  const result = await prisma.eventAttendee.updateMany(payload)
  return result
}

export async function deleteEventAttendeeById(id: string) {
  const result = await prisma.eventAttendee.delete({
    where: {
      id,
    },
  })
  return result as IEventAttendee
}
