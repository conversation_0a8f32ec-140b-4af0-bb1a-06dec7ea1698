import { Prisma } from '@prisma/client'
import { ownerProfilePublicFields, ownerPublicFields } from 'memberup/lib/query-options/communities'

import prisma from './prisma'
import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { updateAlgoliaMembersIndexForUserId, uploadDataToAlgoliaIndex } from '@/shared-libs/algolia'
import { IUser } from '@/shared-types/interfaces'

export async function findUserById(payload: Prisma.UserFindUniqueArgs) {
  const result = await prisma.user.findUnique(payload)
  return result as IUser
}

export async function findUser(payload: Prisma.UserFindFirstArgs) {
  const result = await prisma.user.findFirst(payload)
  return result as IUser
}

export async function findUsers(payload: Prisma.UserFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }

  const result = await prisma.user.findMany({
    ...args,
    ...rest,
  })
  const total = await prisma.user.count({ where })
  return { docs: result as IUser[], total }
}

export async function updateUser(payload: Prisma.UserUpdateArgs) {
  const result = await prisma.user.update(payload)
  if (result?.id) {
    await updateAlgoliaMembersIndexForUserId(result.id)
  }
  return result as IUser
}

export async function deleteUserById(id: string) {
  const result = await prisma.user.delete({
    where: {
      id,
    },
  })
  return result as IUser
}
