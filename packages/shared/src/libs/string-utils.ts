// @ts-ignore
import he from 'he'

export const pluralize = (count: number, noun: string, plural: string): string =>
  `${count} ${count !== 1 ? plural : noun}`

export const getLikesStr = (ele: any, count: number) => {
  if (!count) return null
  return `${count} ${count > 1 ? 'likes' : 'like'}`

  // while (i < 3 && i < likes.length) {
  //   const name = (likes[i].user as IActor)?.data?.name
  //   if (name) {
  //     if (names) names += ', '
  //     names += name
  //   }
  //   i++
  // }
  // return `Liked by ${names}`
  // if (i > 0 && likes.length === i) {
  //   return `Liked by ${names}`
  // } else {
  //   return `${likeCount} ${likeCount > 1 ? 'likes' : 'like'}`
  // }
}

export const stripHtml = (htmlString: string): string => {
  if (htmlString === null) return ''
  let strippedString = htmlString.replace(/<[^>]*>/g, '') // Remove HTML tags
  strippedString = he.decode(strippedString) // Decode HTML entities
  return strippedString
}

export const getCommentsStr = (ele: any, count: number) => {
  if (!count) return null
  return `${count} ${count > 1 ? 'comments' : 'comment'}`
}

export const insertStr = (mainStr: string, insStr: string, start?: number, end?: number) => {
  let startContent = mainStr
  let endContent = ''
  if (start >= 0) {
    startContent = mainStr.slice(0, start)
  }
  if (end >= 0) {
    endContent = mainStr.slice(end)
  }
  return startContent + insStr + endContent
}

export const getFirstLastName = (name: string): string[] => {
  const temp = (name || '').trim().split(' ')
  const firstName = temp.shift()
  return [firstName, temp.join(' ')]
}

// Get shorten user name(e.g. Jayden Jin -> Jayden J.)
export const getShortenName = (name: string): string => {
  const firstLastNames = getFirstLastName(name)
  const firstName = getCapitalized(firstLastNames[0])
  const lastName = firstLastNames[1].length > 0 ? `${getCapitalized(firstLastNames[1].charAt(0))}.` : ''
  return `${firstName} ${lastName}`
}

export const getRandomStr = (length) => {
  let result = ''
  const characters = 'abcdefghijklmnopqrstuvwxyz0123456789'
  const charactersLength = characters.length
  let counter = 0
  while (counter < length) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
    counter += 1
  }
  return result
}

export const validateEmail = (email: string): boolean => {
  if (!email) {
    return false
  }
  const regexExp =
    /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/gi
  return regexExp.test(email)
}

export const getCapitalized = (value: string): string => {
  if (!value) return ''
  return value.charAt(0).toUpperCase() + value.slice(1)
}

export const slugify = (text: string) => {
  if (!text) return null
  return text
    .toString()
    .toLowerCase()
    .replace(/\s+/g, '-') // Replace spaces with -
    .replace(/[^\w-]+/g, '') // Remove all non-word chars
    .replace(/--+/g, '-') // Replace multiple - with single -
    .replace(/^-+/, '') // Trim - from start of text
    .replace(/-+$/, '') // Trim - from end of text
}

export const generateNextSlug = (permalink: string, baseSlug: string) => {
  let currentSeq = 0
  if (permalink) {
    const parts = permalink.split('-')
    currentSeq = parseInt(parts[parts.length - 1])
    if (isNaN(currentSeq)) {
      currentSeq = 0
    }
  }
  const nextSeq = currentSeq + 1
  return `${baseSlug}-${nextSeq}`
}

export const cropString = (str, maxLength) => {
  if (str?.length > maxLength) {
    return str.substring(0, maxLength) + '...'
  }
  return str
}

export const shortenFileName = (name: string, maxLength: number) => {
  const extension = name?.split('.').pop()
  const baseName = name?.replace(`.${extension}`, '')
  if (baseName?.length <= maxLength) return name
  return `${baseName?.substring(0, maxLength)}...${extension}`
}

export const capitalizeAllWords = (str: string) => {
  if (!str) return ''
  return str.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase())
}
