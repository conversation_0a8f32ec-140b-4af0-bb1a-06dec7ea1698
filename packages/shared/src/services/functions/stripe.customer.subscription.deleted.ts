import { inngest } from '../inngest'
import { updateMembershipSettings } from '@/shared-libs/prisma/membership-settings'
import { updateUserProfiles } from '@/shared-libs/prisma/user-profile'

export default inngest.createFunction(
  {
    id: 'customer-subscription-deleted',
    name: 'Customer Subscription Deleted',
  },
  { event: 'stripe/customer.subscription.deleted' },
  async ({ event, step }) => {
    const { data } = event

    await step.run('subscription-deleted', async () => {
      // handle a subscription cancelled by your request from above.
      if (data['isMembershipStripe']) {
        await updateUserProfiles({
          where: {
            stripe_customer_id: data['customer'],
          },
          data: {
            stripe_subscription_status: data['status'],
            stripe_subscription_id: null,
          },
        })
      } else {
        await updateMembershipSettings({
          where: {
            stripe_customer_id: data['customer'],
          },
          data: {
            stripe_subscription_status: data['status'],
            stripe_subscription_id: null,
          },
        })
      }
    })
  },
)
