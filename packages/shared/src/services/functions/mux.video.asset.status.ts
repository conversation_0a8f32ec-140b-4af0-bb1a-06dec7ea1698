import { inngest } from '../inngest'
import { findFeeds, updateFeed } from '@/shared-libs/prisma/feed'
import prisma from '@/shared-libs/prisma/prisma'

export default inngest.createFunction(
  { id: 'mux-video-asset-status', name: 'Mux Video Asset Status' },
  { event: 'mux/video.asset.status' },
  async ({ event, step }) => {
    const { data } = event
    const eventType = data['type']
    const eventData = data['data']
    await step.run('update-library-video-asset-status', async () => {
      const isFeedVideo = eventData.passthrough?.indexOf('feed') >= 0
      if (isFeedVideo) {
        const result = isFeedVideo
          ? await findFeeds({
              where: {
                attachments: {
                  path: '$[*].passthrough',
                  array_contains: eventData.passthrough,
                },
              },
            })
          : { total: 0, docs: [] }
        let updatePayload
        if (result.total) {
          const doc: any = result.docs[0]
          switch (eventType) {
            case 'video.asset.created':
            case 'video.asset.ready':
              // This means an Asset was successfully created! We'll get
              // the existing item from the DB first, then update it with the
              // new Asset details
              // Just in case the events got here out of order, make sure the
              // asset isn't already set to ready before blindly updating it!
              updatePayload = {
                mux_asset: {
                  id: eventData.id,
                  status: eventData.status,
                  duration: eventData.duration,
                  aspect_ratio: eventData.aspect_ratio,
                  playback_ids: eventData.playback_ids,
                  passthrough: eventData.passthrough,
                  tracks: eventData.tracks,
                },
              }

              const tempIndex = doc.attachments.findIndex(
                (a) => a.passthrough === eventData.passthrough
              )
              await updateFeed({
                where: { id: doc.id },
                data: {
                  attachments: doc.attachments
                    .slice(0, tempIndex)
                    .concat(
                      { ...doc.attachments[tempIndex], ...updatePayload },
                      doc.attachments.slice(tempIndex + 1)
                    ),
                },
              })
          }
        }
      } else if (eventType === 'video.asset.static_renditions.ready') {
        if (eventData.passthrough?.startsWith('library')) {
          const lessonId = eventData.passthrough.split(':')[1]
          const lessonData = await prisma.contentLibraryCourseLesson.findUnique({
            where: {
              id: lessonId,
            },
            select: {
              media_file: true,
            },
          })
          // NOTE: We just update if the upload matches the latest saved data.
          if (lessonData.media_file['id'] === eventData.id) {
            const result = await prisma.contentLibraryCourseLesson.update({
              where: { id: lessonId },
              data: {
                media_file: eventData,
              },
            })
          }
        }
      }
    })
  }
)
