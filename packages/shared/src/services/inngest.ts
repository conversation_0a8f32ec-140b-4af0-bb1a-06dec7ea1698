import { Inngest } from 'inngest'
import { Stripe } from 'stripe'

import { slugify } from '@/shared-libs/string-utils'

interface InngestEventBase {
  data: Stripe.Event.Data.Object
}

interface StripeCustomerDeleted extends InngestEventBase {
  name: 'stripe/customer.deleted'
}

interface StripeCustomerSubscriptionUpdated extends InngestEventBase {
  name: 'stripe/customer.subscription.updated'
}

interface StripeCustomerSubscriptionDeleted extends InngestEventBase {
  name: 'stripe/customer.subscription.deleted'
}

interface StripePaymentMethodAttached extends InngestEventBase {
  name: 'stripe/payment_method.attached'
}

interface StripePaymentIntentSucceeded extends InngestEventBase {
  name: 'stripe/payment_intent.succeeded'
}

interface StripeInvoicePaid extends InngestEventBase {
  name: 'stripe/invoice.paid'
}

interface MuxVideoAssetStatus extends InngestEventBase {
  name: 'mux/video.asset.status'
}

interface MemberupFeedDeleted extends InngestEventBase {
  name: 'memberup/feed.deleted'
}

type Events = {
  'stripe/customer.deleted': StripeCustomerDeleted
  'stripe/customer.subscription.updated': StripeCustomerSubscriptionUpdated
  'stripe/customer.subscription.deleted': StripeCustomerSubscriptionDeleted
  'stripe/payment_method.attached': StripePaymentMethodAttached
  'stripe/payment_intent.succeeded': StripePaymentIntentSucceeded
  'stripe/invoice.paid': StripeInvoicePaid
  'mux/video.asset.status': MuxVideoAssetStatus
  'memberup/feed.deleted': MemberupFeedDeleted
}

export const inngest = new Inngest({ id: slugify('Memberup Inngest Functions') })
