import { baseApi } from './base.api'
import {
  ISparkMembershipQuestion,
  ISparkQuestion,
  ISparkQuestionSettings,
  ISparkResponse,
} from '@/shared-types/interfaces'
import { TGetApiParams } from '@/shared-types/types'

export const getSparkCategoriesApi = ({ where, take, skip }: TGetApiParams, membershipId: string) => {
  return baseApi().get(`/api/spark/m-category`, {
    params: { where, take, skip, membership_id: membershipId },
  })
}

export const getSparkMembershipCategoriesApi = (membershipId: string) => {
  return baseApi().get(`/api/spark/membership-category?membership_id=${membershipId}`)
}

export const deleteSparkMembershipQuestionApi = (id: string, membershipId) => {
  return baseApi().delete(`/api/spark/membership-question/${id}?membership_id=${membershipId}`)
}

export const createSparkMembershipQuestionApi = (
  payload: Partial<ISparkMembershipQuestion & { spark_membership_category_id?: string }>,
  membershipId: string,
) => {
  return baseApi().post(`/api/spark/membership-question?membership_id=${membershipId}`, payload)
}

export const setSelectedSparkMembershipCategoryApi = (id: string, membershipId: string) => {
  return baseApi().post(`/api/spark/set-selected-category?membership_id=${membershipId}`, id)
}

export const updateSparkMembershipQuestionApi = (
  id: string,
  payload: Partial<ISparkMembershipQuestion>,
  membershipId: string,
) => {
  return baseApi().put(`/api/spark/membership-question/${id}?membership_id=${membershipId}`, payload)
}

export const getSparkMembershipQuestionsApi = (membershipId: string, membershipCategoryId: string) => {
  return baseApi().get(`/api/spark/membership-question`, {
    params: {
      membership_id: membershipId,
      spark_membership_category_id: membershipCategoryId,
    },
  })
}

export const getSparkCategoryApi = (id: string) => {
  return baseApi().get(`/api/spark/m-category/${id}`)
}

export const enableSparkApi = (enable: boolean, membershipId: string) => {
  return baseApi().post(`/api/spark/enable?membership_id=${membershipId}`, {
    enable,
  })
}

export const startSparkCategoryApi = (category_id: string, m_category_id: string, membershipId: string) => {
  return baseApi().post(`/api/spark/m-category/start?membership_id=${membershipId}`, {
    category_id,
    m_category_id,
  })
}

export const stopSparkCategoryApi = (m_category_id: string, membershipId: string) => {
  return baseApi().post(`/api/spark/m-category/stop?membership_id=${membershipId}`, {
    m_category_id,
  })
}

export const getSparkQuestionsApi = (categoryId: string, questionType: string, membershipId: string) => {
  return baseApi().get(`/api/spark/m-question`, {
    params: { category_id: categoryId, question_type: questionType, membership_id: membershipId },
  })
}

export const getCurrentSparkMembershipQuestionInstanceApi = (membership_id: string) => {
  return baseApi().get(`/api/spark/membership-question-instance/current?membership_id=${membership_id}`)
}

export const upsertActiveSparkQuestionApi = (question_id: string, active: boolean) => {
  return baseApi().put(`/api/spark/m-question/active`, { question_id, active })
}

export const createSparkQuestionApi = (payload: Partial<ISparkQuestion & { category_id?: string }>) => {
  return baseApi().post(`/api/spark/m-question`, payload)
}

export const updateSparkQuestionApi = (id: string, payload: Partial<ISparkMembershipQuestion>) => {
  return baseApi().put(`/api/spark/m-question/${id}`, payload)
}

export const updateSparkQuestionSettingsApi = (id: string, payload: Partial<ISparkQuestionSettings>) => {
  return baseApi().put(`/api/spark/question-settings/${id}`, payload)
}

export const createSparkQuestionSettingsApi = (payload: Partial<ISparkQuestionSettings>) => {
  return baseApi().post(`/api/spark/question-settings`, payload)
}

export const updateSparkQuestionSequenceApi = (
  category_id: string,
  question_id: string,
  old_sequence: number,
  new_sequence: number,
  membershipId: string,
) => {
  return baseApi().post(`/api/spark/m-question/sequence?membership_id=${membershipId}`, {
    category_id,
    question_id,
    old_sequence,
    new_sequence,
  })
}

export const deleteSparkQuestionApi = (id: string) => {
  return baseApi().delete(`/api/spark/m-question/${id}`)
}

export const createSparkResponseApi = (payload: { question_id: string; m_question_id: string; content: string }) => {
  return baseApi().post(`/api/spark/response`, payload)
}

export const createSparkMembershipQuestionInstanceResponseApi = (
  membershipId: string,
  payload: {
    question_id: string
    spark_membership_question_instance_id: string
    content: string
  },
) => {
  return baseApi().post(`/api/spark/membership-question-instance-response?membership_id=${membershipId}`, payload)
}

export const getSparkResponsesApi = ({ where, take, skip }: TGetApiParams) => {
  return baseApi().get(`/api/spark/response`, { params: { where, take, skip } })
}

export const getSparkResponseApi = (id: string) => {
  return baseApi().get(`/api/spark/response/${id}`)
}
