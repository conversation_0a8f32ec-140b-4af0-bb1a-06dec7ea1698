import { baseApi } from './base.api'
import { IMembershipInput } from '@/shared-types/interfaces'

export const createMembershipApi = () => {
  return baseApi().post(`/api/membership`, {})
}

export const payForMembershipApi = (data) => {
  return baseApi().post(`/api/membership/pay`, data)
}

export const joinMembershipApi = (membership_id: string, formData?: any, inviteToken?: string) => {
  return baseApi().post(`/api/membership/join`, {
    membership_id: membership_id,
    form_data: formData,
    invite_token: inviteToken,
  })
}

export const banMemberApi = (userId: string, membershipId: string) => {
  return baseApi().post(`/api/membership/ban-member`, { user_id: userId, membership_id: membershipId })
}

export const removeMemberApi = (userId: string, membershipId: string) => {
  return baseApi().post(`/api/membership/remove-member`, { user_id: userId, membership_id: membershipId })
}

export const cancelMembershipRequestApi = (membership_id: string, formData?: any) => {
  return baseApi().post(`/api/membership/cancel-membership-request`, {
    membership_id: membership_id,
  })
}

export const getMembershipStatsApi = async (id: string) => {
  const res = await fetch(`/api/membership/stats/${id}`)
  const data = await res.json()
  return data
}

export const completeMembershipApi = (data: { id: string }) => {
  return baseApi().post(`/api/membership/complete`, data)
}

export const getUserMembershipApi = (membershipId: string) => {
  return baseApi().get(`/api/membership/user-membership?membership_id=${membershipId}`)
}

export const getMemberRequestsApi = (membershipId: string) => {
  return baseApi().get(`/api/membership/member-request?membership_id=${membershipId}`)
}

export const approveMemberRequestApi = (id: string) => {
  return baseApi().post(`/api/membership/member-request/${id}/approve`, {})
}

export const approveAllMembersRequestsApi = (membershipId: string) => {
  return baseApi().post(`/api/membership/member-request/approve-all?membership_id=${membershipId}`, {})
}

export const rejectMemberRequestApi = (id: string) => {
  return baseApi().post(`/api/membership/member-request/${id}/reject`, {})
}

export const enableMembershipQuestionsApi = (membershipId: string, enabled: boolean) => {
  return baseApi().post(`/api/membership/membership-questions/enable`, {
    membership_id: membershipId,
    enabled: enabled,
  })
}

export const deleteMembershipApi = (id) => {
  return baseApi().delete(`/api/membership`, { params: { id } })
}

// TODO 3023: pass filters, category and search query.
export const getMembershipListApi = (params: {}) => {
  return baseApi().get(`/api/membership/list`, { params })
}

// TODO 3023: Change the path to /api/membership/{id} and remove the slug parameter
export const getMembershipApi = (params: { slug: string; id?: string; host?: string; stripe_code?: string }) => {
  return baseApi().get(`/api/membership`, { params })
}

export const updateMembershipApi = (
  data: {
    about_gallery?: any[]
    about_text?: string
    about_title?: string
    form_enabled?: boolean
    form?: any
    name?: string
    slug?: string
    token?: string
    description?: string
    support_email?: string
    visibility?: string
    intro_html?: string
    cover_image?: string
    cover_image_crop_area?: any
    external_links?: any
  },
  membershipId: string,
) => {
  return baseApi().put(`/api/membership/${membershipId}`, data)
}

export const checkExistsMembershipApi = (params: { slug: string }) => {
  return baseApi().get(`/api/membership/check-exists`, { params })
}

export const deactivateMembershipApi = (data: { deactive_reason: string; password: string }, membershipId: string) => {
  return baseApi().post(`/api/membership/deactivate?membership_id=${membershipId}`, data)
}
