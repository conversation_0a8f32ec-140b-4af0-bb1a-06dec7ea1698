import { baseApi } from './base.api'
import { RECURRING_INTERVAL_ENUM, STRIPE_BALANCE_TYPE_ENUM } from '@/shared-types/enum'

export const getPaymentHistoryApi = (params?: { page?: number; pageSize?: number; membershipId?: string }) => {
  return baseApi().get('/api/payment-history', { params })
}

export const getStripeConnectedAccountApi = (isMembership: boolean) => {
  return baseApi().get(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/account`)
}

export const getStripeAccountTokenApi = (
  isMembership: boolean,
  data: { code: string; membership_slug: string; custom_host?: string },
) => {
  return baseApi().post(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/account/token`, data)
}

export const putConfirmMembershipSetupStripeAccountApi = (membershipId: string) => {
  return baseApi().put(`/api/stripe/membership/account/setup?membership_id=${membershipId}`, {})
}

export const disconnectStripeAccountApi = (isMembership: boolean) => {
  return baseApi().post(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/account/disconnect`, {})
}

export const getStripeCustomerApi = (isMembership: boolean) => {
  return baseApi().get(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/customer`)
}

export const postStripeAccountApi = (membershipId: string) => {
  return baseApi().post(`/api/stripe/membership/account?membership_id=${membershipId}`, {})
}

export const getStripeAccountApi = (membershipId: string) => {
  return baseApi().get(`/api/stripe/membership/account?membership_id=${membershipId}`)
}

export const getStripeConnectLinkApi = (membershipId: string) => {
  return baseApi().get(`/api/stripe/membership/account/connect-link?membership_id=${membershipId}`, {})
}

export const getStripeSubscriptionsApi = async (
  isMembership: boolean,
  membershipId: string,
  includeDefaultPaymentMethod?: boolean,
) => {
  return await baseApi().get(
    `/api/stripe/${isMembership ? 'membership' : 'memberup'}/subscription?membership_id=${membershipId}${includeDefaultPaymentMethod && '&include_default_payment_method=true'}`,
  )
}

export const getStripeBalanceTransactionsApi = (
  isMembership: boolean,
  params: {
    accountId?: string
    type?: STRIPE_BALANCE_TYPE_ENUM
    limit?: number
    created_gte?: number
    created_lt?: number
    starting_after?: string
  },
) => {
  return baseApi().get(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/balance-transactions`, {
    params,
  })
}

export const getStripePayoutsApi = (
  isMembership: boolean,
  params: {
    accountId?: string
    type?: STRIPE_BALANCE_TYPE_ENUM
    limit?: number
    created_gte?: number
    created_lt?: number
    starting_after?: string
  },
) => {
  return baseApi().get(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/payouts`, {
    params,
  })
}

export const createStripeDiscountApi = (
  isMembership: boolean,
  data: {
    code: string
    amount_off?: number
    percent_off?: number
    duration?: 'forever' | 'once' | 'repeating'
    duration_in_months?: number
    max_redemptions?: number
    name?: string
  },
) => {
  return baseApi().post(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/discount`, data)
}

export const deleteStripeDiscountApi = (isMembership: boolean, coupon_id: string) => {
  return baseApi().delete(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/discount`, {
    coupon_id,
  })
}

export const getStripeGetCouponListApi = (isMembership: boolean) => {
  return baseApi().get(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/discount/coupon`)
}

export const createStripeCouponApi = (
  isMembership: boolean,
  data: {
    amountOff: number
    percentOff: number
    name: string
    code: string
    maxRedemptions: number
  },
) => {
  return baseApi().post(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/discount/coupon`, data)
}

export const createStripePromotionCodeApi = (
  isMembership: boolean,
  data: {
    code: string
    amount_off?: number
    percent_off?: number
    name?: string
    duration?: 'forever' | 'once' | 'repeating'
    duration_in_months?: number
    max_redemptions?: number
    active?: boolean
  },
) => {
  return baseApi().post(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/discount/promotion-code/create`, data)
}

export const getStripeGetCouponApi = (isMembership: boolean, couponId: string) => {
  return baseApi().get(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/discount/coupon/${couponId}`)
}

export const getStripePromotionCodeApi = (isMembership: boolean, code: string) => {
  return baseApi().get(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/discount/promotion-code/${code}`)
}

export const updateStripeCouponApi = (
  isMembership: boolean,
  data: {
    couponId: string
    promoId: string
    name: string
    active: boolean
  },
) => {
  return baseApi().put(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/discount/coupon`, data)
}

export const getStripeInvoicesApi = (
  isMembership: boolean,
  data?: {
    limit?: number
    created_gte?: number
    created_lt?: number
    starting_after?: string
    ending_before?: string
    membership_slug?: string
  },
) => {
  return baseApi().post(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/invoice`, data)
}

export const getStripeInvoiceByIdApi = (id: string, expandSubscription?: boolean) => {
  const params = expandSubscription ? { expand_subscription: 'true' } : {}
  return baseApi().get(`/api/stripe/memberup/invoice/id/${id}`, { params })
}

export const getStripeMembershipInvoiceByIdApi = (id: string, membershipSlug: string, expandSubscription?: boolean) => {
  const params = { membership_slug: membershipSlug }
  if (expandSubscription) {
    params['expand_subscription'] = 'true'
  }
  return baseApi().get(`/api/stripe/membership/invoice/id/${id}`, { params })
}

export const getStripePromotionCodesApi = (
  isMembership: boolean,
  params: {
    active?: boolean
    code?: string
    limit?: number
    starting_after?: string
  },
) => {
  return baseApi().get(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/discount/promotion-code`, { params })
}

export const getStripeCurrentPaymentMethodApi = (isMembership: boolean) => {
  return baseApi().get(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/payment-method/current-payment-method`)
}

export const setStripeDefaultPaymentMethodApi = (isMembership: boolean, paymentMethodId: string) => {
  return baseApi().post(
    `/api/stripe/${isMembership ? 'membership' : 'memberup'}/payment-method/${paymentMethodId}/make-default`,
    {},
  )
}

export const getStripeMembershipPaymentMethodsApi = (
  membershipId: string,
  params: {
    type: string
    limit?: number
    starting_after?: string
  },
) => {
  return baseApi().get(`/api/stripe/membership/payment-method`, {
    membershipId,
    params,
  })
}

export const getStripePaymentMethodsApi = (
  isMembership: boolean,
  params: {
    type: string
    limit?: number
    starting_after?: string
  },
) => {
  return baseApi().get(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/payment-method`, {
    params,
  })
}

export const attachStripePaymentMethodApi = (isMembership: boolean, stripe_payment_method_id: string) => {
  return baseApi().post(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/payment-method`, {
    stripe_payment_method_id,
  })
}

export const detachStripePaymentMethodApi = (isMembership: boolean, paymentMethodId: string) => {
  return baseApi().post(
    `/api/stripe/${isMembership ? 'membership' : 'memberup'}/payment-method/${paymentMethodId}/detach`,
    {},
  )
}

export const updateStripePaymentMethodApi = (
  isMembership: boolean,
  data: {
    billing_details?: {
      address?: string
      email?: string
      name?: string
      phone?: string
    }
    card?: { exp_month?: number; exp_year?: number }
  },
) => {
  return baseApi().put(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/payment-method`, data)
}

export const getStripePricesApi = (isMembership: boolean, membershipId: string) => {
  return baseApi().get(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/prices?membership_id=${membershipId}`)
}

export const setStripePriceAsDefaultApi = (isMembership: boolean, membershipId: string, priceId: string) => {
  if (!membershipId || !priceId) {
    throw new Error('Membership ID and Price ID are required.')
  }

  return baseApi().post(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/prices/${priceId}/set-as-default`, {
    membership_id: membershipId,
    price_id: priceId,
  })
}

export const createStripePriceApi = (isMembership: boolean, membershipId: string, data: any) => {
  if (!membershipId || !data) {
    throw new Error('Membership ID and Price data are required.')
  }
  return baseApi().post(
    `/api/stripe/${isMembership ? 'membership' : 'memberup'}/prices?membership_id=${membershipId}`,
    data,
  )
}

export const deleteStripePriceApi = (isMembership: boolean, membershipId: string, priceId: string) => {
  return baseApi().delete(
    `/api/stripe/${isMembership ? 'membership' : 'memberup'}/prices/${priceId}/?membership_id=${membershipId}`,
  )
}

export const updateStripePriceApi = (isMembership: boolean, membershipId: string, priceId: string, data: any) => {
  if (!membershipId || !priceId || !data) {
    throw new Error('Membership ID, Price ID, and Price data are required.')
  }
  return baseApi().put(
    `/api/stripe/${isMembership ? 'membership' : 'memberup'}/prices/${priceId}/?membership_id=${membershipId}`,
    data,
  )
}

export const getStripeUpcomingInvoiceApi = (
  isMembership: boolean,
  membershipId: string,
  data?: {
    plan?: string
    plan_price?: number
    interval?: RECURRING_INTERVAL_ENUM
    promotion_code?: string
    membership_slug?: string
    preview?: boolean
  },
) => {
  return baseApi().post(
    `/api/stripe/${isMembership ? 'membership' : 'memberup'}/invoice/upcoming-invoice?membership_id=${membershipId}`,
    data || {},
  )
}

export const createStripeMemberUpSubscriptionApi = (data: {
  membership_id?: string
  interval?: RECURRING_INTERVAL_ENUM
  default_payment_method?: string
  promotion_code?: string
  plan_price?: number
  plan_key?: string
}) => {
  return baseApi().post(`/api/stripe/memberup/subscription`, data)
}

export const createStripeMembershipSubscriptionApi = (
  data: {
    interval?: RECURRING_INTERVAL_ENUM
    stripe_enable_annual?: boolean
    collection_method?: string
    default_payment_method?: string
    selected_price_id: any
  },
  membershipId?: string,
) => {
  return baseApi().post(`/api/stripe/membership/subscription?membership_id=${membershipId}`, data)
}

export const updateStripeSubscriptionApi = (
  isMembership: boolean,
  data: {
    plan?: string
    plan_price?: number
    interval?: RECURRING_INTERVAL_ENUM
    proration_date?: number
    promotion_code?: string
    membership_slug?: string
    plan_remaining_days?: number
  },
) => {
  return baseApi().post(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/subscription/update`, data)
}

export const createStripeCheckoutSessionApi = (isMembership: boolean, data) => {
  return baseApi().post(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/checkout-session`, data)
}

export const createStripePaymentIntentApi = (isMembership: boolean, data, membership_id: string) => {
  return baseApi().post(
    `/api/stripe/${isMembership ? 'membership' : 'memberup'}/payment-intent?membership_id=${membership_id}`,
    data,
  )
}

export const createStripeInvoiceAndPayApi = (data, membership_id: string) => {
  return baseApi().post(`/api/stripe/membership/invoice/create-and-pay?membership_id=${membership_id}`, data)
}

export const createStripeMembershipSetupIntentApi = (membershipId: string) => {
  return baseApi().post(`/api/stripe/membership/setup-intent?membership_id=${membershipId}`, {})
}

export const createStripeSetupIntentApi = (isMembership: boolean, data) => {
  return baseApi().post(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/setup-intent`, data)
}

export const getStripeReportApi = (
  isMembership: boolean,
  params: {
    report_type: string // revenue_recognition.debit_credit_summary.14
    interval_start: number // 1619827200
    interval_end: number
  },
) => {
  return baseApi().get(`/api/stripe/${isMembership ? 'membership' : 'memberup'}/report`, {
    params,
  })
}
