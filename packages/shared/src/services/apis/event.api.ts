import { baseApi } from './base.api'
import { IEvent } from '@/shared-types/interfaces'
import { TGetApiParams } from '@/shared-types/types'

export const createEventApi = (membershipId: string, data: Partial<IEvent>) => {
  return baseApi().post(`/api/event`, { membership_id: membershipId, data })
}

export const updateEventApi = (membershipId: string, id: string, data: Partial<IEvent>) => {
  return baseApi().put(`/api/event/${id}`, { membership_id: membershipId, data })
}

export const getEventsApi = (membershipId: string, { where, take, skip, orderBy }: TGetApiParams) => {
  return baseApi().get(`/api/event`, {
    params: { membership_id: membershipId, where, take, skip, orderBy },
  })
}

export const getEventApi = (id: string) => {
  return baseApi().get(`/api/event/${id}`)
}

export const createEventAttendApi = (event_id: string) => {
  return baseApi().post(`/api/event/attend`, { event_id })
}

export const deleteEventAttendApi = (id: string) => {
  return baseApi().delete(`/api/event/attend/${id}`)
}

export const getEventWithAttendeeApi = (event_id: string) => {
  return baseApi().get(`/api/event/attendee`, { params: { event_id } })
}

export const deleteEventApi = (id: string, membershipId: string) => {
  return baseApi().delete(`/api/event/${id}`, { params: { membership_id: membershipId } })
}
